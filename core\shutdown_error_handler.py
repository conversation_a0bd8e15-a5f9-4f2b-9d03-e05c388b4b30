#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关闭错误处理机制
专门处理程序关闭过程中的各种异常情况
"""

import os
import sys
import time
import logging
import threading
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass
from enum import Enum

from .error_handler import ErrorHandler, ErrorSeverity, ErrorInfo

logger = logging.getLogger(__name__)

class ShutdownErrorType(Enum):
    """关闭错误类型"""
    TIMEOUT = "timeout"
    NETWORK_ERROR = "network_error"
    RESOURCE_OCCUPIED = "resource_occupied"
    THREAD_STUCK = "thread_stuck"
    CONFIG_SAVE_FAILED = "config_save_failed"
    CLEANUP_FAILED = "cleanup_failed"
    FORCE_SHUTDOWN_REQUIRED = "force_shutdown_required"

@dataclass
class ShutdownErrorContext:
    """关闭错误上下文"""
    phase: str  # 关闭阶段
    component: str  # 出错组件
    timeout_duration: float  # 超时时长
    retry_count: int = 0  # 重试次数
    force_shutdown_triggered: bool = False  # 是否触发强制关闭

class ShutdownErrorHandler:
    """关闭错误处理器"""
    
    def __init__(self, base_error_handler: ErrorHandler = None):
        self.base_error_handler = base_error_handler or ErrorHandler()
        self.shutdown_errors: List[ErrorInfo] = []
        self.force_shutdown_callbacks: List[Callable] = []
        
        # 超时配置
        self.timeout_limits = {
            "config_save": 0.5,      # 配置保存超时 500ms
            "resource_cleanup": 1.0,  # 资源清理超时 1s
            "thread_stop": 1.0,      # 线程停止超时 1s
            "ssh_disconnect": 0.5,   # SSH断开超时 500ms
            "total_shutdown": 3.0    # 总关闭超时 3s
        }
        
        # 重试配置
        self.max_retries = {
            "config_save": 1,
            "resource_cleanup": 2,
            "thread_stop": 1,
            "ssh_disconnect": 2
        }
        
        # 强制关闭标志
        self.force_shutdown_triggered = False
        self.shutdown_start_time = None
        
        logger.info("关闭错误处理器初始化完成")
    
    def handle_shutdown_error(self, 
                            error: Exception,
                            error_type: ShutdownErrorType,
                            context: ShutdownErrorContext) -> bool:
        """处理关闭错误
        
        Args:
            error: 异常对象
            error_type: 关闭错误类型
            context: 错误上下文
            
        Returns:
            bool: 是否应该继续关闭流程
        """
        # 记录错误
        error_info = self.base_error_handler.handle_error(
            error,
            context={
                "shutdown_phase": context.phase,
                "component": context.component,
                "timeout_duration": context.timeout_duration,
                "retry_count": context.retry_count,
                "error_type": error_type.value
            },
            severity=self._get_error_severity(error_type),
            auto_recover=False  # 关闭错误不使用自动恢复
        )
        
        self.shutdown_errors.append(error_info)
        
        # 根据错误类型决定处理策略
        return self._handle_specific_error(error_type, error, context)
    
    def _get_error_severity(self, error_type: ShutdownErrorType) -> ErrorSeverity:
        """获取错误严重程度"""
        severity_map = {
            ShutdownErrorType.TIMEOUT: ErrorSeverity.HIGH,
            ShutdownErrorType.NETWORK_ERROR: ErrorSeverity.MEDIUM,
            ShutdownErrorType.RESOURCE_OCCUPIED: ErrorSeverity.HIGH,
            ShutdownErrorType.THREAD_STUCK: ErrorSeverity.HIGH,
            ShutdownErrorType.CONFIG_SAVE_FAILED: ErrorSeverity.MEDIUM,
            ShutdownErrorType.CLEANUP_FAILED: ErrorSeverity.MEDIUM,
            ShutdownErrorType.FORCE_SHUTDOWN_REQUIRED: ErrorSeverity.CRITICAL
        }
        return severity_map.get(error_type, ErrorSeverity.MEDIUM)
    
    def _handle_specific_error(self, 
                             error_type: ShutdownErrorType, 
                             error: Exception,
                             context: ShutdownErrorContext) -> bool:
        """处理特定类型的关闭错误"""
        
        if error_type == ShutdownErrorType.TIMEOUT:
            return self._handle_timeout_error(error, context)
        
        elif error_type == ShutdownErrorType.NETWORK_ERROR:
            return self._handle_network_error(error, context)
        
        elif error_type == ShutdownErrorType.RESOURCE_OCCUPIED:
            return self._handle_resource_occupied_error(error, context)
        
        elif error_type == ShutdownErrorType.THREAD_STUCK:
            return self._handle_thread_stuck_error(error, context)
        
        elif error_type == ShutdownErrorType.CONFIG_SAVE_FAILED:
            return self._handle_config_save_error(error, context)
        
        elif error_type == ShutdownErrorType.CLEANUP_FAILED:
            return self._handle_cleanup_failed_error(error, context)
        
        elif error_type == ShutdownErrorType.FORCE_SHUTDOWN_REQUIRED:
            return self._handle_force_shutdown_required(error, context)
        
        else:
            logger.warning(f"未知的关闭错误类型: {error_type}")
            return True  # 继续关闭流程
    
    def _handle_timeout_error(self, error: Exception, context: ShutdownErrorContext) -> bool:
        """处理超时错误"""
        logger.warning(f"关闭超时: {context.component} 在 {context.phase} 阶段超时 "
                      f"({context.timeout_duration:.2f}s)")
        
        # 检查是否可以重试
        max_retry = self.max_retries.get(context.component, 0)
        if context.retry_count < max_retry:
            logger.info(f"尝试重试 {context.component} (第{context.retry_count + 1}次)")
            return True  # 允许重试
        
        # 超过重试次数，跳过该组件
        logger.warning(f"跳过超时组件: {context.component}")
        
        # 检查是否需要强制关闭
        if self._should_trigger_force_shutdown(context):
            self._trigger_force_shutdown("超时过多")
            return False
        
        return True  # 继续其他组件的关闭
    
    def _handle_network_error(self, error: Exception, context: ShutdownErrorContext) -> bool:
        """处理网络错误"""
        logger.warning(f"网络错误: {context.component} - {error}")
        
        # 网络错误通常是SSH连接问题，直接跳过
        if "ssh" in context.component.lower():
            logger.info("SSH网络错误，强制断开连接")
            return True  # 继续关闭流程
        
        return True
    
    def _handle_resource_occupied_error(self, error: Exception, context: ShutdownErrorContext) -> bool:
        """处理资源占用错误"""
        logger.warning(f"资源占用错误: {context.component} - {error}")
        
        # 尝试强制释放资源
        if context.retry_count == 0:
            logger.info(f"尝试强制释放资源: {context.component}")
            return True  # 允许重试
        
        # 重试失败，跳过该资源
        logger.warning(f"无法释放资源，跳过: {context.component}")
        return True
    
    def _handle_thread_stuck_error(self, error: Exception, context: ShutdownErrorContext) -> bool:
        """处理线程卡死错误"""
        logger.error(f"线程卡死: {context.component} - {error}")
        
        # 线程卡死是严重问题，直接强制终止
        logger.warning(f"强制终止卡死线程: {context.component}")
        
        # 如果多个线程卡死，触发强制关闭
        stuck_threads = sum(1 for e in self.shutdown_errors 
                          if "thread_stuck" in str(e.context))
        if stuck_threads >= 2:
            self._trigger_force_shutdown("多个线程卡死")
            return False
        
        return True
    
    def _handle_config_save_error(self, error: Exception, context: ShutdownErrorContext) -> bool:
        """处理配置保存错误"""
        logger.warning(f"配置保存失败: {error}")
        
        # 配置保存失败不是致命错误，跳过即可
        logger.info("跳过配置保存，继续关闭流程")
        return True
    
    def _handle_cleanup_failed_error(self, error: Exception, context: ShutdownErrorContext) -> bool:
        """处理清理失败错误"""
        logger.warning(f"清理失败: {context.component} - {error}")
        
        # 清理失败不阻止关闭，但记录警告
        logger.warning(f"资源清理失败，可能存在资源泄漏: {context.component}")
        return True
    
    def _handle_force_shutdown_required(self, error: Exception, context: ShutdownErrorContext) -> bool:
        """处理强制关闭需求"""
        logger.critical(f"需要强制关闭: {error}")
        self._trigger_force_shutdown("关键错误")
        return False
    
    def _should_trigger_force_shutdown(self, context: ShutdownErrorContext) -> bool:
        """判断是否应该触发强制关闭"""
        if self.force_shutdown_triggered:
            return False
        
        # 检查总关闭时间
        if self.shutdown_start_time:
            elapsed = time.time() - self.shutdown_start_time
            if elapsed > self.timeout_limits["total_shutdown"]:
                return True
        
        # 检查错误数量
        if len(self.shutdown_errors) >= 5:
            return True
        
        # 检查关键组件错误
        critical_components = ["thread_manager", "ssh_pool", "main_window"]
        if context.component in critical_components and context.retry_count >= 1:
            return True
        
        return False
    
    def _trigger_force_shutdown(self, reason: str):
        """触发强制关闭"""
        if self.force_shutdown_triggered:
            return
        
        self.force_shutdown_triggered = True
        logger.critical(f"触发强制关闭: {reason}")
        
        # 通知强制关闭回调
        for callback in self.force_shutdown_callbacks:
            try:
                callback(reason)
            except Exception as e:
                logger.error(f"强制关闭回调异常: {e}")
        
        # 设置强制关闭定时器
        threading.Timer(1.0, self._emergency_exit).start()
    
    def _emergency_exit(self):
        """紧急退出"""
        logger.critical("执行紧急退出")
        try:
            # 尝试正常退出
            sys.exit(1)
        except:
            # 强制退出
            os._exit(1)
    
    def register_force_shutdown_callback(self, callback: Callable[[str], None]):
        """注册强制关闭回调"""
        self.force_shutdown_callbacks.append(callback)
    
    def set_shutdown_start_time(self, start_time: float = None):
        """设置关闭开始时间"""
        self.shutdown_start_time = start_time or time.time()
    
    def get_timeout_limit(self, component: str) -> float:
        """获取组件超时限制"""
        return self.timeout_limits.get(component, 1.0)
    
    def update_timeout_limit(self, component: str, timeout: float):
        """更新组件超时限制"""
        self.timeout_limits[component] = timeout
        logger.info(f"更新超时限制: {component} = {timeout}s")
    
    def get_shutdown_error_summary(self) -> Dict[str, Any]:
        """获取关闭错误摘要"""
        if not self.shutdown_errors:
            return {"total_errors": 0}
        
        error_types = {}
        components = {}
        phases = {}
        
        for error in self.shutdown_errors:
            context = error.context
            error_type = context.get("error_type", "unknown")
            component = context.get("component", "unknown")
            phase = context.get("shutdown_phase", "unknown")
            
            error_types[error_type] = error_types.get(error_type, 0) + 1
            components[component] = components.get(component, 0) + 1
            phases[phase] = phases.get(phase, 0) + 1
        
        return {
            "total_errors": len(self.shutdown_errors),
            "error_types": error_types,
            "affected_components": components,
            "error_phases": phases,
            "force_shutdown_triggered": self.force_shutdown_triggered,
            "shutdown_duration": time.time() - self.shutdown_start_time if self.shutdown_start_time else 0
        }
    
    def clear_shutdown_errors(self):
        """清空关闭错误记录"""
        self.shutdown_errors.clear()
        self.force_shutdown_triggered = False
        self.shutdown_start_time = None
        logger.info("关闭错误记录已清空")

# 创建全局关闭错误处理器实例
shutdown_error_handler = ShutdownErrorHandler()
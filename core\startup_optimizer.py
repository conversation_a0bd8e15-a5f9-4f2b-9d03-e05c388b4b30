#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动性能优化器
优化程序启动流程，实现异步服务初始化
"""

import time
import threading
import logging
from typing import Dict, List, Callable, Any, Optional
from dataclasses import dataclass
from enum import Enum
from concurrent.futures import ThreadPoolExecutor, as_completed

logger = logging.getLogger(__name__)

class InitializationPhase(Enum):
    """初始化阶段枚举"""
    CORE_SERVICES = "core_services"
    UI_COMPONENTS = "ui_components"
    BACKGROUND_SERVICES = "background_services"
    OPTIONAL_FEATURES = "optional_features"

@dataclass
class InitializationTask:
    """初始化任务"""
    name: str
    func: Callable
    phase: InitializationPhase
    priority: int = 0
    timeout: float = 5.0
    required: bool = True
    dependencies: List[str] = None

@dataclass
class StartupMetrics:
    """启动性能指标"""
    total_startup_time: float
    core_services_time: float
    ui_components_time: float
    background_services_time: float
    optional_features_time: float
    failed_tasks: List[str]
    successful_tasks: List[str]

class StartupOptimizer:
    """启动性能优化器
    
    管理程序启动流程，实现异步初始化和性能优化
    """
    
    def __init__(self):
        """初始化启动优化器"""
        self.tasks: Dict[str, InitializationTask] = {}
        self.completed_tasks: List[str] = []
        self.failed_tasks: List[str] = []
        self.startup_start_time: float = 0
        self.phase_times: Dict[InitializationPhase, float] = {}
        
        logger.info("启动性能优化器初始化完成")
    
    def register_task(self, 
                     name: str,
                     func: Callable,
                     phase: InitializationPhase,
                     priority: int = 0,
                     timeout: float = 5.0,
                     required: bool = True,
                     dependencies: List[str] = None):
        """注册初始化任务
        
        Args:
            name: 任务名称
            func: 初始化函数
            phase: 初始化阶段
            priority: 优先级（数字越小优先级越高）
            timeout: 超时时间
            required: 是否必需
            dependencies: 依赖的任务列表
        """
        task = InitializationTask(
            name=name,
            func=func,
            phase=phase,
            priority=priority,
            timeout=timeout,
            required=required,
            dependencies=dependencies or []
        )
        
        self.tasks[name] = task
        logger.info(f"注册启动任务: {name} (阶段: {phase.value}, 优先级: {priority})")
    
    def start_application(self) -> StartupMetrics:
        """启动应用程序
        
        Returns:
            StartupMetrics: 启动性能指标
        """
        self.startup_start_time = time.time()
        logger.info("开始应用程序启动流程")
        
        try:
            # 按阶段执行初始化
            self._execute_phase(InitializationPhase.CORE_SERVICES)
            self._execute_phase(InitializationPhase.UI_COMPONENTS)
            
            # 异步执行后台服务和可选功能
            self._execute_phase_async(InitializationPhase.BACKGROUND_SERVICES)
            self._execute_phase_async(InitializationPhase.OPTIONAL_FEATURES)
            
            total_time = time.time() - self.startup_start_time
            
            metrics = StartupMetrics(
                total_startup_time=total_time,
                core_services_time=self.phase_times.get(InitializationPhase.CORE_SERVICES, 0),
                ui_components_time=self.phase_times.get(InitializationPhase.UI_COMPONENTS, 0),
                background_services_time=self.phase_times.get(InitializationPhase.BACKGROUND_SERVICES, 0),
                optional_features_time=self.phase_times.get(InitializationPhase.OPTIONAL_FEATURES, 0),
                failed_tasks=self.failed_tasks.copy(),
                successful_tasks=self.completed_tasks.copy()
            )
            
            logger.info(f"应用程序启动完成，总耗时: {total_time:.3f}秒")
            return metrics
            
        except Exception as e:
            logger.error(f"应用程序启动异常: {e}")
            raise
    
    def _execute_phase(self, phase: InitializationPhase):
        """同步执行初始化阶段
        
        Args:
            phase: 初始化阶段
        """
        start_time = time.time()
        logger.info(f"开始执行阶段: {phase.value}")
        
        # 获取该阶段的任务
        phase_tasks = [task for task in self.tasks.values() if task.phase == phase]
        
        # 按优先级排序
        phase_tasks.sort(key=lambda x: x.priority)
        
        # 按依赖关系排序
        ordered_tasks = self._resolve_dependencies(phase_tasks)
        
        # 执行任务
        for task in ordered_tasks:
            self._execute_task(task)
        
        phase_time = time.time() - start_time
        self.phase_times[phase] = phase_time
        logger.info(f"阶段 {phase.value} 完成，耗时: {phase_time:.3f}秒")
    
    def _execute_phase_async(self, phase: InitializationPhase):
        """异步执行初始化阶段
        
        Args:
            phase: 初始化阶段
        """
        def async_phase_worker():
            try:
                self._execute_phase(phase)
            except Exception as e:
                logger.error(f"异步阶段 {phase.value} 执行异常: {e}")
        
        thread = threading.Thread(
            target=async_phase_worker,
            name=f"Startup-{phase.value}",
            daemon=True
        )
        thread.start()
        
        logger.info(f"异步启动阶段: {phase.value}")
    
    def _resolve_dependencies(self, tasks: List[InitializationTask]) -> List[InitializationTask]:
        """解析任务依赖关系
        
        Args:
            tasks: 任务列表
            
        Returns:
            List[InitializationTask]: 按依赖关系排序的任务列表
        """
        # 简单的拓扑排序实现
        ordered_tasks = []
        remaining_tasks = tasks.copy()
        
        while remaining_tasks:
            # 找到没有未满足依赖的任务
            ready_tasks = []
            for task in remaining_tasks:
                if all(dep in self.completed_tasks for dep in task.dependencies):
                    ready_tasks.append(task)
            
            if not ready_tasks:
                # 如果没有可执行的任务，可能存在循环依赖
                logger.warning("检测到可能的循环依赖，按优先级执行剩余任务")
                ready_tasks = remaining_tasks
            
            # 按优先级排序
            ready_tasks.sort(key=lambda x: x.priority)
            
            # 添加到执行列表
            for task in ready_tasks:
                ordered_tasks.append(task)
                remaining_tasks.remove(task)
                break  # 一次只处理一个任务以正确处理依赖
        
        return ordered_tasks
    
    def _execute_task(self, task: InitializationTask):
        """执行单个初始化任务
        
        Args:
            task: 初始化任务
        """
        try:
            logger.debug(f"执行初始化任务: {task.name}")
            start_time = time.time()
            
            # 使用线程执行任务以支持超时控制
            result_container = [None]
            exception_container = [None]
            
            def task_wrapper():
                try:
                    result_container[0] = task.func()
                except Exception as e:
                    exception_container[0] = e
            
            task_thread = threading.Thread(target=task_wrapper, daemon=True)
            task_thread.start()
            task_thread.join(timeout=task.timeout)
            
            if task_thread.is_alive():
                # 任务超时
                logger.warning(f"初始化任务 {task.name} 超时")
                if task.required:
                    raise TimeoutError(f"必需任务 {task.name} 超时")
                else:
                    self.failed_tasks.append(task.name)
                    return
            
            if exception_container[0]:
                raise exception_container[0]
            
            # 任务成功
            self.completed_tasks.append(task.name)
            elapsed_time = time.time() - start_time
            logger.debug(f"初始化任务 {task.name} 完成，耗时: {elapsed_time:.3f}秒")
            
        except Exception as e:
            logger.error(f"初始化任务 {task.name} 失败: {e}")
            
            if task.required:
                raise RuntimeError(f"必需的初始化任务失败: {task.name}")
            else:
                self.failed_tasks.append(task.name)
    
    def get_startup_status(self) -> Dict[str, Any]:
        """获取启动状态
        
        Returns:
            Dict[str, Any]: 启动状态信息
        """
        total_tasks = len(self.tasks)
        completed_count = len(self.completed_tasks)
        failed_count = len(self.failed_tasks)
        
        return {
            "total_tasks": total_tasks,
            "completed_tasks": completed_count,
            "failed_tasks": failed_count,
            "success_rate": (completed_count / total_tasks * 100) if total_tasks > 0 else 0,
            "phase_times": {phase.value: time_val for phase, time_val in self.phase_times.items()},
            "startup_time": time.time() - self.startup_start_time if self.startup_start_time > 0 else 0
        }
    
    def wait_for_phase_completion(self, phase: InitializationPhase, timeout: float = 10.0) -> bool:
        """等待指定阶段完成
        
        Args:
            phase: 初始化阶段
            timeout: 超时时间
            
        Returns:
            bool: 是否在超时前完成
        """
        start_time = time.time()
        phase_tasks = [task.name for task in self.tasks.values() if task.phase == phase]
        
        while time.time() - start_time < timeout:
            completed_phase_tasks = [name for name in phase_tasks if name in self.completed_tasks]
            failed_phase_tasks = [name for name in phase_tasks if name in self.failed_tasks]
            
            if len(completed_phase_tasks) + len(failed_phase_tasks) >= len(phase_tasks):
                return True
            
            time.sleep(0.1)
        
        return False

# 全局启动优化器实例
startup_optimizer = StartupOptimizer()

# 预定义的启动任务注册函数
def register_core_services():
    """注册核心服务初始化任务"""
    
    def init_config_manager():
        """初始化配置管理器"""
        from core.config_manager import config
        config.load_config()
        logger.info("配置管理器初始化完成")
        return True
    
    def init_error_handler():
        """初始化错误处理器"""
        from core.error_handler import global_exception_handler
        global_exception_handler.install()
        logger.info("全局异常处理器初始化完成")
        return True
    
    def init_shutdown_manager():
        """初始化关闭管理器"""
        from core.fast_shutdown_manager import fast_shutdown_manager
        # 关闭管理器不需要特殊初始化
        logger.info("快速关闭管理器初始化完成")
        return True
    
    # 注册核心服务任务
    startup_optimizer.register_task(
        "config_manager", init_config_manager,
        InitializationPhase.CORE_SERVICES, priority=0, timeout=1.0
    )
    
    startup_optimizer.register_task(
        "error_handler", init_error_handler,
        InitializationPhase.CORE_SERVICES, priority=1, timeout=1.0
    )
    
    startup_optimizer.register_task(
        "shutdown_manager", init_shutdown_manager,
        InitializationPhase.CORE_SERVICES, priority=2, timeout=1.0
    )

def register_background_services():
    """注册后台服务初始化任务"""
    
    def init_performance_monitor():
        """初始化性能监控器"""
        from core.performance_monitor import performance_monitor
        performance_monitor.start_monitoring()
        logger.info("性能监控器初始化完成")
        return True
    
    def init_ssh_pool():
        """初始化SSH连接池"""
        from core.ssh_connection_pool import ssh_pool
        ssh_pool.start()
        logger.info("SSH连接池初始化完成")
        return True
    
    def init_file_transfer():
        """初始化文件传输管理器"""
        from core.async_file_transfer import transfer_manager
        transfer_manager.start()
        logger.info("文件传输管理器初始化完成")
        return True
    
    # 注册后台服务任务
    startup_optimizer.register_task(
        "performance_monitor", init_performance_monitor,
        InitializationPhase.BACKGROUND_SERVICES, priority=0, timeout=2.0, required=False
    )
    
    startup_optimizer.register_task(
        "ssh_pool", init_ssh_pool,
        InitializationPhase.BACKGROUND_SERVICES, priority=1, timeout=2.0, required=False
    )
    
    startup_optimizer.register_task(
        "file_transfer", init_file_transfer,
        InitializationPhase.BACKGROUND_SERVICES, priority=2, timeout=2.0, required=False
    )
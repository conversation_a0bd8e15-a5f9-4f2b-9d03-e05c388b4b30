# 宇泛升级包工具 - Windows可执行文件打包说明

## 项目简介

这是一个基于Python和Tkinter开发的宇泛升级包工具，提供SSH连接、文件传输、远程命令执行等功能。本文档说明如何将其打包成Windows可执行文件。

## 系统要求

- Windows 10/11
- Python 3.7 或更高版本
- 至少 2GB 可用磁盘空间

## 快速开始

### 方法一：使用批处理文件（推荐）

1. 双击运行 `build.bat`
2. 等待打包完成
3. 在 `dist` 文件夹中找到 `YufanUpgradeTool.exe`

### 方法二：使用Python脚本

1. 打开命令提示符或PowerShell
2. 切换到项目目录：
   ```cmd
   cd d:\py_test\yf_repack_tool
   ```
3. 运行打包脚本：
   ```cmd
   python build_exe.py
   ```

### 方法三：手动使用PyInstaller

1. 安装PyInstaller：
   ```cmd
   pip install pyinstaller
   ```

2. 安装项目依赖：
   ```cmd
   pip install -r requirements.txt
   ```

3. 执行打包命令：
   ```cmd
   pyinstaller --onefile --windowed --name=YufanUpgradeTool main.py
   ```

## 打包选项说明

- `--onefile`: 打包成单个exe文件
- `--windowed`: 不显示控制台窗口（适用于GUI应用）
- `--name`: 指定可执行文件名称
- `--clean`: 清理之前的构建文件
- `--noconfirm`: 自动覆盖现有文件

## 输出文件

打包成功后，会在以下位置生成文件：

```
d:\py_test\yf_repack_tool\
├── dist\                    # 输出目录
│   └── YufanUpgradeTool.exe  # 可执行文件
├── build\                   # 临时构建文件
└── YufanUpgradeTool.spec  # PyInstaller配置文件
```

## 使用说明

1. **分发**: `YufanUpgradeTool.exe` 是独立的可执行文件，可以复制到任何Windows系统运行
2. **首次启动**: 第一次运行可能需要几秒钟的启动时间
3. **防病毒软件**: 某些防病毒软件可能会误报，请添加信任
4. **文件大小**: 生成的exe文件大约20-50MB

## 功能特性

- SSH连接管理
- 远程文件浏览和传输
- 远程命令执行
- PKG文件上传下载
- 实时进度显示
- 日志记录

## 故障排除

### 常见问题

1. **Python未找到**
   - 确保已安装Python 3.7+
   - 检查Python是否已添加到系统PATH

2. **依赖安装失败**
   ```cmd
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

3. **打包失败**
   - 检查main.py是否有语法错误
   - 确保所有依赖都已安装
   - 尝试清理缓存：`pip cache purge`

4. **exe文件无法运行**
   - 检查Windows Defender或其他防病毒软件
   - 确保目标系统是64位Windows
   - 尝试以管理员身份运行

### 高级选项

如需自定义打包选项，可以修改 `build_exe.py` 中的PyInstaller参数：

```python
cmd = [
    sys.executable, "-m", "PyInstaller",
    "--onefile",              # 单文件模式
    "--windowed",             # 无控制台窗口
    "--icon=icon.ico",        # 自定义图标（可选）
    "--add-data=config;.",    # 添加额外文件（可选）
    "--hidden-import=module", # 隐式导入（可选）
    str(main_py)
]
```

## 技术细节

### 项目依赖

- **paramiko**: SSH连接和SFTP文件传输
- **tkinter**: GUI界面（Python内置）
- **threading**: 多线程支持（Python内置）
- **logging**: 日志记录（Python内置）

### 打包原理

PyInstaller通过以下步骤将Python程序打包成exe：

1. 分析代码依赖关系
2. 收集所需的Python模块和库
3. 创建独立的运行环境
4. 生成可执行文件

### 性能优化

- 使用 `--onefile` 模式便于分发
- 使用 `--windowed` 隐藏控制台提升用户体验
- 自动清理临时文件减少磁盘占用

## 版本信息

- Python版本: 3.7+
- PyInstaller版本: 最新稳定版
- 支持系统: Windows 10/11

## 联系支持

如遇到问题，请检查：
1. Python和pip版本
2. 网络连接状态
3. 系统权限设置
4. 防病毒软件配置

---

**注意**: 生成的exe文件仅适用于Windows系统。如需Linux或macOS版本，请在对应系统上重新打包。
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI主题管理器
提供现代化的界面主题和样式
"""

import tkinter as tk
from tkinter import ttk
import logging
from typing import Dict, Any, Optional

from core.config_manager import config

logger = logging.getLogger(__name__)

class ThemeManager:
    """主题管理器"""
    
    def __init__(self):
        self.current_theme = "default"
        self.style = None
        self.themes = {
            "default": self._get_default_theme(),
            "dark": self._get_dark_theme(),
            "light": self._get_light_theme(),
            "modern": self._get_modern_theme()
        }
        
        logger.info("主题管理器初始化完成")
    
    def initialize(self, root: tk.Tk):
        """初始化主题系统"""
        self.style = ttk.Style(root)
        
        # 加载配置的主题
        theme_name = config.get("ui.theme", "modern")
        self.apply_theme(theme_name)
        
        logger.info(f"主题系统初始化完成，当前主题: {self.current_theme}")
    
    def apply_theme(self, theme_name: str):
        """应用主题"""
        if theme_name not in self.themes:
            logger.warning(f"未知主题: {theme_name}，使用默认主题")
            theme_name = "default"
        
        theme_config = self.themes[theme_name]
        self.current_theme = theme_name
        
        # 应用TTK样式
        self._apply_ttk_styles(theme_config)
        
        # 保存主题配置
        config.set("ui.theme", theme_name)
        
        logger.info(f"已应用主题: {theme_name}")
    
    def _apply_ttk_styles(self, theme_config: Dict[str, Any]):
        """应用TTK样式"""
        if not self.style:
            return
        
        colors = theme_config["colors"]
        fonts = theme_config["fonts"]
        
        # 配置基础样式
        self.style.configure(".", 
                           background=colors["bg"],
                           foreground=colors["fg"],
                           font=fonts["default"])
        
        # 按钮样式
        self.style.configure("TButton",
                           background=colors["button_bg"],
                           foreground=colors["button_fg"],
                           borderwidth=1,
                           focuscolor="none",
                           font=fonts["button"])
        
        self.style.map("TButton",
                      background=[("active", colors["button_active"]),
                                ("pressed", colors["button_pressed"])])
        
        # 强调按钮样式
        self.style.configure("Accent.TButton",
                           background=colors["accent"],
                           foreground=colors["accent_fg"],
                           font=fonts["button"])
        
        self.style.map("Accent.TButton",
                      background=[("active", colors["accent_active"]),
                                ("pressed", colors["accent_pressed"])])
        
        # 输入框样式
        self.style.configure("TEntry",
                           fieldbackground=colors["entry_bg"],
                           foreground=colors["entry_fg"],
                           borderwidth=1,
                           insertcolor=colors["cursor"])
        
        self.style.map("TEntry",
                      focuscolor=[("focus", colors["focus"])])
        
        # 标签框样式
        self.style.configure("TLabelFrame",
                           background=colors["bg"],
                           foreground=colors["fg"],
                           borderwidth=1,
                           relief="solid")
        
        self.style.configure("TLabelFrame.Label",
                           background=colors["bg"],
                           foreground=colors["label_fg"],
                           font=fonts["label"])
        
        # 标签样式
        self.style.configure("TLabel",
                           background=colors["bg"],
                           foreground=colors["fg"],
                           font=fonts["default"])
        
        # 树形控件样式
        self.style.configure("Treeview",
                           background=colors["tree_bg"],
                           foreground=colors["tree_fg"],
                           fieldbackground=colors["tree_bg"],
                           font=fonts["tree"])
        
        self.style.configure("Treeview.Heading",
                           background=colors["tree_header_bg"],
                           foreground=colors["tree_header_fg"],
                           font=fonts["tree_header"])
        
        # 进度条样式
        self.style.configure("TProgressbar",
                           background=colors["progress_bg"],
                           troughcolor=colors["progress_trough"],
                           borderwidth=0,
                           lightcolor=colors["progress_bg"],
                           darkcolor=colors["progress_bg"])
        
        # 分隔符样式
        self.style.configure("TSeparator",
                           background=colors["separator"])
        
        # 复选框样式
        self.style.configure("TCheckbutton",
                           background=colors["bg"],
                           foreground=colors["fg"],
                           focuscolor="none",
                           font=fonts["default"])
        
        # 框架样式
        self.style.configure("TFrame",
                           background=colors["bg"],
                           borderwidth=0)
    
    def _get_default_theme(self) -> Dict[str, Any]:
        """获取默认主题配置"""
        return {
            "name": "默认",
            "colors": {
                "bg": "#f0f0f0",
                "fg": "#000000",
                "button_bg": "#e1e1e1",
                "button_fg": "#000000",
                "button_active": "#d4d4d4",
                "button_pressed": "#c7c7c7",
                "accent": "#0078d4",
                "accent_fg": "#ffffff",
                "accent_active": "#106ebe",
                "accent_pressed": "#005a9e",
                "entry_bg": "#ffffff",
                "entry_fg": "#000000",
                "cursor": "#000000",
                "focus": "#0078d4",
                "label_fg": "#000000",
                "tree_bg": "#ffffff",
                "tree_fg": "#000000",
                "tree_header_bg": "#f0f0f0",
                "tree_header_fg": "#000000",
                "progress_bg": "#0078d4",
                "progress_trough": "#e1e1e1",
                "separator": "#d4d4d4"
            },
            "fonts": {
                "default": ("Segoe UI", 9),
                "button": ("Segoe UI", 9),
                "label": ("Segoe UI", 9, "bold"),
                "tree": ("Segoe UI", 9),
                "tree_header": ("Segoe UI", 9, "bold")
            }
        }
    
    def _get_dark_theme(self) -> Dict[str, Any]:
        """获取深色主题配置"""
        return {
            "name": "深色",
            "colors": {
                "bg": "#2d2d2d",
                "fg": "#ffffff",
                "button_bg": "#404040",
                "button_fg": "#ffffff",
                "button_active": "#4d4d4d",
                "button_pressed": "#333333",
                "accent": "#0078d4",
                "accent_fg": "#ffffff",
                "accent_active": "#106ebe",
                "accent_pressed": "#005a9e",
                "entry_bg": "#404040",
                "entry_fg": "#ffffff",
                "cursor": "#ffffff",
                "focus": "#0078d4",
                "label_fg": "#ffffff",
                "tree_bg": "#353535",
                "tree_fg": "#ffffff",
                "tree_header_bg": "#404040",
                "tree_header_fg": "#ffffff",
                "progress_bg": "#0078d4",
                "progress_trough": "#404040",
                "separator": "#555555"
            },
            "fonts": {
                "default": ("Segoe UI", 9),
                "button": ("Segoe UI", 9),
                "label": ("Segoe UI", 9, "bold"),
                "tree": ("Segoe UI", 9),
                "tree_header": ("Segoe UI", 9, "bold")
            }
        }
    
    def _get_light_theme(self) -> Dict[str, Any]:
        """获取浅色主题配置"""
        return {
            "name": "浅色",
            "colors": {
                "bg": "#ffffff",
                "fg": "#000000",
                "button_bg": "#f8f8f8",
                "button_fg": "#000000",
                "button_active": "#e8e8e8",
                "button_pressed": "#d8d8d8",
                "accent": "#0078d4",
                "accent_fg": "#ffffff",
                "accent_active": "#106ebe",
                "accent_pressed": "#005a9e",
                "entry_bg": "#ffffff",
                "entry_fg": "#000000",
                "cursor": "#000000",
                "focus": "#0078d4",
                "label_fg": "#000000",
                "tree_bg": "#ffffff",
                "tree_fg": "#000000",
                "tree_header_bg": "#f8f8f8",
                "tree_header_fg": "#000000",
                "progress_bg": "#0078d4",
                "progress_trough": "#f0f0f0",
                "separator": "#e0e0e0"
            },
            "fonts": {
                "default": ("Segoe UI", 9),
                "button": ("Segoe UI", 9),
                "label": ("Segoe UI", 9, "bold"),
                "tree": ("Segoe UI", 9),
                "tree_header": ("Segoe UI", 9, "bold")
            }
        }
    
    def _get_modern_theme(self) -> Dict[str, Any]:
        """获取现代主题配置"""
        return {
            "name": "现代",
            "colors": {
                "bg": "#fafafa",
                "fg": "#333333",
                "button_bg": "#ffffff",
                "button_fg": "#333333",
                "button_active": "#f0f0f0",
                "button_pressed": "#e0e0e0",
                "accent": "#007acc",
                "accent_fg": "#ffffff",
                "accent_active": "#005a9e",
                "accent_pressed": "#004578",
                "entry_bg": "#ffffff",
                "entry_fg": "#333333",
                "cursor": "#333333",
                "focus": "#007acc",
                "label_fg": "#555555",
                "tree_bg": "#ffffff",
                "tree_fg": "#333333",
                "tree_header_bg": "#f5f5f5",
                "tree_header_fg": "#555555",
                "progress_bg": "#007acc",
                "progress_trough": "#e8e8e8",
                "separator": "#d0d0d0"
            },
            "fonts": {
                "default": ("Segoe UI", 9),
                "button": ("Segoe UI", 9),
                "label": ("Segoe UI", 9, "bold"),
                "tree": ("Segoe UI", 9),
                "tree_header": ("Segoe UI", 9, "bold")
            }
        }
    
    def get_available_themes(self) -> Dict[str, str]:
        """获取可用主题列表"""
        return {name: config["name"] for name, config in self.themes.items()}
    
    def get_current_theme(self) -> str:
        """获取当前主题名称"""
        return self.current_theme
    
    def get_theme_colors(self, theme_name: Optional[str] = None) -> Dict[str, str]:
        """获取主题颜色配置"""
        theme_name = theme_name or self.current_theme
        if theme_name in self.themes:
            return self.themes[theme_name]["colors"]
        return self.themes["default"]["colors"]

# 全局主题管理器实例
theme_manager = ThemeManager()
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试配置集成 - 验证SSH连接配置中的默认目录和脚本配置
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.config_manager import config

def test_ssh_config():
    """测试SSH配置"""
    print("=== SSH配置测试 ===")
    
    ssh_config = config.get_ssh_config()
    print(f"主机: {ssh_config.get('host')}")
    print(f"端口: {ssh_config.get('port')}")
    print(f"用户名: {ssh_config.get('username')}")
    print(f"默认远程目录: {ssh_config.get('default_remote_path')}")
    
    # 不再测试脚本配置，因为已经移除
    
    print()

def test_config_modification():
    """测试配置修改"""
    print("=== 配置修改测试 ===")
    
    # 修改默认远程目录
    original_path = config.get("ssh.default_remote_path")
    print(f"原始远程目录: {original_path}")
    
    test_path = "/tmp/test_directory"
    config.set("ssh.default_remote_path", test_path)
    print(f"修改后远程目录: {config.get('ssh.default_remote_path')}")
    
    # 恢复原始配置
    config.set("ssh.default_remote_path", original_path)
    
    print("配置已恢复")
    print()

def test_default_values():
    """测试默认值"""
    print("=== 默认值测试 ===")
    
    # 测试不存在的配置项
    non_existent = config.get("ssh.non_existent", "default_value")
    print(f"不存在的配置项: {non_existent}")
    
    # 测试其他配置的默认值
    ui_config = config.get("ui.non_existent_setting", "default_ui_value")
    print(f"不存在的UI配置: {ui_config}")
    
    print()

if __name__ == "__main__":
    print("开始配置集成测试...")
    print()
    
    test_ssh_config()
    test_config_modification()
    test_default_values()
    
    print("配置集成测试完成！")
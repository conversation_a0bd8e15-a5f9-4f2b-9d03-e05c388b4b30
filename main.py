#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
宇泛升级包工具 - 主应用程序
重构后的模块化版本
"""

import tkinter as tk
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入核心模块
from core.config_manager import config
from core.logger_config import setup_logging
from core.performance_monitor import performance_monitor
from core.security_manager import security_manager
from core.error_handler import error_handler, global_exception_handler
from core.ssh_connection_pool import ssh_pool

# 导入UI模块
from ui.main_window import MainWindow

def main():
    """主函数"""
    try:
        # 初始化日志系统
        log_level = config.get("logging.level", "INFO")
        logger_config = setup_logging(log_level)
        logger = logger_config.get_logger(__name__)
        
        # 安装全局异常处理器
        global_exception_handler.install()
        
        logger.info("=" * 50)
        logger.info("宇泛升级包工具启动")
        logger.info("=" * 50)
        
        # 启动核心服务
        performance_monitor.start_monitoring()
        ssh_pool.start()
        
        # 创建主窗口
        root = tk.Tk()
        
        # 设置窗口图标（如果存在）
        icon_path = project_root / "assets" / "icon.ico"
        if icon_path.exists():
            root.iconbitmap(str(icon_path))
        
        # 创建主应用程序
        app = MainWindow(root)
        
        # 设置窗口关闭事件
        def on_closing():
            logger.info("应用程序正在关闭...")
            performance_monitor.stop_monitoring()
            ssh_pool.stop()
            global_exception_handler.uninstall()
            app.cleanup()
            root.destroy()
        
        root.protocol("WM_DELETE_WINDOW", on_closing)
        
        # 启动主循环
        logger.info("启动GUI主循环")
        root.mainloop()
        
    except Exception as e:
        print(f"应用程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的线程管理器
提供统一的后台线程管理，支持快速停止和强制终止
"""

import threading
import time
import logging
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum
from concurrent.futures import ThreadPoolExecutor, as_completed, TimeoutError

logger = logging.getLogger(__name__)

class ThreadStatus(Enum):
    """线程状态枚举"""
    RUNNING = "running"
    STOPPING = "stopping"
    STOPPED = "stopped"
    FORCE_STOPPED = "force_stopped"
    ERROR = "error"

@dataclass
class ThreadInfo:
    """线程信息"""
    name: str
    thread: threading.Thread
    status: ThreadStatus
    start_time: float
    stop_time: Optional[float] = None
    timeout: float = 1.0
    force_stop_callback: Optional[Callable] = None

class OptimizedThreadManager:
    """优化的线程管理器
    
    管理所有后台线程，提供统一的停止机制和超时控制
    """
    
    def __init__(self, default_timeout: float = 1.0):
        """初始化线程管理器
        
        Args:
            default_timeout: 默认线程停止超时时间（秒）
        """
        self.default_timeout = default_timeout
        self.threads: Dict[str, ThreadInfo] = {}
        self.stop_event = threading.Event()
        self._lock = threading.Lock()
        
        # 强制停止相关
        self.force_stop_timeout = 2.0
        self._force_stop_executor: Optional[ThreadPoolExecutor] = None
        
        logger.info(f"线程管理器初始化完成，默认超时: {default_timeout}秒")
    
    def register_thread(self, 
                       name: str, 
                       thread: threading.Thread,
                       timeout: float = None,
                       force_stop_callback: Callable = None):
        """注册线程
        
        Args:
            name: 线程名称
            thread: 线程对象
            timeout: 停止超时时间
            force_stop_callback: 强制停止回调函数
        """
        if timeout is None:
            timeout = self.default_timeout
            
        with self._lock:
            thread_info = ThreadInfo(
                name=name,
                thread=thread,
                status=ThreadStatus.RUNNING,
                start_time=time.time(),
                timeout=timeout,
                force_stop_callback=force_stop_callback
            )
            self.threads[name] = thread_info
            
        logger.info(f"注册线程: {name} (超时: {timeout}s)")
    
    def unregister_thread(self, name: str):
        """注销线程
        
        Args:
            name: 线程名称
        """
        with self._lock:
            if name in self.threads:
                del self.threads[name]
                logger.info(f"注销线程: {name}")
    
    def stop_all_threads(self, timeout: float = None) -> Dict[str, bool]:
        """停止所有线程
        
        Args:
            timeout: 总超时时间
            
        Returns:
            Dict[str, bool]: 线程名称到停止结果的映射
        """
        if timeout is None:
            timeout = self.force_stop_timeout
            
        logger.info(f"开始停止所有线程，超时: {timeout}秒")
        
        # 设置停止事件
        self.stop_event.set()
        
        results = {}
        start_time = time.time()
        
        with self._lock:
            thread_list = list(self.threads.values())
        
        # 并行停止所有线程
        with ThreadPoolExecutor(max_workers=min(4, len(thread_list))) as executor:
            future_to_thread = {
                executor.submit(self._stop_single_thread, thread_info): thread_info
                for thread_info in thread_list
            }
            
            # 等待所有线程停止
            remaining_timeout = timeout
            for future in as_completed(future_to_thread, timeout=remaining_timeout):
                thread_info = future_to_thread[future]
                try:
                    success = future.result()
                    results[thread_info.name] = success
                    
                    # 更新剩余超时时间
                    elapsed = time.time() - start_time
                    remaining_timeout = max(0, timeout - elapsed)
                    
                except TimeoutError:
                    logger.warning(f"线程停止超时: {thread_info.name}")
                    results[thread_info.name] = False
                    thread_info.status = ThreadStatus.ERROR
                except Exception as e:
                    logger.error(f"停止线程异常 {thread_info.name}: {e}")
                    results[thread_info.name] = False
                    thread_info.status = ThreadStatus.ERROR
        
        # 强制停止未成功停止的线程
        failed_threads = [name for name, success in results.items() if not success]
        if failed_threads:
            logger.warning(f"强制停止失败的线程: {failed_threads}")
            self._force_stop_threads(failed_threads)
        
        total_time = time.time() - start_time
        success_count = sum(results.values())
        logger.info(f"线程停止完成，成功: {success_count}/{len(results)}, 耗时: {total_time:.3f}秒")
        
        return results
    
    def _stop_single_thread(self, thread_info: ThreadInfo) -> bool:
        """停止单个线程
        
        Args:
            thread_info: 线程信息
            
        Returns:
            bool: 是否成功停止
        """
        try:
            logger.debug(f"停止线程: {thread_info.name}")
            thread_info.status = ThreadStatus.STOPPING
            
            # 等待线程自然结束
            thread_info.thread.join(timeout=thread_info.timeout)
            
            if thread_info.thread.is_alive():
                logger.warning(f"线程 {thread_info.name} 未在超时时间内停止")
                return False
            else:
                thread_info.status = ThreadStatus.STOPPED
                thread_info.stop_time = time.time()
                logger.debug(f"线程 {thread_info.name} 已停止")
                return True
                
        except Exception as e:
            logger.error(f"停止线程 {thread_info.name} 异常: {e}")
            thread_info.status = ThreadStatus.ERROR
            return False
    
    def _force_stop_threads(self, thread_names: List[str]):
        """强制停止指定线程
        
        Args:
            thread_names: 要强制停止的线程名称列表
        """
        logger.warning(f"强制停止线程: {thread_names}")
        
        with self._lock:
            for name in thread_names:
                if name in self.threads:
                    thread_info = self.threads[name]
                    
                    # 调用强制停止回调
                    if thread_info.force_stop_callback:
                        try:
                            thread_info.force_stop_callback()
                        except Exception as e:
                            logger.error(f"强制停止回调异常 {name}: {e}")
                    
                    # 标记为强制停止
                    thread_info.status = ThreadStatus.FORCE_STOPPED
                    thread_info.stop_time = time.time()
    
    def stop_thread(self, name: str, timeout: float = None) -> bool:
        """停止指定线程
        
        Args:
            name: 线程名称
            timeout: 超时时间
            
        Returns:
            bool: 是否成功停止
        """
        with self._lock:
            if name not in self.threads:
                logger.warning(f"线程不存在: {name}")
                return False
            
            thread_info = self.threads[name]
        
        if timeout is None:
            timeout = thread_info.timeout
        
        return self._stop_single_thread(thread_info)
    
    def is_thread_running(self, name: str) -> bool:
        """检查线程是否正在运行
        
        Args:
            name: 线程名称
            
        Returns:
            bool: 是否正在运行
        """
        with self._lock:
            if name not in self.threads:
                return False
            
            thread_info = self.threads[name]
            return (thread_info.status == ThreadStatus.RUNNING and 
                   thread_info.thread.is_alive())
    
    def get_thread_status(self, name: str) -> Optional[ThreadStatus]:
        """获取线程状态
        
        Args:
            name: 线程名称
            
        Returns:
            Optional[ThreadStatus]: 线程状态，不存在则返回None
        """
        with self._lock:
            if name in self.threads:
                return self.threads[name].status
            return None
    
    def get_all_thread_status(self) -> Dict[str, ThreadStatus]:
        """获取所有线程状态
        
        Returns:
            Dict[str, ThreadStatus]: 线程名称到状态的映射
        """
        with self._lock:
            return {name: info.status for name, info in self.threads.items()}
    
    def get_running_threads(self) -> List[str]:
        """获取正在运行的线程列表
        
        Returns:
            List[str]: 正在运行的线程名称列表
        """
        running_threads = []
        with self._lock:
            for name, info in self.threads.items():
                if (info.status == ThreadStatus.RUNNING and 
                    info.thread.is_alive()):
                    running_threads.append(name)
        return running_threads
    
    def is_stop_requested(self) -> bool:
        """检查是否请求停止
        
        Returns:
            bool: 是否请求停止
        """
        return self.stop_event.is_set()
    
    def clear_stopped_threads(self):
        """清理已停止的线程"""
        with self._lock:
            stopped_threads = [
                name for name, info in self.threads.items()
                if info.status in [ThreadStatus.STOPPED, ThreadStatus.FORCE_STOPPED]
                and not info.thread.is_alive()
            ]
            
            for name in stopped_threads:
                del self.threads[name]
                
        if stopped_threads:
            logger.info(f"清理已停止的线程: {stopped_threads}")
    
    def get_thread_statistics(self) -> Dict[str, any]:
        """获取线程统计信息
        
        Returns:
            Dict[str, any]: 统计信息
        """
        with self._lock:
            total_threads = len(self.threads)
            status_counts = {}
            
            for info in self.threads.values():
                status = info.status.value
                status_counts[status] = status_counts.get(status, 0) + 1
            
            running_count = sum(1 for info in self.threads.values() 
                              if info.thread.is_alive())
            
            return {
                "total_threads": total_threads,
                "running_threads": running_count,
                "status_distribution": status_counts,
                "stop_requested": self.stop_event.is_set()
            }

# 全局线程管理器实例
optimized_thread_manager = OptimizedThreadManager()
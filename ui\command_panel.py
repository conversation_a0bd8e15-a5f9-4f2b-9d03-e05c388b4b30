#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
命令执行面板
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import threading
import logging
from typing import Optional, Callable

from core.config_manager import config
from core.ssh_manager import SSHManager

logger = logging.getLogger(__name__)

class CommandPanel(ttk.LabelFrame):
    """命令执行面板"""
    
    def __init__(self, parent):
        super().__init__(parent, text="远程命令执行", padding=10)
        
        self.ssh_manager: Optional[SSHManager] = None
        self.is_connected = False
        self.status_callback: Optional[Callable] = None
        
        self.create_widgets()
        self.load_config()
        self.update_button_states()  # 确保初始按钮状态正确
        
        logger.info("命令面板初始化完成")
    
    def create_widgets(self):
        """创建界面组件"""
        # 命令输入框架
        input_frame = ttk.Frame(self)
        input_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(input_frame, text="命令:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.command_var = tk.StringVar()
        self.command_entry = ttk.Entry(input_frame, textvariable=self.command_var)
        self.command_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        self.command_entry.bind("<Return>", lambda e: self.execute_command())
        
        # 执行按钮
        self.execute_btn = ttk.Button(
            input_frame, 
            text="执行", 
            command=self.execute_command,
            state=tk.DISABLED
        )
        self.execute_btn.pack(side=tk.RIGHT)
        
        # 快捷命令框架
        shortcut_frame = ttk.Frame(self)
        shortcut_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(shortcut_frame, text="快捷命令:").pack(side=tk.LEFT, padx=(0, 5))
        
        # 拆包按钮
        self.unpack_btn = ttk.Button(
            shortcut_frame, 
            text="拆包", 
            command=self.execute_unpack,
            state=tk.DISABLED
        )
        self.unpack_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 打包按钮
        self.pack_btn = ttk.Button(
            shortcut_frame, 
            text="打包", 
            command=self.execute_pack,
            state=tk.DISABLED
        )
        self.pack_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 清理按钮
        self.clean_btn = ttk.Button(
            shortcut_frame, 
            text="清理临时文件", 
            command=self.execute_clean,
            state=tk.DISABLED
        )
        self.clean_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 上传pkg按钮
        self.upload_pkg_btn = ttk.Button(
            shortcut_frame, 
            text="上传pkg", 
            command=self.upload_pkg,
            state=tk.DISABLED
        )
        self.upload_pkg_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 下载pkg按钮
        self.download_pkg_btn = ttk.Button(
            shortcut_frame, 
            text="下载pkg", 
            command=self.download_pkg,
            state=tk.DISABLED
        )
        self.download_pkg_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 停止按钮
        self.stop_btn = ttk.Button(
            shortcut_frame, 
            text="停止", 
            command=self.stop_command,
            state=tk.DISABLED
        )
        self.stop_btn.pack(side=tk.RIGHT)
        
        # 输出区域
        output_frame = ttk.Frame(self)
        output_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建文本框和滚动条
        self.output_text = tk.Text(
            output_frame, 
            wrap=tk.WORD, 
            state=tk.DISABLED,
            font=("Consolas", 9)
        )
        
        scrollbar = ttk.Scrollbar(output_frame, orient=tk.VERTICAL, command=self.output_text.yview)
        self.output_text.config(yscrollcommand=scrollbar.set)
        
        self.output_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 底部控制框架
        control_frame = ttk.Frame(self)
        control_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 清空输出按钮
        self.clear_btn = ttk.Button(
            control_frame, 
            text="清空输出", 
            command=self.clear_output
        )
        self.clear_btn.pack(side=tk.LEFT)
        
        # 保存输出按钮
        self.save_btn = ttk.Button(
            control_frame, 
            text="保存输出", 
            command=self.save_output
        )
        self.save_btn.pack(side=tk.LEFT, padx=(5, 0))
        
        # 自动滚动选项
        self.auto_scroll_var = tk.BooleanVar(value=True)
        self.auto_scroll_cb = ttk.Checkbutton(
            control_frame, 
            text="自动滚动", 
            variable=self.auto_scroll_var
        )
        self.auto_scroll_cb.pack(side=tk.RIGHT)
    
    def load_config(self):
        """加载配置"""
        # 设置默认命令
        self.command_var.set("./repack.sh unpack")
    
    def on_connection_changed(self, connected: bool, ssh_manager: Optional[SSHManager]):
        """连接状态变化处理"""
        self.is_connected = connected
        self.ssh_manager = ssh_manager
        
        logger.info(f"命令面板收到连接状态变化: connected={connected}")
        
        # 更新按钮状态
        state = tk.NORMAL if connected else tk.DISABLED
        self.execute_btn.config(state=state)
        self.unpack_btn.config(state=state)
        self.pack_btn.config(state=state)
        self.clean_btn.config(state=state)
        self.upload_pkg_btn.config(state=state)
        self.download_pkg_btn.config(state=state)
        
        logger.info(f"快捷命令按钮状态已更新为: {'可用' if connected else '禁用'}")
        
        if connected:
            self.append_output("\n=== SSH连接已建立 ===\n", "info")
        else:
            self.append_output("\n=== SSH连接已断开 ===\n", "warning")
    
    def execute_command(self):
        """执行命令"""
        if not self.is_connected or not self.ssh_manager:
            messagebox.showerror("错误", "未连接到SSH服务器")
            return
        
        command = self.command_var.get().strip()
        if not command:
            messagebox.showwarning("警告", "请输入要执行的命令")
            return
        
        # 切换到工作目录并执行命令
        ssh_config = config.get_ssh_config()
        remote_path = ssh_config.get("default_remote_path", "/home/<USER>/yf_2113_repack")
        full_command = f"cd {remote_path} && {command}"
        
        self.append_output(f"\n$ {full_command}\n", "command")
        
        # 在后台线程中执行
        self.stop_btn.config(state=tk.NORMAL)
        threading.Thread(target=self._execute_command_thread, args=(full_command,), daemon=True).start()
    
    def _execute_command_thread(self, command: str):
        """命令执行线程"""
        try:
            stdin, stdout, stderr = self.ssh_manager.execute_command(command)
            
            if stdout is None:
                self.after(0, lambda: self.append_output("命令执行失败\n", "error"))
                return
            
            # 实时读取输出
            while True:
                line = stdout.readline()
                if not line:
                    break
                self.after(0, lambda l=line: self.append_output(l, "output"))
            
            # 读取错误输出
            error_output = stderr.read().decode('utf-8', errors='replace')
            if error_output.strip():
                self.after(0, lambda: self.append_output(f"\nSTDERR:\n{error_output}", "error"))
            
            # 获取退出状态
            exit_status = stdout.channel.recv_exit_status()
            if exit_status == 0:
                self.after(0, lambda: self.append_output("\n命令执行完成\n", "success"))
            else:
                self.after(0, lambda: self.append_output(f"\n命令执行失败，退出码: {exit_status}\n", "error"))
                
        except Exception as e:
            self.after(0, lambda: self.append_output(f"\n执行异常: {str(e)}\n", "error"))
            logger.error(f"命令执行异常: {e}")
        finally:
            self.after(0, lambda: self.stop_btn.config(state=tk.DISABLED))
    
    def execute_unpack(self):
        """执行拆包"""
        self.command_var.set("./repack.sh unpack")
        self.execute_command()
    
    def execute_pack(self):
        """执行打包"""
        self.command_var.set("./repack.sh pack")
        self.execute_command()
    
    def execute_clean(self):
        """清理临时文件"""
        self.command_var.set("rm -rf unpacked")
        self.execute_command()
    
    def upload_pkg(self):
        """上传pkg文件"""
        if not self.is_connected or not self.ssh_manager:
            messagebox.showerror("错误", "未连接到SSH服务器")
            return
        
        # 弹出文件选择框
        from tkinter import filedialog
        filename = filedialog.askopenfilename(
            title="选择要上传的pkg文件",
            filetypes=[
                ("压缩文件", "*.tar.gz *.tgz *.zip *.tar"),
                ("所有文件", "*.*")
            ]
        )
        
        if not filename:
            return
        
        # 在后台线程中执行上传
        threading.Thread(
            target=self._upload_pkg_thread, 
            args=(filename,), 
            daemon=True
        ).start()
    
    def download_pkg(self):
        """下载pkg文件"""
        if not self.is_connected or not self.ssh_manager:
            messagebox.showerror("错误", "未连接到SSH服务器")
            return
        
        # 在后台线程中获取远程文件列表并下载
        threading.Thread(target=self._download_pkg_thread, daemon=True).start()
    
    def _upload_pkg_thread(self, local_file_path):
        """上传pkg文件线程"""
        try:
            import os
            from pathlib import Path
            
            # 获取文件信息
            local_path = Path(local_file_path)
            file_size = local_path.stat().st_size
            
            # 构造远程路径
            ssh_config = config.get_ssh_config()
            remote_base_path = ssh_config.get("default_remote_path", "/home/<USER>/yf_2113_repack")
            remote_in_pkg_path = f"{remote_base_path}/in_pkg"
            remote_file_path = f"{remote_in_pkg_path}/{local_path.name}"
            
            # 检查文件大小，如果大于10MB则显示进度框
            show_progress = file_size > 10 * 1024 * 1024  # 10MB
            
            if show_progress:
                self.after(0, lambda: self._show_upload_progress_dialog(local_path.name))
            
            self.after(0, lambda: self.append_output(f"\n开始上传文件: {local_path.name}\n", "info"))
            
            # 确保远程in_pkg目录存在
            mkdir_command = f"mkdir -p {remote_in_pkg_path}"
            self.ssh_manager.execute_command(mkdir_command)
            
            # 上传文件
            def progress_callback(transferred, total):
                if show_progress:
                    progress = (transferred / total) * 100
                    self.after(0, lambda p=progress: self._update_upload_progress(p))
            
            def completion_callback(success, message):
                if success:
                    self.after(0, lambda: self.append_output(f"文件上传成功: {message}\n", "success"))
                    if show_progress:
                        self.after(0, lambda: self._hide_upload_progress(True))
                else:
                    self.after(0, lambda: self.append_output(f"文件上传失败: {message}\n", "error"))
                    if show_progress:
                        self.after(0, lambda: self._hide_upload_progress(False))
            
            self.ssh_manager.upload_file(
                local_file_path,
                remote_file_path,
                progress_callback=progress_callback if show_progress else None,
                completion_callback=completion_callback
            )
            
        except Exception as e:
            logger.error(f"上传pkg文件异常: {e}")
            self.after(0, lambda: self.append_output(f"\n上传异常: {str(e)}\n", "error"))
            if show_progress:
                self.after(0, lambda: self._hide_upload_progress(False))
    
    def _download_pkg_thread(self):
        """下载pkg文件线程"""
        try:
            # 获取远程out_pkg目录中的tar.gz文件
            ssh_config = config.get_ssh_config()
            remote_base_path = ssh_config.get("default_remote_path", "/home/<USER>/yf_2113_repack")
            remote_out_pkg_path = f"{remote_base_path}/out_pkg"
            
            # 列出远程out_pkg目录中的tar.gz文件
            list_command = f"find {remote_out_pkg_path} -name '*.tar.gz' -type f 2>/dev/null || echo 'NO_FILES'"
            stdin, stdout, stderr = self.ssh_manager.execute_command(list_command)
            
            if stdout is None:
                self.after(0, lambda: self.append_output("获取远程文件列表失败\n", "error"))
                return
            
            output = stdout.read().decode('utf-8', errors='replace').strip()
            
            if not output or output == 'NO_FILES':
                self.after(0, lambda: messagebox.showinfo("提示", "远程out_pkg目录中没有找到tar.gz文件"))
                return
            
            # 解析文件列表
            remote_files = [f.strip() for f in output.split('\n') if f.strip() and f.strip() != 'NO_FILES']
            
            if not remote_files:
                self.after(0, lambda: messagebox.showinfo("提示", "远程out_pkg目录中没有找到tar.gz文件"))
                return
            
            # 如果有多个文件，让用户选择
            if len(remote_files) == 1:
                selected_file = remote_files[0]
            else:
                # 在主线程中显示选择对话框
                self.after(0, lambda: self._show_file_selection_dialog(remote_files))
                return
            
            # 下载选中的文件
            self._download_selected_pkg_file(selected_file)
            
        except Exception as e:
            logger.error(f"下载pkg文件异常: {e}")
            self.after(0, lambda: self.append_output(f"\n下载异常: {str(e)}\n", "error"))
    
    def _download_selected_pkg_file(self, remote_file_path):
        """下载选中的pkg文件"""
        try:
            import os
            from pathlib import Path
            from tkinter import filedialog
            
            # 获取文件名
            filename = os.path.basename(remote_file_path)
            
            # 弹出保存对话框
            local_file_path = filedialog.asksaveasfilename(
                title="保存pkg文件",
                defaultextension=".tar.gz",
                initialvalue=filename,
                filetypes=[
                    ("压缩文件", "*.tar.gz"),
                    ("所有文件", "*.*")
                ]
            )
            
            if not local_file_path:
                return
            
            # 获取远程文件大小
            size_command = f"stat -c%s {remote_file_path} 2>/dev/null || echo '0'"
            stdin, stdout, stderr = self.ssh_manager.execute_command(size_command)
            
            file_size = 0
            if stdout:
                try:
                    file_size = int(stdout.read().decode('utf-8', errors='replace').strip())
                except:
                    file_size = 0
            
            # 检查文件大小，如果大于10MB则显示进度框
            show_progress = file_size > 10 * 1024 * 1024  # 10MB
            
            if show_progress:
                self._show_download_progress_dialog(filename)
            
            self.append_output(f"\n开始下载文件: {filename}\n", "info")
            
            # 下载文件
            def progress_callback(transferred, total):
                if show_progress:
                    progress = (transferred / total) * 100
                    self._update_download_progress(progress)
            
            def completion_callback(success, message):
                if success:
                    self.append_output(f"文件下载成功: {message}\n", "success")
                    if show_progress:
                        self._hide_download_progress(True)
                else:
                    self.append_output(f"文件下载失败: {message}\n", "error")
                    if show_progress:
                        self._hide_download_progress(False)
            
            self.ssh_manager.download_file(
                remote_file_path,
                local_file_path,
                progress_callback=progress_callback if show_progress else None,
                completion_callback=completion_callback
            )
            
        except Exception as e:
            logger.error(f"下载pkg文件异常: {e}")
            self.append_output(f"\n下载异常: {str(e)}\n", "error")
    
    def _show_file_selection_dialog(self, remote_files):
        """显示文件选择对话框"""
        # 创建选择对话框
        dialog = tk.Toplevel(self)
        dialog.title("选择要下载的文件")
        dialog.geometry("500x300")
        dialog.transient(self)
        dialog.grab_set()
        
        # 文件列表
        ttk.Label(dialog, text="请选择要下载的tar.gz文件:").pack(pady=10)
        
        listbox = tk.Listbox(dialog, selectmode=tk.SINGLE)
        listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        for file_path in remote_files:
            filename = os.path.basename(file_path)
            listbox.insert(tk.END, filename)
        
        # 按钮框架
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        def on_download():
            selection = listbox.curselection()
            if selection:
                selected_file = remote_files[selection[0]]
                dialog.destroy()
                # 在后台线程中下载
                threading.Thread(
                    target=self._download_selected_pkg_file,
                    args=(selected_file,),
                    daemon=True
                ).start()
            else:
                messagebox.showwarning("警告", "请选择一个文件")
        
        def on_cancel():
            dialog.destroy()
        
        ttk.Button(button_frame, text="下载", command=on_download).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="取消", command=on_cancel).pack(side=tk.LEFT)
    
    # 进度对话框相关方法
    def _show_upload_progress_dialog(self, filename):
        """显示上传进度对话框"""
        self.upload_progress_dialog = tk.Toplevel(self)
        self.upload_progress_dialog.title("上传进度")
        self.upload_progress_dialog.geometry("400x120")
        self.upload_progress_dialog.transient(self)
        self.upload_progress_dialog.grab_set()
        
        ttk.Label(self.upload_progress_dialog, text=f"正在上传: {filename}").pack(pady=10)
        
        self.upload_progress_var = tk.DoubleVar()
        self.upload_progress_bar = ttk.Progressbar(
            self.upload_progress_dialog,
            variable=self.upload_progress_var,
            mode='determinate'
        )
        self.upload_progress_bar.pack(fill=tk.X, padx=20, pady=5)
        
        self.upload_progress_label = ttk.Label(self.upload_progress_dialog, text="0%")
        self.upload_progress_label.pack(pady=5)
    
    def _update_upload_progress(self, progress):
        """更新上传进度"""
        if hasattr(self, 'upload_progress_var'):
            self.upload_progress_var.set(progress)
            self.upload_progress_label.config(text=f"{progress:.1f}%")
    
    def _hide_upload_progress(self, success):
        """隐藏上传进度对话框"""
        if hasattr(self, 'upload_progress_dialog'):
            self.upload_progress_dialog.destroy()
            delattr(self, 'upload_progress_dialog')
        
        if success:
            messagebox.showinfo("上传完成", "文件上传成功！")
            # 1秒后自动关闭提示
            self.after(1000, lambda: None)
    
    def _show_download_progress_dialog(self, filename):
        """显示下载进度对话框"""
        self.download_progress_dialog = tk.Toplevel(self)
        self.download_progress_dialog.title("下载进度")
        self.download_progress_dialog.geometry("400x120")
        self.download_progress_dialog.transient(self)
        self.download_progress_dialog.grab_set()
        
        ttk.Label(self.download_progress_dialog, text=f"正在下载: {filename}").pack(pady=10)
        
        self.download_progress_var = tk.DoubleVar()
        self.download_progress_bar = ttk.Progressbar(
            self.download_progress_dialog,
            variable=self.download_progress_var,
            mode='determinate'
        )
        self.download_progress_bar.pack(fill=tk.X, padx=20, pady=5)
        
        self.download_progress_label = ttk.Label(self.download_progress_dialog, text="0%")
        self.download_progress_label.pack(pady=5)
    
    def _update_download_progress(self, progress):
        """更新下载进度"""
        if hasattr(self, 'download_progress_var'):
            self.download_progress_var.set(progress)
            self.download_progress_label.config(text=f"{progress:.1f}%")
    
    def _hide_download_progress(self, success):
        """隐藏下载进度对话框"""
        if hasattr(self, 'download_progress_dialog'):
            self.download_progress_dialog.destroy()
            delattr(self, 'download_progress_dialog')
        
        if success:
            messagebox.showinfo("下载完成", "文件下载成功！")
            # 1秒后自动关闭提示
            self.after(1000, lambda: None)
    
    def stop_command(self):
        """停止命令执行"""
        # 这里可以实现命令停止逻辑
        self.append_output("\n=== 用户中断 ===\n", "warning")
        self.stop_btn.config(state=tk.DISABLED)
    
    def append_output(self, text: str, text_type: str = "normal"):
        """追加输出文本"""
        self.output_text.config(state=tk.NORMAL)
        
        # 处理文本：替换转义序列和清理ANSI代码
        processed_text = self.process_output_text(text)
        
        # 根据文本类型设置颜色
        color_map = {
            "normal": "black",
            "command": "blue",
            "output": "black",
            "error": "red",
            "warning": "orange",
            "success": "green",
            "info": "purple"
        }
        
        # 配置标签
        tag_name = f"tag_{text_type}"
        self.output_text.tag_config(tag_name, foreground=color_map.get(text_type, "black"))
        
        # 插入文本
        self.output_text.insert(tk.END, processed_text, tag_name)
        
        # 自动滚动
        if self.auto_scroll_var.get():
            self.output_text.see(tk.END)
        
        self.output_text.config(state=tk.DISABLED)
        
        # 更新状态栏
        if self.status_callback and text_type in ["error", "success", "warning"]:
            self.status_callback(processed_text.strip(), text_type)
    
    def process_output_text(self, text: str) -> str:
        """处理输出文本，清理ANSI代码和处理转义序列"""
        import re
        
        # 处理转义序列
        text = text.replace('\\n', '\n')
        text = text.replace('\\t', '\t')
        text = text.replace('\\r', '\r')
        
        # 移除ANSI颜色代码
        ansi_escape = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
        text = ansi_escape.sub('', text)
        
        # 移除其他ANSI序列（如[36m[1m等）
        ansi_codes = re.compile(r'\[[0-9;]*m')
        text = ansi_codes.sub('', text)
        
        return text
    
    def clear_output(self):
        """清空输出"""
        self.output_text.config(state=tk.NORMAL)
        self.output_text.delete(1.0, tk.END)
        self.output_text.config(state=tk.DISABLED)
    
    def save_output(self):
        """保存输出"""
        from tkinter import filedialog
        
        filename = filedialog.asksaveasfilename(
            title="保存命令输出",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        
        if filename:
            try:
                content = self.output_text.get(1.0, tk.END)
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.append_output(f"\n输出已保存到: {filename}\n", "info")
            except Exception as e:
                messagebox.showerror("保存失败", f"无法保存文件:\\n{str(e)}")
    
    def set_status_callback(self, callback: Callable):
        """设置状态更新回调"""
        self.status_callback = callback
    
    def update_button_states(self):
        """更新按钮状态"""
        state = tk.NORMAL if self.is_connected else tk.DISABLED
        self.execute_btn.config(state=state)
        self.unpack_btn.config(state=state)
        self.pack_btn.config(state=state)
        self.clean_btn.config(state=state)
        self.upload_pkg_btn.config(state=state)
        self.download_pkg_btn.config(state=state)
        
        logger.debug(f"命令面板按钮状态更新: {'可用' if self.is_connected else '禁用'}")
    
    def cleanup(self):
        """清理资源"""
        # 这里可以添加清理逻辑
        pass
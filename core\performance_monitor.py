#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能监控模块
提供应用程序性能监控和诊断功能
"""

import time
import threading
import psutil
import gc
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from collections import deque
import logging

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    timestamp: float
    cpu_percent: float
    memory_mb: float
    memory_percent: float
    thread_count: int
    gc_count: Dict[int, int] = field(default_factory=dict)
    custom_metrics: Dict[str, Any] = field(default_factory=dict)

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, max_history: int = 1000, sample_interval: float = 1.0):
        self.max_history = max_history
        self.sample_interval = sample_interval
        self.metrics_history: deque = deque(maxlen=max_history)
        self.custom_counters: Dict[str, int] = {}
        self.custom_timers: Dict[str, List[float]] = {}
        self.running = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.process = psutil.Process()
        self._lock = threading.Lock()
        
        # 回调函数
        self.alert_callbacks: List[Callable] = []
        
        # 阈值设置
        self.thresholds = {
            "cpu_percent": 80.0,
            "memory_percent": 80.0,
            "memory_mb": 500.0,
            "thread_count": 50
        }
    
    def start_monitoring(self):
        """开始性能监控"""
        if self.running:
            return
        
        self.running = True
        # 创建停止事件用于快速响应停止信号
        self._stop_event = threading.Event()
        
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            name="PerformanceMonitor",
            daemon=True
        )
        self.monitor_thread.start()
        logger.info("性能监控已启动")
    
    def stop_monitoring(self, timeout: float = 1.0):
        """停止性能监控"""
        self.running = False
        
        # 使用事件通知监控线程停止
        if hasattr(self, '_stop_event'):
            self._stop_event.set()
        
        if self.monitor_thread:
            self.monitor_thread.join(timeout=timeout)
            
            # 如果线程仍在运行，记录警告
            if self.monitor_thread.is_alive():
                logger.warning(f"性能监控线程未在 {timeout}秒内停止")
            else:
                logger.info("性能监控已停止")
    
    def force_stop_monitoring(self):
        """强制停止性能监控"""
        logger.warning("强制停止性能监控")
        self.running = False
        
        if hasattr(self, '_stop_event'):
            self._stop_event.set()
        
        # 不等待线程结束，直接标记为停止
        if self.monitor_thread:
            logger.warning("性能监控线程被强制终止")
    
    def is_monitoring_active(self) -> bool:
        """检查监控是否活跃
        
        Returns:
            bool: 监控是否活跃
        """
        return (self.running and 
                self.monitor_thread is not None and 
                self.monitor_thread.is_alive())
    
    def _monitor_loop(self):
        """优化的监控循环，支持快速停止"""
        quick_check_interval = 0.1  # 100ms快速检查间隔
        
        while self.running:
            try:
                metrics = self._collect_metrics()
                
                with self._lock:
                    self.metrics_history.append(metrics)
                
                # 检查阈值
                self._check_thresholds(metrics)
                
                # 使用事件等待而不是sleep，支持快速中断
                # 将采样间隔分割为多个小间隔，每个小间隔检查停止信号
                remaining_time = self.sample_interval
                while remaining_time > 0 and self.running:
                    wait_time = min(quick_check_interval, remaining_time)
                    if self._stop_event.wait(timeout=wait_time):
                        # 收到停止信号，立即退出
                        break
                    remaining_time -= wait_time
                
            except Exception as e:
                logger.error(f"性能监控异常: {e}")
                # 异常情况下也要检查停止信号
                if not self._stop_event.wait(timeout=1.0):
                    continue
                else:
                    break
    
    def _collect_metrics(self) -> PerformanceMetrics:
        """收集性能指标"""
        try:
            # CPU使用率
            cpu_percent = self.process.cpu_percent()
            
            # 内存使用情况
            memory_info = self.process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            memory_percent = self.process.memory_percent()
            
            # 线程数量
            thread_count = self.process.num_threads()
            
            # 垃圾回收统计
            gc_count = {i: gc.get_count()[i] for i in range(3)}
            
            # 自定义指标
            custom_metrics = {}
            with self._lock:
                custom_metrics.update(self.custom_counters)
                for name, times in self.custom_timers.items():
                    if times:
                        custom_metrics[f"{name}_avg"] = sum(times) / len(times)
                        custom_metrics[f"{name}_max"] = max(times)
                        custom_metrics[f"{name}_min"] = min(times)
            
            return PerformanceMetrics(
                timestamp=time.time(),
                cpu_percent=cpu_percent,
                memory_mb=memory_mb,
                memory_percent=memory_percent,
                thread_count=thread_count,
                gc_count=gc_count,
                custom_metrics=custom_metrics
            )
            
        except Exception as e:
            logger.error(f"收集性能指标失败: {e}")
            return PerformanceMetrics(
                timestamp=time.time(),
                cpu_percent=0.0,
                memory_mb=0.0,
                memory_percent=0.0,
                thread_count=0,
                gc_count={},
                custom_metrics={}
            )
    
    def _check_thresholds(self, metrics: PerformanceMetrics):
        """检查性能阈值"""
        alerts = []
        
        if metrics.cpu_percent > self.thresholds["cpu_percent"]:
            alerts.append(f"CPU使用率过高: {metrics.cpu_percent:.1f}%")
        
        if metrics.memory_percent > self.thresholds["memory_percent"]:
            alerts.append(f"内存使用率过高: {metrics.memory_percent:.1f}%")
        
        if metrics.memory_mb > self.thresholds["memory_mb"]:
            alerts.append(f"内存使用量过高: {metrics.memory_mb:.1f}MB")
        
        if metrics.thread_count > self.thresholds["thread_count"]:
            alerts.append(f"线程数量过多: {metrics.thread_count}")
        
        # 触发警报回调
        for alert in alerts:
            logger.warning(f"性能警报: {alert}")
            for callback in self.alert_callbacks:
                try:
                    callback(alert, metrics)
                except Exception as e:
                    logger.error(f"警报回调异常: {e}")
    
    def get_current_metrics(self) -> Optional[PerformanceMetrics]:
        """获取当前性能指标"""
        with self._lock:
            if self.metrics_history:
                return self.metrics_history[-1]
            return None
    
    def get_metrics_history(self, count: int = None) -> List[PerformanceMetrics]:
        """获取性能指标历史"""
        with self._lock:
            if count is None:
                return list(self.metrics_history)
            else:
                return list(self.metrics_history)[-count:]
    
    def get_average_metrics(self, duration_seconds: int = 60) -> Dict[str, float]:
        """获取指定时间段内的平均指标"""
        current_time = time.time()
        cutoff_time = current_time - duration_seconds
        
        with self._lock:
            relevant_metrics = [
                m for m in self.metrics_history 
                if m.timestamp >= cutoff_time
            ]
        
        if not relevant_metrics:
            return {}
        
        return {
            "cpu_percent": sum(m.cpu_percent for m in relevant_metrics) / len(relevant_metrics),
            "memory_mb": sum(m.memory_mb for m in relevant_metrics) / len(relevant_metrics),
            "memory_percent": sum(m.memory_percent for m in relevant_metrics) / len(relevant_metrics),
            "thread_count": sum(m.thread_count for m in relevant_metrics) / len(relevant_metrics)
        }
    
    def increment_counter(self, name: str, value: int = 1):
        """增加自定义计数器"""
        with self._lock:
            self.custom_counters[name] = self.custom_counters.get(name, 0) + value
    
    def record_time(self, name: str, duration: float):
        """记录自定义时间指标"""
        with self._lock:
            if name not in self.custom_timers:
                self.custom_timers[name] = deque(maxlen=100)
            self.custom_timers[name].append(duration)
    
    def add_alert_callback(self, callback: Callable):
        """添加警报回调函数"""
        self.alert_callbacks.append(callback)
    
    def set_threshold(self, metric: str, value: float):
        """设置性能阈值"""
        if metric in self.thresholds:
            self.thresholds[metric] = value
            logger.info(f"设置性能阈值: {metric} = {value}")
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        try:
            return {
                "cpu_count": psutil.cpu_count(),
                "cpu_count_logical": psutil.cpu_count(logical=True),
                "memory_total_gb": psutil.virtual_memory().total / 1024 / 1024 / 1024,
                "disk_usage": {
                    path: {
                        "total_gb": usage.total / 1024 / 1024 / 1024,
                        "used_gb": usage.used / 1024 / 1024 / 1024,
                        "free_gb": usage.free / 1024 / 1024 / 1024,
                        "percent": (usage.used / usage.total) * 100
                    }
                    for path, usage in [("/", psutil.disk_usage("/"))]
                },
                "platform": psutil.WINDOWS if hasattr(psutil, 'WINDOWS') else "Unknown"
            }
        except Exception as e:
            logger.error(f"获取系统信息失败: {e}")
            return {}

class TimerContext:
    """计时器上下文管理器"""
    
    def __init__(self, monitor: PerformanceMonitor, name: str):
        self.monitor = monitor
        self.name = name
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration = time.time() - self.start_time
            self.monitor.record_time(self.name, duration)

# 全局性能监控器实例
performance_monitor = PerformanceMonitor()

def timer(name: str):
    """装饰器：自动计时函数执行时间"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            with TimerContext(performance_monitor, name):
                return func(*args, **kwargs)
        return wrapper
    return decorator

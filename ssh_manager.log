2025-07-31 09:21:10,888 - INFO - 启动宇泛升级包工具
2025-07-31 09:21:10,994 - INFO - 应用程序启动
2025-07-31 09:21:17,501 - INFO - 尝试连接SSH服务器: ma<PERSON><PERSON>yi@10.67.73.254:22
2025-07-31 09:21:17,501 - INFO - 状态更新: 正在连接...
2025-07-31 09:21:17,530 - INFO - Connected (version 2.0, client OpenSSH_8.9p1)
2025-07-31 09:21:17,778 - INFO - Authentication (password) successful!
2025-07-31 09:21:17,881 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-07-31 09:21:17,882 - INFO - 成功连接到 10.67.73.254:22
2025-07-31 09:21:17,882 - INFO - 状态更新: 成功连接到 10.67.73.254:22
2025-07-31 09:21:17,883 - INFO - SSH连接成功: ma<PERSON><PERSON><PERSON>@10.67.73.254:22
2025-07-31 09:21:17,885 - INFO - 执行远程命令: cd '/home/<USER>/yf_repack_tool' && ls -Ap && pwd
2025-07-31 09:21:17,900 - INFO - 执行远程命令: cd '/home/<USER>/yf_repack_tool' && ls -Ap && pwd
2025-07-31 09:21:39,936 - INFO - 应用程序关闭
2025-07-31 09:21:39,936 - INFO - [chan 0] sftp session closed.
2025-07-31 09:21:39,936 - INFO - SFTP会话已关闭。
2025-07-31 09:21:39,937 - INFO - SSH连接已关闭。
2025-07-31 09:30:23,856 - INFO - 启动宇泛升级包工具
2025-07-31 09:30:23,922 - INFO - 应用程序启动
2025-07-31 09:31:03,995 - INFO - 尝试连接SSH服务器: maojunyi@10.67.73.254:22
2025-07-31 09:31:03,995 - INFO - 状态更新: 正在连接...
2025-07-31 09:31:04,020 - INFO - Connected (version 2.0, client OpenSSH_8.9p1)
2025-07-31 09:31:04,265 - INFO - Authentication (password) successful!
2025-07-31 09:31:04,362 - INFO - [chan 0] Opened sftp connection (server version 3)
2025-07-31 09:31:04,362 - INFO - 成功连接到 10.67.73.254:22
2025-07-31 09:31:04,362 - INFO - 状态更新: 成功连接到 10.67.73.254:22
2025-07-31 09:31:04,362 - INFO - SSH连接成功: maojunyi@10.67.73.254:22
2025-07-31 09:31:04,371 - INFO - 执行远程命令: cd '/home/<USER>/yf_repack_tool' && ls -Ap && pwd
2025-07-31 09:31:04,386 - INFO - 执行远程命令: cd '/home/<USER>/yf_repack_tool' && ls -Ap && pwd
2025-07-31 09:31:06,882 - INFO - 命令输出: --- 执行拆包命令: cd /home/<USER>/yf_repack_tool && ./pkg_unpack.sh unpack -v ---
2025-07-31 09:31:06,882 - INFO - 执行拆包命令: cd /home/<USER>/yf_repack_tool && ./pkg_unpack.sh unpack -v
2025-07-31 09:31:13,080 - INFO - 命令输出: --- 执行打包命令: cd /home/<USER>/yf_repack_tool && ./pkg_unpack.sh pack -v ---
2025-07-31 09:31:13,081 - INFO - 执行打包命令: cd /home/<USER>/yf_repack_tool && ./pkg_unpack.sh pack -v
2025-07-31 09:31:22,040 - INFO - 命令输出: --- 执行交互式命令: cd /home/<USER>/yf_repack_tool && ./pkg_unpack.sh -v ---
2025-07-31 09:31:22,040 - INFO - 执行远程交互式命令: cd /home/<USER>/yf_repack_tool && ./pkg_unpack.sh -v
2025-07-31 09:31:29,713 - INFO - 应用程序关闭
2025-07-31 09:31:29,713 - INFO - [chan 0] sftp session closed.
2025-07-31 09:31:29,713 - INFO - SFTP会话已关闭。
2025-07-31 09:31:29,713 - INFO - SSH连接已关闭。
2025-07-31 09:41:33,959 - INFO - 启动宇泛升级包工具
2025-07-31 09:41:34,021 - INFO - 应用程序启动
2025-07-31 09:41:49,571 - INFO - 应用程序关闭
2025-07-31 10:55:12,661 - INFO - 启动宇泛升级包工具
2025-07-31 10:55:12,763 - INFO - 应用程序启动
2025-07-31 10:55:21,091 - INFO - 应用程序关闭

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异步文件传输模块
提供高效的文件传输功能，支持断点续传、并发传输等
"""

import asyncio
import threading
import queue
import time
from pathlib import Path
from typing import Callable, Optional, Dict, Any, List
import logging

logger = logging.getLogger(__name__)

class FileTransferTask:
    """文件传输任务"""
    
    def __init__(self, 
                 task_id: str,
                 local_path: str,
                 remote_path: str,
                 operation: str,  # 'upload' or 'download'
                 priority: int = 0):
        self.task_id = task_id
        self.local_path = Path(local_path)
        self.remote_path = remote_path
        self.operation = operation
        self.priority = priority
        self.status = "pending"  # pending, running, completed, failed, paused, cancelled, interrupted
        self.progress = 0.0
        self.speed = 0.0
        self.error_message = ""
        self.start_time = None
        self.end_time = None
        self.bytes_transferred = 0
        self.total_bytes = 0
        
        # 中断控制
        self.interrupt_event = threading.Event()
        self.force_stop = False
        
    def __lt__(self, other):
        """用于优先队列排序"""
        return self.priority > other.priority

class AsyncFileTransferManager:
    """异步文件传输管理器"""
    
    def __init__(self, max_concurrent_transfers: int = 3):
        self.max_concurrent_transfers = max_concurrent_transfers
        self.task_queue = queue.PriorityQueue()
        self.active_tasks: Dict[str, FileTransferTask] = {}
        self.completed_tasks: List[FileTransferTask] = []
        self.worker_threads = []
        self.running = False
        self._lock = threading.Lock()
        
        # 回调函数
        self.progress_callback: Optional[Callable] = None
        self.completion_callback: Optional[Callable] = None
        self.error_callback: Optional[Callable] = None
    
    def start(self):
        """启动传输管理器"""
        if self.running:
            return
            
        self.running = True
        
        # 启动工作线程
        for i in range(self.max_concurrent_transfers):
            thread = threading.Thread(
                target=self._worker_thread,
                name=f"FileTransfer-{i}",
                daemon=True
            )
            thread.start()
            self.worker_threads.append(thread)
        
        logger.info(f"文件传输管理器已启动，并发数: {self.max_concurrent_transfers}")
    
    def stop(self, timeout: float = 2.0):
        """快速停止传输管理器
        
        Args:
            timeout: 停止超时时间（秒）
        """
        logger.info(f"开始停止文件传输管理器，超时: {timeout}秒")
        self.running = False
        
        # 中断所有活跃任务
        self.interrupt_all_tasks()
        
        # 等待所有工作线程结束
        start_time = time.time()
        for thread in self.worker_threads:
            remaining_timeout = max(0, timeout - (time.time() - start_time))
            thread.join(timeout=remaining_timeout)
            
            if thread.is_alive():
                logger.warning(f"文件传输线程 {thread.name} 未及时停止")
        
        self.worker_threads.clear()
        elapsed_time = time.time() - start_time
        logger.info(f"文件传输管理器已停止，耗时: {elapsed_time:.3f}秒")
    
    def force_stop_all(self):
        """强制停止所有传输任务"""
        logger.warning("强制停止所有文件传输任务")
        self.running = False
        
        with self._lock:
            # 标记所有任务为强制停止
            for task in self.active_tasks.values():
                task.force_stop = True
                task.interrupt_event.set()
                task.status = "interrupted"
        
        # 不等待线程结束，直接清理
        self.worker_threads.clear()
        logger.warning("文件传输管理器强制停止完成")
    
    def interrupt_all_tasks(self):
        """中断所有活跃任务"""
        with self._lock:
            interrupted_count = 0
            for task in self.active_tasks.values():
                if task.status == "running":
                    task.interrupt_event.set()
                    task.status = "interrupted"
                    interrupted_count += 1
            
            if interrupted_count > 0:
                logger.info(f"中断了 {interrupted_count} 个活跃的传输任务")
    
    def add_task(self, task: FileTransferTask) -> str:
        """添加传输任务
        
        Args:
            task: 文件传输任务
            
        Returns:
            str: 任务ID
        """
        with self._lock:
            self.task_queue.put(task)
            logger.info(f"添加传输任务: {task.task_id} ({task.operation})")
            return task.task_id
    
    def cancel_task(self, task_id: str) -> bool:
        """取消传输任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功取消
        """
        with self._lock:
            if task_id in self.active_tasks:
                task = self.active_tasks[task_id]
                task.interrupt_event.set()
                task.status = "cancelled"
                logger.info(f"取消传输任务: {task_id}")
                return True
            return False
    
    def interrupt_task(self, task_id: str) -> bool:
        """中断传输任务（快速停止）
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功中断
        """
        with self._lock:
            if task_id in self.active_tasks:
                task = self.active_tasks[task_id]
                task.interrupt_event.set()
                task.status = "interrupted"
                logger.info(f"中断传输任务: {task_id}")
                return True
            return False
    
    def pause_task(self, task_id: str) -> bool:
        """暂停传输任务"""
        with self._lock:
            if task_id in self.active_tasks:
                task = self.active_tasks[task_id]
                task.status = "paused"
                logger.info(f"暂停传输任务: {task_id}")
                return True
            return False
    
    def resume_task(self, task_id: str) -> bool:
        """恢复传输任务"""
        with self._lock:
            if task_id in self.active_tasks:
                task = self.active_tasks[task_id]
                if task.status == "paused":
                    task.status = "running"
                    logger.info(f"恢复传输任务: {task_id}")
                    return True
            return False
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        with self._lock:
            if task_id in self.active_tasks:
                task = self.active_tasks[task_id]
                return {
                    "task_id": task.task_id,
                    "status": task.status,
                    "progress": task.progress,
                    "speed": task.speed,
                    "bytes_transferred": task.bytes_transferred,
                    "total_bytes": task.total_bytes,
                    "error_message": task.error_message
                }
            return None
    
    def get_all_tasks(self) -> List[Dict[str, Any]]:
        """获取所有任务状态"""
        with self._lock:
            tasks = []
            for task in self.active_tasks.values():
                tasks.append(self.get_task_status(task.task_id))
            return tasks
    
    def _worker_thread(self):
        """工作线程"""
        while self.running:
            try:
                # 从队列获取任务
                task = self.task_queue.get(timeout=1)
                
                with self._lock:
                    self.active_tasks[task.task_id] = task
                
                # 执行任务
                self._execute_task(task)
                
                with self._lock:
                    # 移动到完成列表
                    if task.task_id in self.active_tasks:
                        del self.active_tasks[task.task_id]
                    self.completed_tasks.append(task)
                    
                    # 限制完成任务列表大小
                    if len(self.completed_tasks) > 100:
                        self.completed_tasks = self.completed_tasks[-50:]
                
                self.task_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"工作线程异常: {e}")
    
    def _execute_task(self, task: FileTransferTask):
        """执行传输任务"""
        try:
            task.status = "running"
            task.start_time = time.time()
            
            # 检查是否在开始前就被中断
            if task.interrupt_event.is_set() or task.force_stop:
                task.status = "interrupted"
                task.end_time = time.time()
                logger.info(f"任务在开始前被中断: {task.task_id}")
                return
            
            # 这里需要实际的SSH管理器实例
            # 在实际使用中，需要传入SSH管理器
            # ssh_manager = get_ssh_manager()
            
            if task.operation == "upload":
                self._upload_file(task)
            elif task.operation == "download":
                self._download_file(task)
            
            # 检查是否在执行过程中被中断
            if task.interrupt_event.is_set() or task.force_stop:
                task.status = "interrupted"
            else:
                task.status = "completed"
            
            task.end_time = time.time()
            
            if task.status == "completed" and self.completion_callback:
                self.completion_callback(task.task_id, True, "传输完成")
            elif task.status == "interrupted":
                logger.info(f"任务被中断: {task.task_id}")
                if self.completion_callback:
                    self.completion_callback(task.task_id, False, "传输被中断")
                
        except Exception as e:
            task.status = "failed"
            task.error_message = str(e)
            task.end_time = time.time()
            
            logger.error(f"任务执行失败 {task.task_id}: {e}")
            
            if self.error_callback:
                self.error_callback(task.task_id, str(e))
    
    def _upload_file(self, task: FileTransferTask):
        """上传文件（支持中断检查）"""
        try:
            # 模拟文件传输过程，实际实现需要使用SSH管理器
            total_size = task.local_path.stat().st_size if task.local_path.exists() else 0
            task.total_bytes = total_size
            
            # 分块传输，每块检查中断信号
            chunk_size = 64 * 1024  # 64KB
            transferred = 0
            
            while transferred < total_size:
                # 检查中断信号
                if task.interrupt_event.is_set() or task.force_stop or not self.running:
                    logger.info(f"上传任务被中断: {task.task_id}")
                    break
                
                # 模拟传输一块数据
                remaining = total_size - transferred
                current_chunk = min(chunk_size, remaining)
                
                # 这里应该是实际的上传逻辑
                # ssh_manager.upload_chunk(...)
                time.sleep(0.01)  # 模拟传输时间
                
                transferred += current_chunk
                task.bytes_transferred = transferred
                task.progress = (transferred / total_size) * 100 if total_size > 0 else 100
                
                # 调用进度回调
                if self.progress_callback:
                    self.progress_callback(task.task_id, task.progress, transferred, total_size)
            
        except Exception as e:
            logger.error(f"上传文件异常 {task.task_id}: {e}")
            raise
    
    def _download_file(self, task: FileTransferTask):
        """下载文件（支持中断检查）"""
        try:
            # 模拟文件传输过程，实际实现需要使用SSH管理器
            # 这里应该先获取远程文件大小
            # total_size = ssh_manager.get_file_size(task.remote_path)
            total_size = 1024 * 1024  # 模拟1MB文件
            task.total_bytes = total_size
            
            # 分块传输，每块检查中断信号
            chunk_size = 64 * 1024  # 64KB
            transferred = 0
            
            while transferred < total_size:
                # 检查中断信号
                if task.interrupt_event.is_set() or task.force_stop or not self.running:
                    logger.info(f"下载任务被中断: {task.task_id}")
                    break
                
                # 模拟传输一块数据
                remaining = total_size - transferred
                current_chunk = min(chunk_size, remaining)
                
                # 这里应该是实际的下载逻辑
                # ssh_manager.download_chunk(...)
                time.sleep(0.01)  # 模拟传输时间
                
                transferred += current_chunk
                task.bytes_transferred = transferred
                task.progress = (transferred / total_size) * 100 if total_size > 0 else 100
                
                # 调用进度回调
                if self.progress_callback:
                    self.progress_callback(task.task_id, task.progress, transferred, total_size)
            
        except Exception as e:
            logger.error(f"下载文件异常 {task.task_id}: {e}")
            raise
    
    def get_transfer_statistics(self) -> Dict[str, Any]:
        """获取传输统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        with self._lock:
            active_count = len(self.active_tasks)
            completed_count = len(self.completed_tasks)
            
            # 统计各状态任务数量
            status_counts = {}
            for task in self.active_tasks.values():
                status = task.status
                status_counts[status] = status_counts.get(status, 0) + 1
            
            return {
                "active_tasks": active_count,
                "completed_tasks": completed_count,
                "status_distribution": status_counts,
                "max_concurrent": self.max_concurrent_transfers,
                "manager_running": self.running
            }

# 全局传输管理器实例
transfer_manager = AsyncFileTransferManager()

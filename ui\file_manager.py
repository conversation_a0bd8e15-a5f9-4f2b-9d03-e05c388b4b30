#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件管理面板
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import threading
import logging
from typing import Optional, Callable, List
from pathlib import Path

from core.config_manager import config
from core.ssh_manager import SSHManager

logger = logging.getLogger(__name__)

class FileManagerPanel(ttk.LabelFrame):
    """文件管理面板"""
    
    def __init__(self, parent):
        super().__init__(parent, text="文件管理", padding=10)
        
        self.ssh_manager: Optional[SSHManager] = None
        self.is_connected = False
        self.status_callback: Optional[Callable] = None
        
        # 当前路径
        self.local_path = Path.cwd()
        self.remote_path = "/"
        
        self.create_widgets()
        self.load_config()
        self.refresh_local_files()
        
        logger.info("文件管理面板初始化完成")
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建分割窗口
        paned_window = ttk.PanedWindow(self, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)
        
        # 本地文件面板
        self.create_local_panel(paned_window)
        
        # 远程文件面板
        self.create_remote_panel(paned_window)
        
        # 操作按钮面板
        self.create_operation_panel()
    
    def create_local_panel(self, parent):
        """创建本地文件面板"""
        local_frame = ttk.Frame(parent)
        parent.add(local_frame, weight=1)
        
        # 本地路径框架
        local_path_frame = ttk.Frame(local_frame)
        local_path_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(local_path_frame, text="本地路径:").pack(side=tk.LEFT)
        
        self.local_path_var = tk.StringVar()
        self.local_path_entry = ttk.Entry(local_path_frame, textvariable=self.local_path_var)
        self.local_path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 5))
        self.local_path_entry.bind("<Return>", self.on_local_path_change)
        
        ttk.Button(
            local_path_frame, 
            text="浏览", 
            command=self.browse_local_path,
            width=8
        ).pack(side=tk.RIGHT)
        
        # 本地文件列表
        local_list_frame = ttk.Frame(local_frame)
        local_list_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建Treeview
        self.local_tree = ttk.Treeview(
            local_list_frame, 
            columns=("size", "modified"), 
            show="tree headings",
            selectmode="extended"
        )
        
        # 设置列
        self.local_tree.heading("#0", text="名称")
        self.local_tree.heading("size", text="大小")
        self.local_tree.heading("modified", text="修改时间")
        
        self.local_tree.column("#0", width=200)
        self.local_tree.column("size", width=80)
        self.local_tree.column("modified", width=120)
        
        # 滚动条
        local_scrollbar = ttk.Scrollbar(local_list_frame, orient=tk.VERTICAL, command=self.local_tree.yview)
        self.local_tree.config(yscrollcommand=local_scrollbar.set)
        
        self.local_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        local_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定双击事件
        self.local_tree.bind("<Double-1>", self.on_local_double_click)
        
        # 右键菜单
        self.create_local_context_menu()
    
    def create_remote_panel(self, parent):
        """创建远程文件面板"""
        remote_frame = ttk.Frame(parent)
        parent.add(remote_frame, weight=1)
        
        # 远程路径框架
        remote_path_frame = ttk.Frame(remote_frame)
        remote_path_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(remote_path_frame, text="远程路径:").pack(side=tk.LEFT)
        
        self.remote_path_var = tk.StringVar()
        self.remote_path_entry = ttk.Entry(
            remote_path_frame, 
            textvariable=self.remote_path_var,
            state=tk.DISABLED
        )
        self.remote_path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 5))
        self.remote_path_entry.bind("<Return>", self.on_remote_path_change)
        
        self.refresh_remote_btn = ttk.Button(
            remote_path_frame, 
            text="刷新", 
            command=self.refresh_remote_files,
            state=tk.DISABLED,
            width=8
        )
        self.refresh_remote_btn.pack(side=tk.RIGHT)
        
        # 远程文件列表
        remote_list_frame = ttk.Frame(remote_frame)
        remote_list_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建Treeview
        self.remote_tree = ttk.Treeview(
            remote_list_frame, 
            columns=("size", "modified", "permissions"), 
            show="tree headings",
            selectmode="extended"
        )
        
        # 设置列
        self.remote_tree.heading("#0", text="名称")
        self.remote_tree.heading("size", text="大小")
        self.remote_tree.heading("modified", text="修改时间")
        self.remote_tree.heading("permissions", text="权限")
        
        self.remote_tree.column("#0", width=200)
        self.remote_tree.column("size", width=80)
        self.remote_tree.column("modified", width=120)
        self.remote_tree.column("permissions", width=80)
        
        # 滚动条
        remote_scrollbar = ttk.Scrollbar(remote_list_frame, orient=tk.VERTICAL, command=self.remote_tree.yview)
        self.remote_tree.config(yscrollcommand=remote_scrollbar.set)
        
        self.remote_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        remote_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定双击事件
        self.remote_tree.bind("<Double-1>", self.on_remote_double_click)
        
        # 右键菜单
        self.create_remote_context_menu()
        
        # 初始显示未连接状态
        self.remote_tree.insert("", "end", text="(未连接)", values=("", "", ""))
    
    def create_operation_panel(self):
        """创建操作按钮面板"""
        op_frame = ttk.Frame(self)
        op_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 上传按钮
        self.upload_btn = ttk.Button(
            op_frame, 
            text="上传 →", 
            command=self.upload_files,
            state=tk.DISABLED
        )
        self.upload_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 下载按钮
        self.download_btn = ttk.Button(
            op_frame, 
            text="← 下载", 
            command=self.download_files,
            state=tk.DISABLED
        )
        self.download_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 删除远程文件按钮
        self.delete_remote_btn = ttk.Button(
            op_frame, 
            text="删除远程", 
            command=self.delete_remote_files,
            state=tk.DISABLED
        )
        self.delete_remote_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 同步按钮
        self.sync_btn = ttk.Button(
            op_frame, 
            text="同步", 
            command=self.sync_directories,
            state=tk.DISABLED
        )
        self.sync_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            op_frame, 
            variable=self.progress_var, 
            mode='determinate'
        )
        # 初始隐藏
        
        # 进度标签
        self.progress_label = ttk.Label(op_frame, text="")
        # 初始隐藏
    
    def create_local_context_menu(self):
        """创建本地文件右键菜单"""
        self.local_context_menu = tk.Menu(self, tearoff=0)
        self.local_context_menu.add_command(label="刷新", command=self.refresh_local_files)
        self.local_context_menu.add_separator()
        self.local_context_menu.add_command(label="上传到远程", command=self.upload_selected_files, state=tk.DISABLED)
        self.local_context_menu.add_separator()
        self.local_context_menu.add_command(label="新建文件夹", command=self.create_local_folder)
        self.local_context_menu.add_command(label="删除", command=self.delete_local_files)
        self.local_context_menu.add_separator()
        self.local_context_menu.add_command(label="属性", command=self.show_local_properties)
        
        self.local_tree.bind("<Button-3>", self.show_local_context_menu)
    
    def create_remote_context_menu(self):
        """创建远程文件右键菜单"""
        self.remote_context_menu = tk.Menu(self, tearoff=0)
        self.remote_context_menu.add_command(label="刷新", command=self.refresh_remote_files)
        self.remote_context_menu.add_separator()
        self.remote_context_menu.add_command(label="下载到本地", command=self.download_selected_files, state=tk.DISABLED)
        self.remote_context_menu.add_separator()
        self.remote_context_menu.add_command(label="新建文件夹", command=self.create_remote_folder)
        self.remote_context_menu.add_command(label="删除", command=self.delete_remote_files)
        self.remote_context_menu.add_separator()
        self.remote_context_menu.add_command(label="属性", command=self.show_remote_properties)
        
        self.remote_tree.bind("<Button-3>", self.show_remote_context_menu)
    
    def load_config(self):
        """加载配置"""
        # 加载路径配置
        paths_config = config.get("paths", {})
        
        default_local = paths_config.get("default_local_path", str(Path.cwd()))
        self.local_path = Path(default_local)
        self.local_path_var.set(str(self.local_path))
        
        # 从SSH配置中获取默认远程目录
        ssh_config = config.get_ssh_config()
        default_remote = ssh_config.get("default_remote_path", "/home/<USER>/yf_2113_repack")
        self.remote_path = default_remote
        self.remote_path_var.set(self.remote_path)
    
    def on_connection_changed(self, connected: bool, ssh_manager: Optional[SSHManager]):
        """连接状态变化处理"""
        self.is_connected = connected
        self.ssh_manager = ssh_manager
        
        # 更新按钮和控件状态
        state = tk.NORMAL if connected else tk.DISABLED
        self.remote_path_entry.config(state=state)
        self.refresh_remote_btn.config(state=state)
        self.upload_btn.config(state=state)
        self.download_btn.config(state=state)
        self.delete_remote_btn.config(state=state)
        self.sync_btn.config(state=state)
        
        if connected:
            # 重新加载配置中的默认远程目录
            ssh_config = config.get_ssh_config()
            default_remote = ssh_config.get("default_remote_path", "/home/<USER>/yf_2113_repack")
            self.remote_path = default_remote
            self.remote_path_var.set(self.remote_path)
            
            # 更新右键菜单状态
            self.local_context_menu.entryconfig("上传到远程", state=tk.NORMAL)
            self.remote_context_menu.entryconfig("下载到本地", state=tk.NORMAL)
            
            # 刷新远程文件列表
            self.refresh_remote_files()
        else:
            # 更新右键菜单状态
            self.local_context_menu.entryconfig("上传到远程", state=tk.DISABLED)
            self.remote_context_menu.entryconfig("下载到本地", state=tk.DISABLED)
            
            # 清空远程文件列表
            self.remote_tree.delete(*self.remote_tree.get_children())
            self.remote_tree.insert("", "end", text="(未连接)", values=("", "", ""))
    
    def refresh_local_files(self):
        """刷新本地文件列表"""
        try:
            # 清空现有项目
            self.local_tree.delete(*self.local_tree.get_children())
            
            if not self.local_path.exists():
                self.local_tree.insert("", "end", text="(路径不存在)", values=("", ""))
                return
            
            # 添加上级目录项
            if self.local_path.parent != self.local_path:
                self.local_tree.insert("", "end", text="..", values=("", ""), tags=("parent",))
            
            # 获取文件列表
            items = []
            try:
                for item in self.local_path.iterdir():
                    try:
                        stat = item.stat()
                        size = self.format_size(stat.st_size) if item.is_file() else ""
                        modified = self.format_time(stat.st_mtime)
                        
                        items.append({
                            "name": item.name,
                            "is_dir": item.is_dir(),
                            "size": size,
                            "modified": modified,
                            "path": item
                        })
                    except (OSError, PermissionError):
                        # 跳过无法访问的文件
                        continue
            except PermissionError:
                self.local_tree.insert("", "end", text="(权限不足)", values=("", ""))
                return
            
            # 排序：目录在前，然后按名称排序
            items.sort(key=lambda x: (not x["is_dir"], x["name"].lower()))
            
            # 添加到树形控件
            for item in items:
                icon = "📁" if item["is_dir"] else "📄"
                text = f"{icon} {item['name']}"
                tags = ("directory",) if item["is_dir"] else ("file",)
                
                self.local_tree.insert(
                    "", "end", 
                    text=text, 
                    values=(item["size"], item["modified"]),
                    tags=tags
                )
            
            # 更新路径显示
            self.local_path_var.set(str(self.local_path))
            
        except Exception as e:
            logger.error(f"刷新本地文件列表失败: {e}")
            messagebox.showerror("错误", f"刷新本地文件列表失败:\\n{str(e)}")
    
    def refresh_remote_files(self):
        """刷新远程文件列表"""
        if not self.is_connected or not self.ssh_manager:
            return
        
        # 在后台线程中执行
        threading.Thread(target=self._refresh_remote_files_thread, daemon=True).start()
    
    def _refresh_remote_files_thread(self):
        """刷新远程文件列表线程"""
        try:
            logger.info(f"开始刷新远程文件列表，路径: {self.remote_path}")
            
            # 清空现有项目
            self.after(0, lambda: self.remote_tree.delete(*self.remote_tree.get_children()))
            
            # 执行ls命令获取文件列表
            command = f"ls -la {self.remote_path}"
            logger.info(f"执行远程命令: {command}")
            stdin, stdout, stderr = self.ssh_manager.execute_command(command)
            
            if stdout is None:
                self.after(0, lambda: self.remote_tree.insert("", "end", text="(获取失败)", values=("", "", "")))
                return
            
            output = stdout.read().decode('utf-8', errors='replace')
            error = stderr.read().decode('utf-8', errors='replace')
            
            logger.info(f"ls命令输出长度: {len(output)} 字符")
            logger.info(f"ls命令错误输出: {repr(error)}")
            
            if error.strip():
                logger.warning(f"远程文件列表警告: {error}")
                # 如果是目录不存在的错误，显示错误信息
                if "没有那个文件或目录" in error or "No such file or directory" in error:
                    self.after(0, lambda: self.remote_tree.insert("", "end", text="(目录不存在)", values=("", "", "")))
                    return
            
            # 解析ls输出
            lines = output.strip().split('\n')
            items = []
            
            for line in lines[1:]:  # 跳过第一行（总计）
                if not line.strip():
                    continue
                
                parts = line.split(None, 8)
                if len(parts) < 9:
                    continue
                
                permissions = parts[0]
                size = parts[4]
                date_parts = parts[5:8]
                name = parts[8]
                
                # 跳过当前目录
                if name in ['.']:
                    continue
                
                is_dir = permissions.startswith('d')
                
                items.append({
                    "name": name,
                    "is_dir": is_dir,
                    "size": size if not is_dir else "",
                    "modified": " ".join(date_parts),
                    "permissions": permissions
                })
            
            logger.info(f"解析到 {len(items)} 个文件/目录项")
            
            # 在主线程中更新UI
            self.after(0, lambda: self._update_remote_tree(items))
            
        except Exception as e:
            logger.error(f"刷新远程文件列表失败: {e}")
            self.after(0, lambda: messagebox.showerror("错误", f"刷新远程文件列表失败:\\n{str(e)}"))
    
    def _update_remote_tree(self, items):
        """更新远程文件树"""
        logger.info(f"开始更新远程文件树，项目数: {len(items)}")
        
        # 排序：目录在前，然后按名称排序
        items.sort(key=lambda x: (not x["is_dir"], x["name"].lower()))
        
        # 添加到树形控件
        for item in items:
            icon = "📁" if item["is_dir"] else "📄"
            text = f"{icon} {item['name']}"
            tags = ("directory",) if item["is_dir"] else ("file",)
            
            self.remote_tree.insert(
                "", "end", 
                text=text, 
                values=(item["size"], item["modified"], item["permissions"]),
                tags=tags
            )
            logger.debug(f"添加项目: {text}")
        
        logger.info("远程文件树更新完成")
    
    def on_local_double_click(self, event):
        """本地文件双击事件"""
        selection = self.local_tree.selection()
        if not selection:
            return
        
        item = self.local_tree.item(selection[0])
        text = item["text"]
        
        if text == "..":
            # 返回上级目录
            self.local_path = self.local_path.parent
            self.refresh_local_files()
        elif "📁" in text:
            # 进入目录
            name = text.replace("📁 ", "")
            new_path = self.local_path / name
            if new_path.is_dir():
                self.local_path = new_path
                self.refresh_local_files()
    
    def on_remote_double_click(self, event):
        """远程文件双击事件"""
        if not self.is_connected:
            return
        
        selection = self.remote_tree.selection()
        if not selection:
            return
        
        item = self.remote_tree.item(selection[0])
        text = item["text"]
        
        if text == "📁 ..":
            # 返回上级目录
            self.remote_path = os.path.dirname(self.remote_path.rstrip('/'))
            if not self.remote_path:
                self.remote_path = "/"
            self.remote_path_var.set(self.remote_path)
            self.refresh_remote_files()
        elif "📁" in text:
            # 进入目录
            name = text.replace("📁 ", "")
            if self.remote_path.endswith('/'):
                self.remote_path = self.remote_path + name
            else:
                self.remote_path = self.remote_path + "/" + name
            self.remote_path_var.set(self.remote_path)
            self.refresh_remote_files()
    
    def upload_files(self):
        """上传选中的本地文件"""
        self.upload_selected_files()
    
    def download_files(self):
        """下载选中的远程文件"""
        self.download_selected_files()
    
    def upload_selected_files(self):
        """上传选中的本地文件"""
        if not self.is_connected or not self.ssh_manager:
            messagebox.showerror("错误", "未连接到SSH服务器")
            return
        
        selection = self.local_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择要上传的文件或文件夹")
            return
        
        # 获取选中的文件
        selected_files = []
        for item_id in selection:
            item = self.local_tree.item(item_id)
            text = item["text"]
            
            # 跳过特殊项目
            if text in ["..", "(路径不存在)", "(权限不足)"]:
                continue
            
            # 获取文件名
            if "📁" in text:
                filename = text.replace("📁 ", "")
                is_dir = True
            elif "📄" in text:
                filename = text.replace("📄 ", "")
                is_dir = False
            else:
                continue
            
            local_path = self.local_path / filename
            selected_files.append((local_path, is_dir))
        
        if not selected_files:
            messagebox.showwarning("警告", "没有有效的文件可上传")
            return
        
        # 在后台线程中执行上传
        threading.Thread(
            target=self._upload_files_thread, 
            args=(selected_files,), 
            daemon=True
        ).start()
    
    def download_selected_files(self):
        """下载选中的远程文件"""
        if not self.is_connected or not self.ssh_manager:
            messagebox.showerror("错误", "未连接到SSH服务器")
            return
        
        selection = self.remote_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择要下载的文件或文件夹")
            return
        
        # 获取选中的文件
        selected_files = []
        for item_id in selection:
            item = self.remote_tree.item(item_id)
            text = item["text"]
            
            # 跳过特殊项目
            if text in ["..", "(未连接)", "(获取失败)", "(目录不存在)"]:
                continue
            
            # 获取文件名
            if "📁" in text:
                filename = text.replace("📁 ", "")
                is_dir = True
            elif "📄" in text:
                filename = text.replace("📄 ", "")
                is_dir = False
            else:
                continue
            
            if self.remote_path.endswith('/'):
                remote_path = self.remote_path + filename
            else:
                remote_path = self.remote_path + "/" + filename
            
            selected_files.append((remote_path, filename, is_dir))
        
        if not selected_files:
            messagebox.showwarning("警告", "没有有效的文件可下载")
            return
        
        # 在后台线程中执行下载
        threading.Thread(
            target=self._download_files_thread, 
            args=(selected_files,), 
            daemon=True
        ).start()
    
    def _upload_files_thread(self, selected_files):
        """上传文件线程"""
        try:
            total_files = len(selected_files)
            completed = 0
            large_files = []
            
            # 检查是否有大文件需要显示进度对话框
            for local_path, is_dir in selected_files:
                if not is_dir and local_path.stat().st_size > 10 * 1024 * 1024:  # 10MB
                    large_files.append(local_path.name)
            
            if large_files:
                self.after(0, lambda: self._show_upload_progress_dialog(large_files))
            else:
                self.after(0, lambda: self.show_progress("正在上传文件..."))
            
            for local_path, is_dir in selected_files:
                try:
                    # 构造远程路径
                    if self.remote_path.endswith('/'):
                        remote_path = self.remote_path + local_path.name
                    else:
                        remote_path = self.remote_path + "/" + local_path.name
                    
                    # 检查是否为大文件
                    is_large_file = not is_dir and local_path.stat().st_size > 10 * 1024 * 1024
                    
                    if is_dir:
                        # 上传目录
                        self.ssh_manager.upload_directory(
                            str(local_path), 
                            remote_path,
                            completion_callback=lambda success, msg: logger.info(f"上传目录: {msg}")
                        )
                    else:
                        # 上传文件
                        def progress_callback(transferred, total):
                            if is_large_file:
                                progress = (transferred / total) * 100
                                self.after(0, lambda p=progress: self._update_upload_progress(local_path.name, p))
                        
                        self.ssh_manager.upload_file(
                            str(local_path), 
                            remote_path,
                            progress_callback=progress_callback if is_large_file else None,
                            completion_callback=lambda success, msg: logger.info(f"上传文件: {msg}")
                        )
                    
                    completed += 1
                    if not large_files:
                        progress = (completed / total_files) * 100
                        self.after(0, lambda p=progress: self.update_progress(p))
                    
                except Exception as e:
                    logger.error(f"上传 {local_path} 失败: {e}")
                    self.after(0, lambda err=str(e): messagebox.showerror("上传失败", f"上传文件失败:\n{err}"))
            
            if large_files:
                self.after(0, lambda: self._hide_upload_progress_dialog(True))
            else:
                self.after(0, lambda: self.hide_progress())
            
            self.after(0, lambda: self._show_success_message("上传完成", f"成功上传 {completed} 个文件/文件夹"))
            self.after(0, lambda: self.refresh_remote_files())
            
        except Exception as e:
            logger.error(f"上传过程异常: {e}")
            if large_files:
                self.after(0, lambda: self._hide_upload_progress_dialog(False))
            else:
                self.after(0, lambda: self.hide_progress())
            self.after(0, lambda: messagebox.showerror("上传失败", f"上传过程出现异常:\n{str(e)}"))
    
    def _download_files_thread(self, selected_files):
        """下载文件线程"""
        try:
            total_files = len(selected_files)
            completed = 0
            large_files = []
            
            # 检查是否有大文件需要显示进度对话框
            for remote_path, filename, is_dir in selected_files:
                if not is_dir:
                    # 获取远程文件大小
                    try:
                        size_command = f"stat -c%s {remote_path} 2>/dev/null || echo '0'"
                        stdin, stdout, stderr = self.ssh_manager.execute_command(size_command)
                        if stdout:
                            file_size = int(stdout.read().decode('utf-8', errors='replace').strip())
                            if file_size > 10 * 1024 * 1024:  # 10MB
                                large_files.append(filename)
                    except:
                        pass
            
            if large_files:
                self.after(0, lambda: self._show_download_progress_dialog(large_files))
            else:
                self.after(0, lambda: self.show_progress("正在下载文件..."))
            
            for remote_path, filename, is_dir in selected_files:
                try:
                    # 构造本地路径
                    local_path = self.local_path / filename
                    
                    # 检查是否为大文件
                    is_large_file = filename in large_files
                    
                    if is_dir:
                        # 下载目录
                        self.ssh_manager.download_directory(
                            remote_path, 
                            str(local_path),
                            completion_callback=lambda success, msg: logger.info(f"下载目录: {msg}")
                        )
                    else:
                        # 下载文件
                        def progress_callback(transferred, total):
                            if is_large_file:
                                progress = (transferred / total) * 100
                                self.after(0, lambda p=progress: self._update_download_progress(filename, p))
                        
                        self.ssh_manager.download_file(
                            remote_path, 
                            str(local_path),
                            progress_callback=progress_callback if is_large_file else None,
                            completion_callback=lambda success, msg: logger.info(f"下载文件: {msg}")
                        )
                    
                    completed += 1
                    if not large_files:
                        progress = (completed / total_files) * 100
                        self.after(0, lambda p=progress: self.update_progress(p))
                    
                except Exception as e:
                    logger.error(f"下载 {remote_path} 失败: {e}")
                    self.after(0, lambda err=str(e): messagebox.showerror("下载失败", f"下载文件失败:\n{err}"))
            
            if large_files:
                self.after(0, lambda: self._hide_download_progress_dialog(True))
            else:
                self.after(0, lambda: self.hide_progress())
            
            self.after(0, lambda: self._show_success_message("下载完成", f"成功下载 {completed} 个文件/文件夹"))
            self.after(0, lambda: self.refresh_local_files())
            
        except Exception as e:
            logger.error(f"下载过程异常: {e}")
            if large_files:
                self.after(0, lambda: self._hide_download_progress_dialog(False))
            else:
                self.after(0, lambda: self.hide_progress())
            self.after(0, lambda: messagebox.showerror("下载失败", f"下载过程出现异常:\n{str(e)}"))
    
    def show_progress(self, message: str):
        """显示进度条"""
        self.progress_label.config(text=message)
        self.progress_label.pack(side=tk.RIGHT, padx=(10, 0))
        self.progress_bar.pack(side=tk.RIGHT, padx=(5, 0))
        self.progress_bar.start()
    
    def update_progress(self, value: float):
        """更新进度条"""
        self.progress_var.set(value)
        self.progress_label.config(text=f"进度: {value:.1f}%")
    
    def hide_progress(self):
        """隐藏进度条"""
        self.progress_bar.stop()
        self.progress_bar.pack_forget()
        self.progress_label.pack_forget()
    
    def delete_remote_files(self):
        """删除远程文件"""
        # 实现远程文件删除逻辑
        pass
    
    def sync_directories(self):
        """同步目录"""
        # 实现目录同步逻辑
        pass
    
    # 辅助方法
    def format_size(self, size: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024:
                return f"{size:.1f} {unit}"
            size /= 1024
        return f"{size:.1f} TB"
    
    def format_time(self, timestamp: float) -> str:
        """格式化时间"""
        import datetime
        dt = datetime.datetime.fromtimestamp(timestamp)
        return dt.strftime("%Y-%m-%d %H:%M")
    
    def set_status_callback(self, callback: Callable):
        """设置状态更新回调"""
        self.status_callback = callback
    
    def cleanup(self):
        """清理资源"""
        # 保存当前路径配置
        config.set("paths.default_local_path", str(self.local_path))
        config.save_config()
    
    def show_local_context_menu(self, event):
        """显示本地文件右键菜单"""
        # 选中右键点击的项目
        item = self.local_tree.identify_row(event.y)
        if item:
            self.local_tree.selection_set(item)
            self.local_context_menu.post(event.x_root, event.y_root)
    
    def show_remote_context_menu(self, event):
        """显示远程文件右键菜单"""
        if not self.is_connected:
            return
        
        # 选中右键点击的项目
        item = self.remote_tree.identify_row(event.y)
        if item:
            self.remote_tree.selection_set(item)
            self.remote_context_menu.post(event.x_root, event.y_root)
    
    def browse_local_path(self):
        """浏览本地路径"""
        directory = filedialog.askdirectory(
            title="选择本地目录",
            initialdir=str(self.local_path)
        )
        if directory:
            self.local_path = Path(directory)
            self.refresh_local_files()
    
    def on_local_path_change(self, event):
        """本地路径变化处理"""
        path_str = self.local_path_var.get().strip()
        if path_str:
            try:
                new_path = Path(path_str)
                if new_path.exists() and new_path.is_dir():
                    self.local_path = new_path
                    self.refresh_local_files()
                else:
                    messagebox.showerror("错误", "路径不存在或不是目录")
                    self.local_path_var.set(str(self.local_path))
            except Exception as e:
                messagebox.showerror("错误", f"无效路径: {str(e)}")
                self.local_path_var.set(str(self.local_path))
    
    def on_remote_path_change(self, event):
        """远程路径变化处理"""
        if not self.is_connected:
            return
        
        path_str = self.remote_path_var.get().strip()
        if path_str:
            self.remote_path = path_str
            self.refresh_remote_files()
    
    # 进度对话框相关方法
    def _show_upload_progress_dialog(self, file_list):
        """显示上传进度对话框"""
        self.upload_progress_dialog = tk.Toplevel(self)
        self.upload_progress_dialog.title("上传进度")
        self.upload_progress_dialog.geometry("450x150")
        self.upload_progress_dialog.transient(self)
        self.upload_progress_dialog.grab_set()
        
        ttk.Label(self.upload_progress_dialog, text="正在上传大文件...").pack(pady=10)
        
        self.upload_current_file_label = ttk.Label(self.upload_progress_dialog, text="")
        self.upload_current_file_label.pack(pady=5)
        
        self.upload_progress_var = tk.DoubleVar()
        self.upload_progress_bar = ttk.Progressbar(
            self.upload_progress_dialog,
            variable=self.upload_progress_var,
            mode='determinate'
        )
        self.upload_progress_bar.pack(fill=tk.X, padx=20, pady=5)
        
        self.upload_progress_label = ttk.Label(self.upload_progress_dialog, text="0%")
        self.upload_progress_label.pack(pady=5)
    
    def _update_upload_progress(self, filename, progress):
        """更新上传进度"""
        if hasattr(self, 'upload_progress_var'):
            self.upload_progress_var.set(progress)
            self.upload_progress_label.config(text=f"{progress:.1f}%")
            self.upload_current_file_label.config(text=f"当前文件: {filename}")
    
    def _hide_upload_progress_dialog(self, success):
        """隐藏上传进度对话框"""
        if hasattr(self, 'upload_progress_dialog'):
            self.upload_progress_dialog.destroy()
            delattr(self, 'upload_progress_dialog')
    
    def _show_download_progress_dialog(self, file_list):
        """显示下载进度对话框"""
        self.download_progress_dialog = tk.Toplevel(self)
        self.download_progress_dialog.title("下载进度")
        self.download_progress_dialog.geometry("450x150")
        self.download_progress_dialog.transient(self)
        self.download_progress_dialog.grab_set()
        
        ttk.Label(self.download_progress_dialog, text="正在下载大文件...").pack(pady=10)
        
        self.download_current_file_label = ttk.Label(self.download_progress_dialog, text="")
        self.download_current_file_label.pack(pady=5)
        
        self.download_progress_var = tk.DoubleVar()
        self.download_progress_bar = ttk.Progressbar(
            self.download_progress_dialog,
            variable=self.download_progress_var,
            mode='determinate'
        )
        self.download_progress_bar.pack(fill=tk.X, padx=20, pady=5)
        
        self.download_progress_label = ttk.Label(self.download_progress_dialog, text="0%")
        self.download_progress_label.pack(pady=5)
    
    def _update_download_progress(self, filename, progress):
        """更新下载进度"""
        if hasattr(self, 'download_progress_var'):
            self.download_progress_var.set(progress)
            self.download_progress_label.config(text=f"{progress:.1f}%")
            self.download_current_file_label.config(text=f"当前文件: {filename}")
    
    def _hide_download_progress_dialog(self, success):
        """隐藏下载进度对话框"""
        if hasattr(self, 'download_progress_dialog'):
            self.download_progress_dialog.destroy()
            delattr(self, 'download_progress_dialog')
    
    def _show_success_message(self, title, message):
        """显示成功消息并1秒后自动关闭"""
        success_dialog = tk.Toplevel(self)
        success_dialog.title(title)
        success_dialog.geometry("300x100")
        success_dialog.transient(self)
        success_dialog.grab_set()
        
        ttk.Label(success_dialog, text=message).pack(expand=True)
        
        # 1秒后自动关闭
        self.after(1000, success_dialog.destroy)
    
    # 占位符方法（待实现）
    def create_local_folder(self): 
        messagebox.showinfo("提示", "新建本地文件夹功能待实现")
    
    def delete_local_files(self): 
        messagebox.showinfo("提示", "删除本地文件功能待实现")
    
    def show_local_properties(self): 
        messagebox.showinfo("提示", "本地文件属性功能待实现")
    
    def create_remote_folder(self): 
        messagebox.showinfo("提示", "新建远程文件夹功能待实现")
    
    def show_remote_properties(self): 
        messagebox.showinfo("提示", "远程文件属性功能待实现")
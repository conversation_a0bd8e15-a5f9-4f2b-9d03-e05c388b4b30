#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速关闭管理器集成示例
展示如何在主应用程序中使用FastShutdownManager
"""

import time
import logging
from core.fast_shutdown_manager import fast_shutdown_manager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

class MockSSHConnectionPool:
    """模拟SSH连接池"""
    
    def __init__(self):
        self.connections = ["conn1", "conn2", "conn3"]
    
    def cleanup(self):
        """清理连接池"""
        logger.info("清理SSH连接池...")
        time.sleep(0.1)  # 模拟清理时间
        self.connections.clear()
        logger.info("SSH连接池清理完成")
        return True

class MockPerformanceMonitor:
    """模拟性能监控器"""
    
    def __init__(self):
        self.running = True
    
    def stop(self):
        """停止监控"""
        logger.info("停止性能监控...")
        self.running = False
        time.sleep(0.05)  # 模拟停止时间
        logger.info("性能监控已停止")
        return True

class MockConfigManager:
    """模拟配置管理器"""
    
    def save_config(self):
        """保存配置"""
        logger.info("保存应用配置...")
        time.sleep(0.02)  # 模拟保存时间
        logger.info("配置保存完成")
        return True

def setup_shutdown_handlers():
    """设置关闭处理器"""
    
    # 创建模拟组件
    ssh_pool = MockSSHConnectionPool()
    perf_monitor = MockPerformanceMonitor()
    config_manager = MockConfigManager()
    
    # 注册清理任务
    fast_shutdown_manager.register_cleanup_task(
        name="ssh_pool_cleanup",
        task=ssh_pool.cleanup,
        timeout=0.5,
        priority=1  # 高优先级
    )
    
    fast_shutdown_manager.register_cleanup_task(
        name="performance_monitor_stop",
        task=perf_monitor.stop,
        timeout=0.3,
        priority=2
    )
    
    fast_shutdown_manager.register_cleanup_task(
        name="config_save",
        task=config_manager.save_config,
        timeout=0.2,
        priority=0  # 最高优先级
    )
    
    logger.info("关闭处理器设置完成")

def simulate_main_window_closing():
    """模拟主窗口关闭事件"""
    logger.info("=== 模拟主窗口关闭事件 ===")
    
    try:
        # 启动关闭流程
        metrics = fast_shutdown_manager.initiate_shutdown()
        
        # 输出关闭指标
        logger.info(f"关闭完成 - 总耗时: {metrics.total_shutdown_time:.3f}秒")
        
        if metrics.total_shutdown_time <= 0.5:
            logger.info("✅ 关闭时间符合500ms要求")
        else:
            logger.warning(f"⚠️ 关闭时间超过500ms: {metrics.total_shutdown_time:.3f}秒")
        
        return True
        
    except Exception as e:
        logger.error(f"关闭流程异常: {e}")
        return False

def main():
    """主函数"""
    logger.info("=== 快速关闭管理器集成示例 ===")
    
    # 设置关闭处理器
    setup_shutdown_handlers()
    
    # 模拟应用程序运行
    logger.info("应用程序正在运行...")
    time.sleep(1)
    
    # 模拟用户点击关闭按钮
    logger.info("用户点击关闭按钮...")
    success = simulate_main_window_closing()
    
    if success:
        logger.info("🎉 集成示例运行成功")
    else:
        logger.error("💥 集成示例运行失败")

if __name__ == "__main__":
    main()
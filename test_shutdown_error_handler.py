#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关闭错误处理器测试
"""

import time
import threading
from core.shutdown_error_handler import ShutdownErrorHandler, ShutdownErrorType, ShutdownErrorContext

def test_shutdown_error_handler():
    """测试关闭错误处理器"""
    print("测试关闭错误处理器...")
    
    # 创建错误处理器
    handler = ShutdownErrorHandler()
    
    # 设置关闭开始时间
    handler.set_shutdown_start_time()
    
    # 测试超时错误
    print("\n1. 测试超时错误处理")
    timeout_error = TimeoutError("组件响应超时")
    context = ShutdownErrorContext(
        phase="resource_cleanup",
        component="ssh_pool",
        timeout_duration=1.5,
        retry_count=0
    )
    
    result = handler.handle_shutdown_error(timeout_error, ShutdownErrorType.TIMEOUT, context)
    print(f"超时错误处理结果: {'继续' if result else '停止'}")
    
    # 测试网络错误
    print("\n2. 测试网络错误处理")
    network_error = ConnectionError("SSH连接失败")
    context = ShutdownErrorContext(
        phase="resource_cleanup",
        component="ssh_connection",
        timeout_duration=0.5,
        retry_count=0
    )
    
    result = handler.handle_shutdown_error(network_error, ShutdownErrorType.NETWORK_ERROR, context)
    print(f"网络错误处理结果: {'继续' if result else '停止'}")
    
    # 测试配置保存错误
    print("\n3. 测试配置保存错误处理")
    config_error = IOError("配置文件写入失败")
    context = ShutdownErrorContext(
        phase="config_save",
        component="config_manager",
        timeout_duration=0.6,
        retry_count=0
    )
    
    result = ha
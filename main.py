# --- main.py (宇泛升级包工具GUI版本) ---
import tkinter as tk
from tkinter import filedialog, messagebox, simpledialog
import os  # 用于文件路径操作
import logging
from datetime import datetime
# 注意：如果需要拖拽功能，通常需要使用外部库如TkinterDnD2
# 或实现自定义事件处理，这会更复杂一些
# 对于PyQt/PySide，拖拽功能有更直接的支持

# 假设ssh_manager.py在同一目录或可通过PYTHONPATH访问
# from ssh_manager import SSHManager # 如果作为独立文件运行

# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ssh_manager.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class App:
    """宇泛升级包拆包打包工具主应用程序类"""
    def __init__(self, master):
        """初始化应用程序界面和SSH管理器"""
        self.master = master
        master.title("宇泛升级包拆包打包工具")
        
        # 初始化SSH管理器和连接状态
        self.ssh_manager = None # 将在connect_ssh中实例化
        self.is_connected = False
        
        # 记录应用程序启动
        logger.info("应用程序启动")

        # --- SSH连接配置框架 ---
        self.conn_frame = tk.LabelFrame(master, text="SSH连接配置")
        self.conn_frame.pack(padx=10, pady=5, fill="x")

        # 主机地址输入
        tk.Label(self.conn_frame, text="主机:").grid(row=0, column=0, sticky="w")
        self.host_entry = tk.Entry(self.conn_frame)
        self.host_entry.grid(row=0, column=1, padx=5, pady=2, sticky="ew")
        self.host_entry.insert(0, "************")  # 默认主机地址

        # 用户名输入
        tk.Label(self.conn_frame, text="用户名:").grid(row=1, column=0, sticky="w")
        self.user_entry = tk.Entry(self.conn_frame)
        self.user_entry.grid(row=1, column=1, padx=5, pady=2, sticky="ew")
        self.user_entry.insert(0, "maojunyi")  # 默认用户名

        # 密码输入
        tk.Label(self.conn_frame, text="密码:").grid(row=2, column=0, sticky="w")
        self.pass_entry = tk.Entry(self.conn_frame, show="*")
        self.pass_entry.grid(row=2, column=1, padx=5, pady=2, sticky="ew")
        self.pass_entry.insert(0, "maojunyi")  # 默认密码

        # 端口输入
        tk.Label(self.conn_frame, text="端口:").grid(row=3, column=0, sticky="w")
        self.port_entry = tk.Entry(self.conn_frame)
        self.port_entry.grid(row=3, column=1, padx=5, pady=2, sticky="ew")
        self.port_entry.insert(0, "22")  # 默认SSH端口

        # 连接按钮
        self.connect_btn = tk.Button(self.conn_frame, text="连接", command=self.connect_ssh)
        self.connect_btn.grid(row=4, column=0, columnspan=2, pady=5, sticky="ew")
        
        # 断开连接按钮
        self.disconnect_btn = tk.Button(self.conn_frame, text="断开连接", command=self.disconnect_ssh, state="disabled")
        self.disconnect_btn.grid(row=4, column=2, pady=5, sticky="ew")

        # 连接状态显示
        self.status_label = tk.Label(self.conn_frame, text="状态: 未连接", fg="red")
        self.status_label.grid(row=5, column=0, columnspan=2, sticky="ew")

        # PKG文件上传按钮（移至状态下方）
        self.upload_pkg_btn = tk.Button(self.conn_frame, text="上传PKG文件 (tar.gz)", command=self.upload_pkg_file, state="disabled")
        self.upload_pkg_btn.grid(row=6, column=0, columnspan=2, pady=5, sticky="ew")
        
        # PKG上传进度显示
        self.pkg_progress_label = tk.Label(self.conn_frame, text="PKG上传进度: 0%")
        self.pkg_progress_label.grid(row=7, column=0, columnspan=2, sticky="ew")

        # 使输入框可以自动扩展
        self.conn_frame.grid_columnconfigure(1, weight=1)

        # --- 远程命令执行框架 ---
        self.cmd_frame = tk.LabelFrame(master, text="远程命令执行")
        self.cmd_frame.pack(padx=10, pady=5, fill="x")

        # 脚本路径或命令输入
        tk.Label(self.cmd_frame, text="脚本路径/命令:").grid(row=0, column=0, sticky="w")
        self.command_entry = tk.Entry(self.cmd_frame)
        self.command_entry.grid(row=0, column=1, padx=5, pady=2, sticky="ew")
        self.command_entry.insert(0, "./pkg_unpack.sh -v")  # 默认命令

        # 按钮框架
        self.button_frame = tk.Frame(self.cmd_frame)
        self.button_frame.grid(row=1, column=0, columnspan=2, pady=5, sticky="ew")
        
        # 执行脚本按钮
        self.execute_btn = tk.Button(self.button_frame, text="执行脚本", command=self.execute_remote_script, state="disabled")
        self.execute_btn.grid(row=0, column=0, padx=2, sticky="ew")
        
        # 拆包按钮
        self.unpack_btn = tk.Button(self.button_frame, text="拆包", command=self.execute_unpack, state="disabled")
        self.unpack_btn.grid(row=0, column=1, padx=2, sticky="ew")
        
        # 打包按钮
        self.pack_btn = tk.Button(self.button_frame, text="打包", command=self.execute_pack, state="disabled")
        self.pack_btn.grid(row=0, column=2, padx=2, sticky="ew")
        
        # 下载PKG按钮
        self.download_pkg_btn = tk.Button(self.button_frame, text="下载PKG", command=self.download_pkg_files, state="disabled")
        self.download_pkg_btn.grid(row=1, column=0, columnspan=3, padx=2, pady=(5,0), sticky="ew")
        
        # 下载进度显示框架
        self.download_progress_frame = tk.Frame(self.button_frame)
        self.download_progress_frame.grid(row=2, column=0, columnspan=3, padx=2, pady=(5,0), sticky="ew")
        
        # 下载进度标签
        self.download_progress_label = tk.Label(self.download_progress_frame, text="", fg="blue")
        self.download_progress_label.pack(side="top", fill="x")
        
        # 下载进度条
        self.download_progress_bar = tk.Canvas(self.download_progress_frame, height=20, bg="white", relief="sunken", bd=1)
        self.download_progress_bar.pack(side="top", fill="x", pady=(2,0))
        
        # 初始化时隐藏进度显示
        self.download_progress_frame.grid_remove()
        
        # 设置按钮框架的列权重，使按钮平均分布
        self.button_frame.grid_columnconfigure(0, weight=1)
        self.button_frame.grid_columnconfigure(1, weight=1)
        self.button_frame.grid_columnconfigure(2, weight=1)

        # 命令输出显示区域
        self.cmd_output_text = tk.Text(self.cmd_frame, height=10, wrap="word")
        self.cmd_output_text.grid(row=2, column=0, columnspan=2, padx=5, pady=5, sticky="nsew")
        self.cmd_output_text.config(state="disabled")  # 设置为只读
        self.cmd_frame.grid_rowconfigure(2, weight=1)
        self.cmd_frame.grid_columnconfigure(1, weight=1)

        # --- FTP风格文件管理框架 ---
        self.ftp_frame = tk.LabelFrame(master, text="文件管理")
        self.ftp_frame.pack(padx=10, pady=5, fill="both", expand=True)

        self.paned_window = tk.PanedWindow(self.ftp_frame, orient=tk.HORIZONTAL, sashrelief=tk.RAISED, bd=1)
        self.paned_window.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # --- 左侧：本地文件浏览器 ---
        self.local_panel = tk.Frame(self.paned_window, relief=tk.SUNKEN, bd=1)
        self.local_path_frame = tk.Frame(self.local_panel)
        self.local_path_frame.pack(fill=tk.X, padx=2, pady=2)
        self.local_path_var = tk.StringVar()
        self.local_path_entry_label = tk.Label(self.local_path_frame, text="本地路径:")
        self.local_path_entry_label.pack(side=tk.LEFT)
        self.local_path_entry = tk.Entry(self.local_path_frame, textvariable=self.local_path_var)
        self.local_path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        self.local_path_entry.bind("<Return>", self.list_local_files_event)
        self.browse_local_button = tk.Button(self.local_path_frame, text="浏览", command=self.browse_select_local_path, width=5)
        self.browse_local_button.pack(side=tk.LEFT, padx=(2,0))

        self.local_files_listbox_frame = tk.Frame(self.local_panel)
        self.local_files_listbox_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
        self.local_files_scrollbar = tk.Scrollbar(self.local_files_listbox_frame)
        self.local_files_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.local_files_listbox = tk.Listbox(self.local_files_listbox_frame, yscrollcommand=self.local_files_scrollbar.set, selectmode=tk.EXTENDED, exportselection=False)
        self.local_files_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.local_files_scrollbar.config(command=self.local_files_listbox.yview)
        self.local_files_listbox.bind("<Double-1>", self.on_local_file_double_click)
        self.paned_window.add(self.local_panel, minsize=200)

        # --- 右侧：远程文件浏览器 ---
        self.remote_panel = tk.Frame(self.paned_window, relief=tk.SUNKEN, bd=1)
        self.remote_path_frame = tk.Frame(self.remote_panel)
        self.remote_path_frame.pack(fill=tk.X, padx=2, pady=2)
        self.remote_path_var = tk.StringVar()
        self.remote_path_entry_label = tk.Label(self.remote_path_frame, text="远程路径:")
        self.remote_path_entry_label.pack(side=tk.LEFT)
        self.remote_path_entry = tk.Entry(self.remote_path_frame, textvariable=self.remote_path_var, state="disabled")
        self.remote_path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        self.remote_path_entry.bind("<Return>", self.list_remote_files_event)
        # 远程路径默认值在连接成功后设置或用户输入

        self.remote_files_listbox_frame = tk.Frame(self.remote_panel)
        self.remote_files_listbox_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
        self.remote_files_scrollbar = tk.Scrollbar(self.remote_files_listbox_frame)
        self.remote_files_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.remote_files_listbox = tk.Listbox(self.remote_files_listbox_frame, yscrollcommand=self.remote_files_scrollbar.set, selectmode=tk.EXTENDED, state="disabled", exportselection=False)
        self.remote_files_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.remote_files_scrollbar.config(command=self.remote_files_listbox.yview)
        self.remote_files_listbox.bind("<Double-1>", self.on_remote_file_double_click)
        self.paned_window.add(self.remote_panel, minsize=200)
        
        # 初始化本地路径并列出文件
        self.local_path_var.set(os.getcwd())
        self.list_local_files()
        self.remote_path_var.set("/home/<USER>/yf_repack_tool") # 默认远程起始路径

        # --- 文件操作按钮区域 (放在FTP框架底部) ---
        self.file_ops_frame = tk.Frame(self.ftp_frame)
        self.file_ops_frame.pack(fill=tk.X, padx=5, pady=(0,5)) # pady top 0, bottom 5

        self.upload_button = tk.Button(self.file_ops_frame, text="上传选定 (本地 -> 远程)", command=self.upload_selected_from_list, state="disabled")
        self.upload_button.pack(side=tk.LEFT, padx=2, pady=2, expand=True, fill=tk.X)

        self.download_button = tk.Button(self.file_ops_frame, text="下载选定 (远程 -> 本地)", command=self.download_selected_from_list, state="disabled")
        self.download_button.pack(side=tk.LEFT, padx=2, pady=2, expand=True, fill=tk.X)

        self.delete_remote_button = tk.Button(self.file_ops_frame, text="删除远程选定", command=self.delete_selected_remote_from_list, state="disabled")
        self.delete_remote_button.pack(side=tk.LEFT, padx=2, pady=2, expand=True, fill=tk.X)
        
        self.refresh_remote_button = tk.Button(self.file_ops_frame, text="刷新远程", command=self.list_remote_files, state="disabled")
        self.refresh_remote_button.pack(side=tk.LEFT, padx=2, pady=2)

        # 文件传输进度标签 (放在操作按钮下方)
        self.file_transfer_progress_label = tk.Label(self.ftp_frame, text="传输进度: --")
        self.file_transfer_progress_label.pack(fill=tk.X, padx=5, pady=(0,5))
        self.file_transfer_progress_label.pack_forget() # 初始隐藏



    def update_status(self, message, color="black"):
        """更新状态标签显示"""
        self.status_label.config(text=f"状态: {message}", fg=color)
        logger.info(f"状态更新: {message}")

    def append_command_output(self, text):
        """向命令输出区域追加文本"""
        self.cmd_output_text.config(state="normal")
        self.cmd_output_text.insert(tk.END, text)
        self.cmd_output_text.see(tk.END)  # 自动滚动到底部
        self.cmd_output_text.config(state="disabled")
        # 记录命令输出到日志
        logger.info(f"命令输出: {text.strip()}")
    
    def enable_buttons(self):
        """启用所有需要SSH连接的按钮"""
        self.upload_pkg_btn.config(state="normal")
        self.execute_btn.config(state="normal")
        self.unpack_btn.config(state="normal")
        self.pack_btn.config(state="normal")
        self.download_pkg_btn.config(state="normal")
        self.disconnect_btn.config(state="normal")
        
        # FTP Style buttons
        self.remote_path_entry.config(state="normal")
        self.remote_files_listbox.config(state="normal")
        self.upload_button.config(state="normal")
        self.download_button.config(state="normal")
        self.delete_remote_button.config(state="normal")
        self.refresh_remote_button.config(state="normal")
        
        # 初始加载远程文件列表
        self.list_remote_files()
        
    def disable_buttons(self):
        """禁用所有需要SSH连接的按钮"""
        self.upload_pkg_btn.config(state="disabled")
        self.execute_btn.config(state="disabled")
        self.unpack_btn.config(state="disabled")
        self.pack_btn.config(state="disabled")
        self.download_pkg_btn.config(state="disabled")
        self.disconnect_btn.config(state="disabled")

        # FTP Style buttons
        self.remote_path_entry.config(state="disabled")
        self.remote_files_listbox.config(state="disabled")
        self.upload_button.config(state="disabled")
        self.download_button.config(state="disabled")
        self.delete_remote_button.config(state="disabled")
        self.refresh_remote_button.config(state="disabled")
        self.remote_files_listbox.delete(0, tk.END) # 清空远程列表
        self.remote_files_listbox.insert(tk.END, "(未连接)")

    def connect_ssh(self):
        """启动SSH连接"""
        host = self.host_entry.get()
        username = self.user_entry.get() # 统一变量名为 username
        password = self.pass_entry.get() # 统一变量名为 password
        port_str = self.port_entry.get()

        if not host or not username:
            messagebox.showerror("错误", "主机和用户名不能为空")
            return
        
        try:
            port = int(port_str)
        except ValueError:
            messagebox.showerror("错误", "端口号必须是一个有效的数字")
            logger.error(f"端口输入错误: '{port_str}' 不是一个有效的数字")
            return

        # 在这里实例化SSHManager
        try:
            from ssh_manager import SSHManager # 确保导入
            self.ssh_manager = SSHManager(host, port, username, password=password)
        except ImportError:
            logger.error("无法导入SSHManager类。请确保ssh_manager.py存在且在PYTHONPATH中。")
            messagebox.showerror("导入错误", "无法加载SSH管理模块。")
            return
        except Exception as e:
            logger.error(f"实例化SSHManager时发生错误: {e}")
            messagebox.showerror("错误", f"SSH管理器初始化失败: {e}")
            return

        logger.info(f"尝试连接SSH服务器: {username}@{host}:{port}")
        self.update_status("正在连接...", "blue")
        # 在单独线程中连接以避免GUI冻结
        # _connect_ssh_thread 现在将使用 self.ssh_manager
        threading.Thread(target=self._connect_ssh_thread, daemon=True).start()

    def _connect_ssh_thread(self):
        """
        在单独的线程中连接SSH服务器（使用self.ssh_manager），避免GUI冻结。
        连接成功后，启用相关按钮。
        """
        if not self.ssh_manager: # 防御性检查
            logger.error("SSHManager未在_connect_ssh_thread中实例化")
            self.master.after(0, lambda: self.update_status("连接错误: SSHManager未初始化", "red"))
            return

        # self.ssh_manager 已经由 connect_ssh 方法用正确的参数实例化
        # 不再需要在此处重新设置 host, user, pwd, port

        if self.ssh_manager.connect(): # connect方法现在使用实例自身的host, port等属性
            self.is_connected = True
            self.master.after(0, lambda: self.update_status(f"成功连接到 {self.ssh_manager.host}:{self.ssh_manager.port}", "green"))
            self.master.after(0, self.enable_buttons)
            logger.info(f"SSH连接成功: {self.ssh_manager.username}@{self.ssh_manager.host}:{self.ssh_manager.port}")
            # 连接成功后，设置远程路径的默认值并列出文件
            self.master.after(0, lambda: self.remote_path_var.set(self.ssh_manager.get_home_directory() or "/"))
            self.master.after(0, self.list_remote_files)
        else:
            self.is_connected = False
            self.master.after(0, lambda: self.update_status(f"连接失败: {self.ssh_manager.host}:{self.ssh_manager.port}", "red"))
            self.master.after(0, self.disable_buttons)
            logger.error(f"SSH连接失败: {self.ssh_manager.username}@{self.ssh_manager.host}:{self.ssh_manager.port}")
            self.master.after(0, lambda: messagebox.showerror("连接失败", f"无法连接到 {self.ssh_manager.host}:{self.ssh_manager.port}。请检查网络和凭据。"))
        """
        if self.ssh_manager.connect(host, user, pwd, port):
            self.is_connected = True
            self.master.after(0, lambda: self.update_status("已连接!", "green"))
            self.master.after(0, lambda: self.append_command_output("已成功连接到服务器。\n"))
            self.master.after(0, self.enable_buttons)  # 启用按钮
            logger.info(f"SSH连接成功: {user}@{host}:{port}")
        else:
            self.is_connected = False
            self.master.after(0, lambda: self.update_status("连接失败!", "red"))
            self.master.after(0, lambda: self.append_command_output("连接服务器失败。\n"))
            self.master.after(0, self.disable_buttons)  # 确保按钮被禁用
            logger.error(f"SSH连接失败: {user}@{host}:{port}")
        """

    def execute_remote_script(self):
        """执行远程脚本或命令（支持交互式输入）"""
        if not self.ssh_manager.client:
            messagebox.showerror("错误", "未连接到SSH服务器。")
            logger.error("尝试执行命令但未连接SSH")
            return
        command = self.command_entry.get()
        if not command:
            messagebox.showwarning("输入错误", "请输入命令或脚本路径。")
            logger.warning("尝试执行空命令")
            return

        # 清除之前的输出
        self.cmd_output_text.config(state="normal")
        self.cmd_output_text.delete(1.0, tk.END)
        self.cmd_output_text.config(state="disabled")
        
        # 在执行命令前先切换到工作目录
        full_command = f"cd /home/<USER>/yf_repack_tool && {command}"
        self.append_command_output(f"\n--- 执行交互式命令: {full_command} ---\n")
        logger.info(f"执行远程交互式命令: {full_command}")

        # 使用交互式命令执行，支持用户输入
        self.ssh_manager.start_interactive_command(
            full_command, 
            self._command_output_callback,
            self._input_request_callback
        )
    
    def download_pkg_files(self):
        """下载远程PKG文件到本地"""
        if not self.ssh_manager.client:
            messagebox.showerror("错误", "未连接到SSH服务器。")
            logger.error("尝试下载PKG文件但未连接SSH")
            return
        
        # 选择本地保存目录
        local_dir = filedialog.askdirectory(title="选择PKG文件保存目录")
        if not local_dir:
            return  # 用户取消选择
        
        # 获取远程目录中的tar.gz文件列表
        remote_pkg_dir = "/home/<USER>/yf_repack_tool/out_pkg"
        
        def get_pkg_files():
            try:
                # 执行ls命令获取tar.gz文件列表
                stdin, stdout, stderr = self.ssh_manager.client.exec_command(f"ls -1 {remote_pkg_dir}/*.tar.gz 2>/dev/null || echo 'NO_FILES'")
                output = stdout.read().decode('utf-8').strip()
                error = stderr.read().decode('utf-8').strip()
                
                if output == 'NO_FILES' or not output:
                    self.master.after(0, lambda: messagebox.showinfo("提示", "远程目录中没有找到.tar.gz文件"))
                    return
                
                # 解析文件列表
                pkg_files = [line.strip() for line in output.split('\n') if line.strip()]
                
                if not pkg_files:
                    self.master.after(0, lambda: messagebox.showinfo("提示", "远程目录中没有找到.tar.gz文件"))
                    return
                
                # 在主线程中显示文件选择对话框
                self.master.after(0, lambda: self._show_pkg_file_selection_dialog(pkg_files, local_dir))
                
            except Exception as e:
                self.master.after(0, lambda: messagebox.showerror("错误", f"获取PKG文件列表失败: {str(e)}"))
                logger.error(f"获取PKG文件列表失败: {str(e)}")
        
        # 在单独线程中执行
        threading.Thread(target=get_pkg_files, daemon=True).start()
    
    def _show_pkg_file_selection_dialog(self, pkg_files, local_dir):
        """显示PKG文件选择对话框"""
        dialog = tk.Toplevel(self.master)
        dialog.title("选择要下载的PKG文件")
        dialog.geometry("500x400")
        dialog.transient(self.master)
        dialog.grab_set()
        
        # 居中显示
        dialog.geometry("+%d+%d" % (self.master.winfo_rootx() + 50, self.master.winfo_rooty() + 50))
        
        # 提示标签
        label = tk.Label(dialog, text=f"找到 {len(pkg_files)} 个PKG文件，请选择要下载的文件：")
        label.pack(pady=10)
        
        # 文件列表框架
        list_frame = tk.Frame(dialog)
        list_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # 创建列表框和滚动条
        listbox = tk.Listbox(list_frame, selectmode=tk.MULTIPLE)
        scrollbar = tk.Scrollbar(list_frame, orient="vertical", command=listbox.yview)
        listbox.config(yscrollcommand=scrollbar.set)
        
        listbox.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 添加文件到列表框
        for pkg_file in pkg_files:
            filename = os.path.basename(pkg_file)
            listbox.insert(tk.END, filename)
        
        # 按钮框架
        button_frame = tk.Frame(dialog)
        button_frame.pack(pady=10)
        
        def download_selected():
            selected_indices = listbox.curselection()
            if not selected_indices:
                messagebox.showwarning("提示", "请至少选择一个文件")
                return
            
            selected_files = [pkg_files[i] for i in selected_indices]
            dialog.destroy()
            
            # 开始下载选中的文件
            self._start_pkg_download(selected_files, local_dir)
        
        def select_all():
            listbox.select_set(0, tk.END)
        
        def cancel_download():
            dialog.destroy()
        
        # 全选按钮
        select_all_btn = tk.Button(button_frame, text="全选", command=select_all)
        select_all_btn.pack(side="left", padx=5)
        
        # 下载按钮
        download_btn = tk.Button(button_frame, text="下载选中文件", command=download_selected, bg="#4CAF50", fg="white")
        download_btn.pack(side="left", padx=5)
        
        # 取消按钮
        cancel_btn = tk.Button(button_frame, text="取消", command=cancel_download, bg="#f44336", fg="white")
        cancel_btn.pack(side="left", padx=5)
    
    def _start_pkg_download(self, selected_files, local_dir):
        """开始下载选中的PKG文件"""
        self.append_command_output(f"\n--- 开始下载PKG文件到: {local_dir} ---\n")
        logger.info(f"开始下载PKG文件，共{len(selected_files)}个文件")
        
        # 下载进度跟踪
        self.download_progress = {
            'total_files': len(selected_files),
            'completed_files': 0,
            'current_file': '',
            'current_progress': 0
        }
        
        # 显示下载进度区域
        self.master.after(0, lambda: self._show_download_progress())
        
        # 逐个下载文件
        for i, remote_file in enumerate(selected_files):
            filename = os.path.basename(remote_file)
            local_file_path = os.path.join(local_dir, filename)
            
            self.download_progress['current_file'] = filename
            self.append_command_output(f"正在下载: {filename}\n")
            
            # 使用SSHManager的download_file方法
            self.ssh_manager.download_file(
                remote_file, 
                local_file_path,
                self._download_progress_callback,
                lambda success, message, file_idx=i: self._download_completion_callback(success, message, file_idx)
            )
    
    def _download_progress_callback(self, bytes_xfered, bytes_total):
        """下载进度回调函数"""
        percent = (bytes_xfered / bytes_total) * 100 if bytes_total > 0 else 0
        self.download_progress['current_progress'] = percent
        
        # 更新界面显示
        current_file = self.download_progress['current_file']
        completed = self.download_progress['completed_files']
        total = self.download_progress['total_files']
        
        # 更新进度标签和进度条
        progress_text = f"下载进度: [{completed+1}/{total}] {current_file} - {percent:.1f}%"
        self.master.after(0, lambda: self._update_download_progress_display(progress_text, percent))
        
        # 同时更新状态栏
        self.master.after(0, lambda: self.update_status(progress_text, "blue"))
    
    def _download_completion_callback(self, success, message, file_index):
        """下载完成回调函数"""
        self.download_progress['completed_files'] += 1
        completed = self.download_progress['completed_files']
        total = self.download_progress['total_files']
        
        if success:
            self.master.after(0, lambda: self.append_command_output(f"✓ {message}"))
            logger.info(f"文件下载成功: {message}")
        else:
            self.master.after(0, lambda: self.append_command_output(f"✗ {message}"))
            logger.error(f"文件下载失败: {message}")
        
        # 检查是否所有文件都下载完成
        if completed >= total:
            self.master.after(0, lambda: self.update_status("PKG文件下载完成!", "green"))
            self.master.after(0, lambda: self.append_command_output(f"\n--- 所有PKG文件下载完成! 共{total}个文件 ---\n"))
            self.master.after(0, lambda: self._hide_download_progress())
            logger.info(f"所有PKG文件下载完成，共{total}个文件")
    
    def _show_download_progress(self):
        """显示下载进度区域"""
        self.download_progress_frame.grid()
        self.download_progress_label.config(text="准备下载...")
        self._update_progress_bar(0)
    
    def _hide_download_progress(self):
        """隐藏下载进度区域"""
        self.download_progress_frame.grid_remove()
    
    def _update_download_progress_display(self, progress_text, percent):
        """更新下载进度显示"""
        self.download_progress_label.config(text=progress_text)
        self._update_progress_bar(percent)
    
    def _update_progress_bar(self, percent):
        """更新进度条显示"""
        # 清除之前的进度条
        self.download_progress_bar.delete("all")
        
        # 获取进度条尺寸
        width = self.download_progress_bar.winfo_width()
        height = self.download_progress_bar.winfo_height()
        
        # 如果尺寸还未初始化，使用默认值
        if width <= 1:
            width = 300
        if height <= 1:
            height = 20
        
        # 计算进度条填充宽度
        fill_width = int(width * percent / 100)
        
        # 绘制背景
        self.download_progress_bar.create_rectangle(0, 0, width, height, fill="#f0f0f0", outline="")
        
        # 绘制进度
        if fill_width > 0:
            self.download_progress_bar.create_rectangle(0, 0, fill_width, height, fill="#4CAF50", outline="")
        
        # 绘制进度文本
        self.download_progress_bar.create_text(width//2, height//2, text=f"{percent:.1f}%", fill="black")
 
    def _command_output_callback(self, text):
        self.master.after(0, lambda: self.append_command_output(text)) # Update GUI from main thread


    # --- FTP Style File Management Methods ---
    def browse_select_local_path(self):
        """浏览并选择本地路径"""
        directory = filedialog.askdirectory(initialdir=self.local_path_var.get())
        if directory:
            self.local_path_var.set(directory)
            self.list_local_files()

    def list_local_files_event(self, event=None):
        """本地路径Entry回车事件"""
        self.list_local_files()

    def list_local_files(self, path=None):
        """列出本地指定路径的文件和文件夹"""
        if path is None:
            path = self.local_path_var.get()
        
        self.local_files_listbox.delete(0, tk.END)
        try:
            if not os.path.exists(path) or not os.path.isdir(path):
                self.local_files_listbox.insert(tk.END, "路径不存在或不是一个目录")
                return

            self.local_files_listbox.insert(tk.END, ".. (返回上级)")
            # 分离文件夹和文件，文件夹优先显示
            dirs = []
            files = []
            for item in os.listdir(path):
                if os.path.isdir(os.path.join(path, item)):
                    dirs.append(item)
                else:
                    files.append(item)
            
            for item in sorted(dirs):
                self.local_files_listbox.insert(tk.END, f"[D] {item}")
            for item in sorted(files):
                self.local_files_listbox.insert(tk.END, f"[F] {item}")

        except Exception as e:
            self.local_files_listbox.insert(tk.END, f"错误: {str(e)}")
            logger.error(f"列出本地文件错误 {path}: {e}")

    def on_local_file_double_click(self, event):
        """处理本地文件列表双击事件"""
        selection_index = self.local_files_listbox.curselection()
        if not selection_index:
            return
        
        selected_item_text = self.local_files_listbox.get(selection_index[0])
        current_path = self.local_path_var.get()

        if selected_item_text == ".. (返回上级)":
            new_path = os.path.dirname(current_path)
            self.local_path_var.set(new_path)
            self.list_local_files(new_path)
        elif selected_item_text.startswith("[D]"):
            dir_name = selected_item_text[4:]
            new_path = os.path.join(current_path, dir_name)
            if os.path.isdir(new_path):
                self.local_path_var.set(new_path)
                self.list_local_files(new_path)
        # 如果是文件[F]，双击不执行导航操作

    def list_remote_files_event(self, event=None):
        """远程路径Entry回车事件"""
        if self.is_connected:
            self.list_remote_files()
        else:
            messagebox.showwarning("未连接", "请先连接到SSH服务器。")

    def list_remote_files(self, path=None):
        """列出远程指定路径的文件和文件夹"""
        if not self.is_connected or not self.ssh_manager.client:
            self.remote_files_listbox.config(state="normal")
            self.remote_files_listbox.delete(0, tk.END)
            self.remote_files_listbox.insert(tk.END, "(未连接到SSH服务器)")
            # self.remote_files_listbox.config(state="disabled") # Keep it normal to show message
            return

        if path is None:
            path = self.remote_path_var.get()
        if not path: 
            path = "." 

        self.remote_files_listbox.config(state="normal")
        self.remote_files_listbox.delete(0, tk.END)
        self.remote_files_listbox.insert(tk.END, "(正在加载远程列表...)")
        
        threading.Thread(target=self._list_remote_files_thread, args=(path,), daemon=True).start()

    def _list_remote_files_thread(self, path):
        """在线程中执行远程ls命令并更新UI"""
        try:
            # 使用 'ls -Ap --group-directories-first' 来区分文件和目录, 目录优先
            # paramiko的exec_command不支持直接的shell特性如管道和重定向复杂组合，但ls本身可以
            # For simplicity, we use 'ls -Alp' and parse. '--group-directories-first' might not be on all systems.
            # 'ls -Ap' is more portable for basic dir/file distinction.
            # We will try to get a canonical path as well.
            command = f"cd {self.ssh_manager.escape_shell_arg(path)} && ls -Ap && pwd" # Get files and then current path
            logger.info(f"执行远程命令: {command}")
            stdin, stdout, stderr = self.ssh_manager.client.exec_command(command)
            output = stdout.read().decode(errors='replace').strip()
            error = stderr.read().decode(errors='replace').strip()

            self.master.after(0, lambda: self.remote_files_listbox.delete(0, tk.END)) # Clear loading message

            if error and "No such file or directory" not in error and "没有那个文件或目录" not in error:
                logger.error(f"列出远程文件错误 (stderr): {error}")
                self.master.after(0, lambda: self.remote_files_listbox.insert(tk.END, f"错误: {error}"))
                # Try to get pwd even on error if cd failed but connection is alive
                stdin_pwd, stdout_pwd, stderr_pwd = self.ssh_manager.client.exec_command("pwd")
                current_remote_path = stdout_pwd.read().decode(errors='replace').strip()
                if current_remote_path:
                     self.master.after(0, lambda: self.remote_path_var.set(current_remote_path))
                return

            lines = output.split('\n')
            if not lines:
                self.master.after(0, lambda: self.remote_files_listbox.insert(tk.END, "(目录为空或读取错误)"))
                return

            # Last line should be the result of pwd
            actual_path = path # Default to input path
            if lines and lines[-1].startswith("/"):
                 actual_path = lines.pop().strip()
            self.master.after(0, lambda: self.remote_path_var.set(actual_path))

            self.master.after(0, lambda: self.remote_files_listbox.insert(tk.END, ".. (返回上级)"))
            
            dirs = []
            files = []
            for item_line in lines:
                item = item_line.strip()
                if not item: continue
                if item.endswith('/'):
                    dirs.append(item[:-1]) # Remove trailing slash for display name
                else:
                    files.append(item)

            for item_name in sorted(dirs):
                self.master.after(0, lambda name=item_name: self.remote_files_listbox.insert(tk.END, f"[D] {name}"))
            for item_name in sorted(files):
                 self.master.after(0, lambda name=item_name: self.remote_files_listbox.insert(tk.END, f"[F] {name}"))
            
            if not dirs and not files and not error:
                 self.master.after(0, lambda: self.remote_files_listbox.insert(tk.END, "(目录为空)"))

        except Exception as e:
            logger.error(f"列出远程文件时发生异常 {path}: {e}")
            self.master.after(0, lambda: self.remote_files_listbox.delete(0, tk.END))
            self.master.after(0, lambda: self.remote_files_listbox.insert(tk.END, f"异常: {str(e)}"))
        finally:
            self.master.after(0, lambda: self.remote_files_listbox.config(state="normal"))

    def on_remote_file_double_click(self, event):
        """处理远程文件列表双击事件"""
        if not self.is_connected:
            return
        selection_index = self.remote_files_listbox.curselection()
        if not selection_index:
            return
        
        selected_item_text = self.remote_files_listbox.get(selection_index[0])
        current_path = self.remote_path_var.get()

        if selected_item_text == ".. (返回上级)":
            # Basic parent directory logic for POSIX paths
            new_path = os.path.dirname(current_path) if current_path != "/" else "/"
            self.remote_path_var.set(new_path)
            self.list_remote_files(new_path)
        elif selected_item_text.startswith("[D]"):
            dir_name = selected_item_text[4:]
            # Construct new path carefully for remote systems
            new_path = os.path.join(current_path, dir_name).replace("\\", "/") # Ensure POSIX separators
            if new_path.startswith("//") and len(new_path) > 1: new_path = new_path[1:] # Avoid double slash unless root
            if not new_path.startswith("/"): new_path = "/" + new_path # Ensure absolute if it became relative
            self.remote_path_var.set(new_path)
            self.list_remote_files(new_path)

    def _get_selected_local_items(self):
        """获取本地列表中的选定项 (完整路径和类型)"""
        selected_indices = self.local_files_listbox.curselection()
        if not selected_indices:
            return []
        
        local_base_path = self.local_path_var.get()
        items = []
        for index in selected_indices:
            item_text = self.local_files_listbox.get(index)
            if item_text == ".. (返回上级)": continue
            
            is_dir = item_text.startswith("[D]")
            name_part = item_text[4:] 
            full_path = os.path.join(local_base_path, name_part)
            items.append({"path": full_path, "name": name_part, "is_dir": is_dir})
        return items

    def _get_selected_remote_items(self):
        """获取远程列表中的选定项 (完整路径和类型)"""
        selected_indices = self.remote_files_listbox.curselection()
        if not selected_indices:
            return []

        remote_base_path = self.remote_path_var.get()
        items = []
        for index in selected_indices:
            item_text = self.remote_files_listbox.get(index)
            if item_text == ".. (返回上级)" or "(目录为空)" in item_text or "(正在加载" in item_text or "(未连接" in item_text : continue
            
            is_dir = item_text.startswith("[D]")
            name_part = item_text[4:]
            # Construct remote path carefully
            remote_item_path = os.path.join(remote_base_path, name_part).replace("\\", "/")
            if remote_item_path.startswith("//") and len(remote_item_path) > 1: remote_item_path = remote_item_path[1:]
            if not remote_item_path.startswith("/"): remote_item_path = "/" + remote_item_path
            items.append({"path": remote_item_path, "name": name_part, "is_dir": is_dir})
        return items

    def upload_selected_from_list(self):
        """从本地列表上传选定文件/文件夹到远程当前路径"""
        if not self.is_connected: messagebox.showerror("错误", "未连接到SSH服务器。"); return
        
        local_items = self._get_selected_local_items()
        if not local_items: messagebox.showwarning("选择文件", "请在左侧本地文件列表中选择要上传的文件或文件夹。"); return

        remote_target_dir = self.remote_path_var.get()
        self.append_command_output(f"\n--- 准备上传 {len(local_items)} 个项目到: {remote_target_dir} ---\n")
        logger.info(f"准备上传 {len(local_items)} 个项目到 {remote_target_dir}")
        self.file_transfer_progress_label.config(text="上传进度: --")
        self.file_transfer_progress_label.pack(fill=tk.X, padx=5, pady=(0,5))
        threading.Thread(target=self._upload_items_thread, args=(local_items, remote_target_dir), daemon=True).start()

    def _upload_items_thread(self, local_items, remote_dir):
        """在线程中执行上传操作"""
        for i, item_info in enumerate(local_items):
            local_item_path = item_info["path"]
            item_name = item_info["name"]
            remote_item_path = os.path.join(remote_dir, item_name).replace("\\", "/")
            if remote_item_path.startswith("//") and len(remote_item_path) > 1: remote_item_path = remote_item_path[1:]
            if not remote_item_path.startswith("/"): remote_item_path = "/" + remote_item_path

            self.master.after(0, lambda name=item_name: self.append_command_output(f"正在上传: {name}...\n"))
            current_op_prefix = f"上传 ({i+1}/{len(local_items)}) {item_name}"
            
            completion_cb = lambda success, msg, op_type="上传", name=item_name: \
                self.master.after(0, lambda s=success, m=msg, ot=op_type, n=name: \
                    self._item_transfer_completion(ot, s, m, n))
            progress_cb = lambda transferred, total, prefix=current_op_prefix: \
                self.master.after(0, lambda t=transferred, T=total, p=prefix: \
                    self._update_file_transfer_progress(t, T, p))

            if item_info["is_dir"]:
                self.ssh_manager.upload_directory(local_item_path, remote_item_path, progress_cb, completion_cb)
            else:
                self.ssh_manager.upload_file(local_item_path, remote_item_path, progress_cb, completion_cb)
        
        self.master.after(0, lambda: self.append_command_output("--- 所有项目上传尝试完毕 ---\n"))
        self.master.after(0, lambda: self.file_transfer_progress_label.pack_forget())
        self.master.after(0, self.list_remote_files) # 刷新远程列表

    def download_selected_from_list(self):
        """从远程列表下载选定文件/文件夹到本地当前路径"""
        if not self.is_connected: messagebox.showerror("错误", "未连接到SSH服务器。"); return

        remote_items = self._get_selected_remote_items()
        if not remote_items: messagebox.showwarning("选择文件", "请在右侧远程文件列表中选择要下载的文件或文件夹。"); return
        
        local_target_dir = self.local_path_var.get()
        self.append_command_output(f"\n--- 准备下载 {len(remote_items)} 个项目到: {local_target_dir} ---\n")
        logger.info(f"准备下载 {len(remote_items)} 个项目到 {local_target_dir}")
        self.file_transfer_progress_label.config(text="下载进度: --")
        self.file_transfer_progress_label.pack(fill=tk.X, padx=5, pady=(0,5))
        threading.Thread(target=self._download_items_thread, args=(remote_items, local_target_dir), daemon=True).start()

    def _download_items_thread(self, remote_items_info, local_dir):
        """在线程中执行下载操作"""
        for i, item_info in enumerate(remote_items_info):
            remote_item_path = item_info["path"]
            item_name = item_info["name"]
            local_item_path = os.path.join(local_dir, item_name)

            self.master.after(0, lambda name=item_name: self.append_command_output(f"正在下载: {name}...\n"))
            current_op_prefix = f"下载 ({i+1}/{len(remote_items_info)}) {item_name}"

            completion_cb = lambda success, msg, op_type="下载", name=item_name: \
                self.master.after(0, lambda s=success, m=msg, ot=op_type, n=name: \
                    self._item_transfer_completion(ot, s, m, n))
            progress_cb = lambda transferred, total, prefix=current_op_prefix: \
                self.master.after(0, lambda t=transferred, T=total, p=prefix: \
                    self._update_file_transfer_progress(t, T, p))

            if item_info["is_dir"]:
                self.ssh_manager.download_directory(remote_item_path, local_item_path, progress_cb, completion_cb)
            else:
                self.ssh_manager.download_file(remote_item_path, local_item_path, progress_cb, completion_cb)

        self.master.after(0, lambda: self.append_command_output("--- 所有项目下载尝试完毕 ---\n"))
        self.master.after(0, lambda: self.file_transfer_progress_label.pack_forget())
        self.master.after(0, self.list_local_files) # 刷新本地列表

    def delete_selected_remote_from_list(self):
        """从远程列表删除选定的文件或文件夹"""
        if not self.is_connected: messagebox.showerror("错误", "未连接到SSH服务器。"); return

        remote_items = self._get_selected_remote_items()
        if not remote_items: messagebox.showwarning("选择文件", "请在右侧远程文件列表中选择要删除的项目。"); return

        confirm_msg = f"确定要删除选定的 {len(remote_items)} 个远程项目吗？\n此操作不可恢复！\n"
        for item in remote_items[:5]: # Show first 5 items for confirmation
            confirm_msg += f"  - {item['name']}\n"
        if len(remote_items) > 5: confirm_msg += "  ...等等\n"
        if not messagebox.askyesno("确认删除", confirm_msg): return

        self.append_command_output(f"\n--- 准备删除 {len(remote_items)} 个远程项目 ---\n")
        logger.info(f"准备删除 {len(remote_items)} 个远程项目")
        # No progress bar for delete, it's usually fast or hard to track for recursive deletes via 'rm -rf'
        threading.Thread(target=self._delete_remote_items_thread, args=(remote_items,), daemon=True).start()

    def _delete_remote_items_thread(self, remote_items_info):
        """在线程中执行远程删除操作"""
        for item_info in remote_items_info:
            remote_item_path = item_info["path"]
            item_name = item_info["name"]
            is_dir = item_info["is_dir"]
            self.master.after(0, lambda name=item_name: self.append_command_output(f"正在删除远程: {name}...\n"))
            
            completion_cb = lambda success, msg, op_type="删除远程", name=item_name: \
                self.master.after(0, lambda s=success, m=msg, ot=op_type, n=name: \
                    self._item_transfer_completion(ot, s, m, n))
            
            # SSHManager's delete_remote_item should handle if it's a file or dir
            self.ssh_manager.delete_remote_item(remote_item_path, is_dir, completion_cb)

        self.master.after(0, lambda: self.append_command_output("--- 所有远程项目删除尝试完毕 ---\n"))
        self.master.after(0, self.list_remote_files) # 刷新远程列表

    def _update_file_transfer_progress(self, transferred_bytes, total_bytes, operation_prefix):
        """更新文件传输进度条/标签"""
        percent = (transferred_bytes / total_bytes) * 100 if total_bytes > 0 else 0
        self.file_transfer_progress_label.config(text=f"{operation_prefix}: {percent:.2f}% ({transferred_bytes}/{total_bytes})")

    def _item_transfer_completion(self, operation_type, success, message, item_name):
        """单个文件/目录传输或删除完成后的回调"""
        if success:
            self.append_command_output(f"{operation_type}成功: {item_name}\n")
            logger.info(f"项目{operation_type}成功: {item_name}")
        else:
            self.append_command_output(f"{operation_type}失败: {item_name} - {message}\n")
            logger.error(f"项目{operation_type}失败: {item_name} - {message}")

    # --- 旧的文件传输和删除方法 (可以注释掉或移除) ---
    # def browse_local_file(self): ... 
    # def browse_remote_path(self): ... 
    # def upload_selected_file(self): ... 
    # def delete_remote_file(self): ... 

    # --- Interactive Input Handling ---
    def _input_request_callback(self):
        """当远程脚本需要输入时调用此函数"""
        # 在主线程中显示输入对话框
        self.master.after(0, self._show_interactive_input_dialog)

    def _show_interactive_input_dialog(self):
        """显示交互式输入对话框"""
        # 创建一个更友好的输入对话框
        dialog = tk.Toplevel(self.master)
        dialog.title("远程脚本需要输入")
        dialog.geometry("400x200")
        dialog.transient(self.master)
        dialog.grab_set()
        
        # 居中显示
        dialog.geometry("+%d+%d" % (self.master.winfo_rootx() + 50, self.master.winfo_rooty() + 50))
        
        # 提示标签
        label = tk.Label(dialog, text="远程脚本正在等待输入，请在下方输入框中输入内容：", wraplength=350)
        label.pack(pady=10)
        
        # 输入框
        input_frame = tk.Frame(dialog)
        input_frame.pack(pady=10, padx=20, fill="x")
        
        input_entry = tk.Entry(input_frame, width=40)
        input_entry.pack(fill="x")
        input_entry.focus_set()
        
        # 按钮框架
        button_frame = tk.Frame(dialog)
        button_frame.pack(pady=10)
        
        def send_input():
            user_input = input_entry.get()
            if user_input is not None:
                self.ssh_manager.send_interactive_input(user_input)
                self.append_command_output(f">>> 用户输入: {user_input}\n")
                logger.info(f"发送用户输入: {user_input}")
            dialog.destroy()
        
        def cancel_input():
            # 发送Ctrl+C信号取消命令
            self.ssh_manager.send_interactive_input("\x03")
            self.append_command_output(">>> 用户取消输入，发送中断信号\n")
            logger.info("用户取消输入，发送中断信号")
            dialog.destroy()
        
        # 确定按钮
        ok_button = tk.Button(button_frame, text="发送", command=send_input, bg="#4CAF50", fg="white")
        ok_button.pack(side="left", padx=5)
        
        # 取消按钮
        cancel_button = tk.Button(button_frame, text="取消", command=cancel_input, bg="#f44336", fg="white")
        cancel_button.pack(side="left", padx=5)
        
        # 绑定回车键
        input_entry.bind("<Return>", lambda e: send_input())
        dialog.bind("<Escape>", lambda e: cancel_input())
    
    def _update_directory_dialog(self, text_widget, current_path, directory_content):
        """更新远程目录对话框的内容"""
        text_widget.config(state="normal")
        text_widget.delete("1.0", tk.END)
        text_widget.insert("1.0", directory_content)
        text_widget.config(state="disabled")
    
    def execute_unpack(self):
        """执行拆包命令"""
        if not self.ssh_manager.client:
            messagebox.showerror("错误", "未连接到SSH服务器。")
            logger.error("尝试执行拆包命令但未连接SSH")
            return
        
        # 清除之前的输出
        self.cmd_output_text.config(state="normal")
        self.cmd_output_text.delete(1.0, tk.END)
        self.cmd_output_text.config(state="disabled")
        
        # 执行拆包命令
        command = "./pkg_unpack.sh unpack -v"
        full_command = f"cd /home/<USER>/yf_repack_tool && {command}"
        self.append_command_output(f"\n--- 执行拆包命令: {full_command} ---\n")
        logger.info(f"执行拆包命令: {full_command}")
        
        # 使用交互式命令执行，支持用户输入
        self.ssh_manager.start_interactive_command(
            full_command, 
            self._command_output_callback,
            self._input_request_callback
        )
    
    def download_pkg_files(self):
        """下载远程PKG文件到本地"""
        if not self.ssh_manager.client:
            messagebox.showerror("错误", "未连接到SSH服务器。")
            logger.error("尝试下载PKG文件但未连接SSH")
            return
        
        # 选择本地保存目录
        local_dir = filedialog.askdirectory(title="选择PKG文件保存目录")
        if not local_dir:
            return  # 用户取消选择
        
        # 获取远程目录中的tar.gz文件列表
        remote_pkg_dir = "/home/<USER>/yf_repack_tool/out_pkg"
        
        def get_pkg_files():
            try:
                # 执行ls命令获取tar.gz文件列表
                stdin, stdout, stderr = self.ssh_manager.client.exec_command(f"ls -1 {remote_pkg_dir}/*.tar.gz 2>/dev/null || echo 'NO_FILES'")
                output = stdout.read().decode('utf-8').strip()
                error = stderr.read().decode('utf-8').strip()
                
                if output == 'NO_FILES' or not output:
                    self.master.after(0, lambda: messagebox.showinfo("提示", "远程目录中没有找到.tar.gz文件"))
                    return
                
                # 解析文件列表
                pkg_files = [line.strip() for line in output.split('\n') if line.strip()]
                
                if not pkg_files:
                    self.master.after(0, lambda: messagebox.showinfo("提示", "远程目录中没有找到.tar.gz文件"))
                    return
                
                # 在主线程中显示文件选择对话框
                self.master.after(0, lambda: self._show_pkg_file_selection_dialog(pkg_files, local_dir))
                
            except Exception as e:
                self.master.after(0, lambda: messagebox.showerror("错误", f"获取PKG文件列表失败: {str(e)}"))
                logger.error(f"获取PKG文件列表失败: {str(e)}")
        
        # 在单独线程中执行
        threading.Thread(target=get_pkg_files, daemon=True).start()
    
    def _show_pkg_file_selection_dialog(self, pkg_files, local_dir):
        """显示PKG文件选择对话框"""
        dialog = tk.Toplevel(self.master)
        dialog.title("选择要下载的PKG文件")
        dialog.geometry("500x400")
        dialog.transient(self.master)
        dialog.grab_set()
        
        # 居中显示
        dialog.geometry("+%d+%d" % (self.master.winfo_rootx() + 50, self.master.winfo_rooty() + 50))
        
        # 提示标签
        label = tk.Label(dialog, text=f"找到 {len(pkg_files)} 个PKG文件，请选择要下载的文件：")
        label.pack(pady=10)
        
        # 文件列表框架
        list_frame = tk.Frame(dialog)
        list_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # 创建列表框和滚动条
        listbox = tk.Listbox(list_frame, selectmode=tk.MULTIPLE)
        scrollbar = tk.Scrollbar(list_frame, orient="vertical", command=listbox.yview)
        listbox.config(yscrollcommand=scrollbar.set)
        
        listbox.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 添加文件到列表框
        for pkg_file in pkg_files:
            filename = os.path.basename(pkg_file)
            listbox.insert(tk.END, filename)
        
        # 按钮框架
        button_frame = tk.Frame(dialog)
        button_frame.pack(pady=10)
        
        def download_selected():
            selected_indices = listbox.curselection()
            if not selected_indices:
                messagebox.showwarning("提示", "请至少选择一个文件")
                return
            
            selected_files = [pkg_files[i] for i in selected_indices]
            dialog.destroy()
            
            # 开始下载选中的文件
            self._start_pkg_download(selected_files, local_dir)
        
        def select_all():
            listbox.select_set(0, tk.END)
        
        def cancel_download():
            dialog.destroy()
        
        # 全选按钮
        select_all_btn = tk.Button(button_frame, text="全选", command=select_all)
        select_all_btn.pack(side="left", padx=5)
        
        # 下载按钮
        download_btn = tk.Button(button_frame, text="下载选中文件", command=download_selected, bg="#4CAF50", fg="white")
        download_btn.pack(side="left", padx=5)
        
        # 取消按钮
        cancel_btn = tk.Button(button_frame, text="取消", command=cancel_download, bg="#f44336", fg="white")
        cancel_btn.pack(side="left", padx=5)
    
    def _start_pkg_download(self, selected_files, local_dir):
        """开始下载选中的PKG文件"""
        self.append_command_output(f"\n--- 开始下载PKG文件到: {local_dir} ---\n")
        logger.info(f"开始下载PKG文件，共{len(selected_files)}个文件")
        
        # 下载进度跟踪
        self.download_progress = {
            'total_files': len(selected_files),
            'completed_files': 0,
            'current_file': '',
            'current_progress': 0
        }
        
        # 显示下载进度区域
        self.master.after(0, lambda: self._show_download_progress())
        
        # 逐个下载文件
        for i, remote_file in enumerate(selected_files):
            filename = os.path.basename(remote_file)
            local_file_path = os.path.join(local_dir, filename)
            
            self.download_progress['current_file'] = filename
            self.append_command_output(f"正在下载: {filename}\n")
            
            # 使用SSHManager的download_file方法
            self.ssh_manager.download_file(
                remote_file, 
                local_file_path,
                self._download_progress_callback,
                lambda success, message, file_idx=i: self._download_completion_callback(success, message, file_idx)
            )
    
    def _download_progress_callback(self, bytes_xfered, bytes_total):
        """下载进度回调函数"""
        percent = (bytes_xfered / bytes_total) * 100 if bytes_total > 0 else 0
        self.download_progress['current_progress'] = percent
        
        # 更新界面显示
        current_file = self.download_progress['current_file']
        completed = self.download_progress['completed_files']
        total = self.download_progress['total_files']
        
        # 更新进度标签和进度条
        progress_text = f"下载进度: [{completed+1}/{total}] {current_file} - {percent:.1f}%"
        self.master.after(0, lambda: self._update_download_progress_display(progress_text, percent))
        
        # 同时更新状态栏
        self.master.after(0, lambda: self.update_status(progress_text, "blue"))
    
    def _download_completion_callback(self, success, message, file_index):
        """下载完成回调函数"""
        self.download_progress['completed_files'] += 1
        completed = self.download_progress['completed_files']
        total = self.download_progress['total_files']
        
        if success:
            self.master.after(0, lambda: self.append_command_output(f"✓ {message}"))
            logger.info(f"文件下载成功: {message}")
        else:
            self.master.after(0, lambda: self.append_command_output(f"✗ {message}"))
            logger.error(f"文件下载失败: {message}")
        
        # 检查是否所有文件都下载完成
        if completed >= total:
            self.master.after(0, lambda: self.update_status("PKG文件下载完成!", "green"))
            self.master.after(0, lambda: self.append_command_output(f"\n--- 所有PKG文件下载完成! 共{total}个文件 ---\n"))
            self.master.after(0, lambda: self._hide_download_progress())
            logger.info(f"所有PKG文件下载完成，共{total}个文件")
    
    def execute_pack(self):
        """执行打包命令"""
        if not self.ssh_manager.client:
            messagebox.showerror("错误", "未连接到SSH服务器。")
            logger.error("尝试执行打包命令但未连接SSH")
            return
        
        # 清除之前的输出
        self.cmd_output_text.config(state="normal")
        self.cmd_output_text.delete(1.0, tk.END)
        self.cmd_output_text.config(state="disabled")
        
        # 执行打包命令
        command = "./pkg_unpack.sh pack -v"
        full_command = f"cd /home/<USER>/yf_repack_tool && {command}"
        self.append_command_output(f"\n--- 执行打包命令: {full_command} ---\n")
        logger.info(f"执行打包命令: {full_command}")
        
        # 使用交互式命令执行，支持用户输入
        self.ssh_manager.start_interactive_command(
            full_command, 
            self._command_output_callback,
            self._input_request_callback
        )
    
    def download_pkg_files(self):
        """下载远程PKG文件到本地"""
        if not self.ssh_manager.client:
            messagebox.showerror("错误", "未连接到SSH服务器。")
            logger.error("尝试下载PKG文件但未连接SSH")
            return
        
        # 选择本地保存目录
        local_dir = filedialog.askdirectory(title="选择PKG文件保存目录")
        if not local_dir:
            return  # 用户取消选择
        
        # 获取远程目录中的tar.gz文件列表
        remote_pkg_dir = "/home/<USER>/yf_repack_tool/out_pkg"
        
        def get_pkg_files():
            try:
                # 执行ls命令获取tar.gz文件列表
                stdin, stdout, stderr = self.ssh_manager.client.exec_command(f"ls -1 {remote_pkg_dir}/*.tar.gz 2>/dev/null || echo 'NO_FILES'")
                output = stdout.read().decode('utf-8').strip()
                error = stderr.read().decode('utf-8').strip()
                
                if output == 'NO_FILES' or not output:
                    self.master.after(0, lambda: messagebox.showinfo("提示", "远程目录中没有找到.tar.gz文件"))
                    return
                
                # 解析文件列表
                pkg_files = [line.strip() for line in output.split('\n') if line.strip()]
                
                if not pkg_files:
                    self.master.after(0, lambda: messagebox.showinfo("提示", "远程目录中没有找到.tar.gz文件"))
                    return
                
                # 在主线程中显示文件选择对话框
                self.master.after(0, lambda: self._show_pkg_file_selection_dialog(pkg_files, local_dir))
                
            except Exception as e:
                self.master.after(0, lambda: messagebox.showerror("错误", f"获取PKG文件列表失败: {str(e)}"))
                logger.error(f"获取PKG文件列表失败: {str(e)}")
        
        # 在单独线程中执行
        threading.Thread(target=get_pkg_files, daemon=True).start()
    
    def _show_pkg_file_selection_dialog(self, pkg_files, local_dir):
        """显示PKG文件选择对话框"""
        dialog = tk.Toplevel(self.master)
        dialog.title("选择要下载的PKG文件")
        dialog.geometry("500x400")
        dialog.transient(self.master)
        dialog.grab_set()
        
        # 居中显示
        dialog.geometry("+%d+%d" % (self.master.winfo_rootx() + 50, self.master.winfo_rooty() + 50))
        
        # 提示标签
        label = tk.Label(dialog, text=f"找到 {len(pkg_files)} 个PKG文件，请选择要下载的文件：")
        label.pack(pady=10)
        
        # 文件列表框架
        list_frame = tk.Frame(dialog)
        list_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # 创建列表框和滚动条
        listbox = tk.Listbox(list_frame, selectmode=tk.MULTIPLE)
        scrollbar = tk.Scrollbar(list_frame, orient="vertical", command=listbox.yview)
        listbox.config(yscrollcommand=scrollbar.set)
        
        listbox.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 添加文件到列表框
        for pkg_file in pkg_files:
            filename = os.path.basename(pkg_file)
            listbox.insert(tk.END, filename)
        
        # 按钮框架
        button_frame = tk.Frame(dialog)
        button_frame.pack(pady=10)
        
        def download_selected():
            selected_indices = listbox.curselection()
            if not selected_indices:
                messagebox.showwarning("提示", "请至少选择一个文件")
                return
            
            selected_files = [pkg_files[i] for i in selected_indices]
            dialog.destroy()
            
            # 开始下载选中的文件
            self._start_pkg_download(selected_files, local_dir)
        
        def select_all():
            listbox.select_set(0, tk.END)
        
        def cancel_download():
            dialog.destroy()
        
        # 全选按钮
        select_all_btn = tk.Button(button_frame, text="全选", command=select_all)
        select_all_btn.pack(side="left", padx=5)
        
        # 下载按钮
        download_btn = tk.Button(button_frame, text="下载选中文件", command=download_selected, bg="#4CAF50", fg="white")
        download_btn.pack(side="left", padx=5)
        
        # 取消按钮
        cancel_btn = tk.Button(button_frame, text="取消", command=cancel_download, bg="#f44336", fg="white")
        cancel_btn.pack(side="left", padx=5)
    
    def _start_pkg_download(self, selected_files, local_dir):
        """开始下载选中的PKG文件"""
        self.append_command_output(f"\n--- 开始下载PKG文件到: {local_dir} ---\n")
        logger.info(f"开始下载PKG文件，共{len(selected_files)}个文件")
        
        # 下载进度跟踪
        self.download_progress = {
            'total_files': len(selected_files),
            'completed_files': 0,
            'current_file': '',
            'current_progress': 0
        }
        
        # 显示下载进度区域
        self.master.after(0, lambda: self._show_download_progress())
        
        # 逐个下载文件
        for i, remote_file in enumerate(selected_files):
            filename = os.path.basename(remote_file)
            local_file_path = os.path.join(local_dir, filename)
            
            self.download_progress['current_file'] = filename
            self.append_command_output(f"正在下载: {filename}\n")
            
            # 使用SSHManager的download_file方法
            self.ssh_manager.download_file(
                remote_file, 
                local_file_path,
                self._download_progress_callback,
                lambda success, message, file_idx=i: self._download_completion_callback(success, message, file_idx)
            )
    
    def _download_progress_callback(self, bytes_xfered, bytes_total):
        """下载进度回调函数"""
        percent = (bytes_xfered / bytes_total) * 100 if bytes_total > 0 else 0
        self.download_progress['current_progress'] = percent
        
        # 更新界面显示
        current_file = self.download_progress['current_file']
        completed = self.download_progress['completed_files']
        total = self.download_progress['total_files']
        
        # 更新进度标签和进度条
        progress_text = f"下载进度: [{completed+1}/{total}] {current_file} - {percent:.1f}%"
        self.master.after(0, lambda: self._update_download_progress_display(progress_text, percent))
        
        # 同时更新状态栏
        self.master.after(0, lambda: self.update_status(progress_text, "blue"))
    
    def _download_completion_callback(self, success, message, file_index):
        """下载完成回调函数"""
        self.download_progress['completed_files'] += 1
        completed = self.download_progress['completed_files']
        total = self.download_progress['total_files']
        
        if success:
            self.master.after(0, lambda: self.append_command_output(f"✓ {message}"))
            logger.info(f"文件下载成功: {message}")
        else:
            self.master.after(0, lambda: self.append_command_output(f"✗ {message}"))
            logger.error(f"文件下载失败: {message}")
        
        # 检查是否所有文件都下载完成
        if completed >= total:
            self.master.after(0, lambda: self.update_status("PKG文件下载完成!", "green"))
            self.master.after(0, lambda: self.append_command_output(f"\n--- 所有PKG文件下载完成! 共{total}个文件 ---\n"))
            self.master.after(0, lambda: self._hide_download_progress())
            logger.info(f"所有PKG文件下载完成，共{total}个文件")

    # --- File Transfer Methods ---
    def disconnect_ssh(self):
        """断开SSH连接"""
        if self.ssh_manager.client:
            self.ssh_manager.disconnect()
            self.is_connected = False
            self.update_status("已断开连接", "red")
            self.append_command_output("已断开与服务器的连接。\n")
            self.disable_buttons()  # 禁用按钮
            logger.info("SSH连接已断开")
        else:
            self.append_command_output("没有活动连接可断开。\n")
            logger.warning("尝试断开不存在的SSH连接")
    
    def browse_local_file(self):
        """浏览本地文件"""
        filepath = filedialog.askopenfilename()
        if filepath:
            self.local_file_entry.delete(0, tk.END)
            self.local_file_entry.insert(0, filepath)
            logger.info(f"选择本地文件: {filepath}")
    
    def browse_remote_path(self):
        """浏览远程路径（简化版本，显示当前目录内容）"""
        if not self.ssh_manager.client:
            messagebox.showerror("错误", "未连接到SSH服务器。")
            return
        
        # 获取当前远程路径
        current_path = self.remote_path_entry.get() or "/home/<USER>/yf_repack_tool/in_pkg"
        
        # 执行ls命令获取目录内容
        def list_remote_directory():
            try:
                stdin, stdout, stderr = self.ssh_manager.client.exec_command(f"ls -la {current_path}")
                output = stdout.read().decode('utf-8')
                error = stderr.read().decode('utf-8')
                
                if error:
                    self.master.after(0, lambda: messagebox.showerror("错误", f"无法访问远程路径: {error}"))
                    return
                
                # 在主线程中显示结果
                self.master.after(0, lambda: self.show_remote_directory_dialog(current_path, output))
                
            except Exception as e:
                self.master.after(0, lambda: messagebox.showerror("错误", f"获取远程目录失败: {str(e)}"))
                logger.error(f"获取远程目录失败: {str(e)}")
        
        # 在单独线程中执行
        threading.Thread(target=list_remote_directory, daemon=True).start()
    
    def show_remote_directory_dialog(self, current_path, directory_content):
        """显示远程目录内容对话框"""
        dialog = tk.Toplevel(self.master)
        dialog.title(f"远程目录: {current_path}")
        dialog.geometry("600x400")
        
        # 创建文本框显示目录内容
        text_frame = tk.Frame(dialog)
        text_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        text_widget = tk.Text(text_frame, wrap="none")
        scrollbar_y = tk.Scrollbar(text_frame, orient="vertical", command=text_widget.yview)
        scrollbar_x = tk.Scrollbar(text_frame, orient="horizontal", command=text_widget.xview)
        text_widget.config(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        text_widget.pack(side="left", fill="both", expand=True)
        scrollbar_y.pack(side="right", fill="y")
        scrollbar_x.pack(side="bottom", fill="x")
        
        text_widget.insert("1.0", directory_content)
        text_widget.config(state="disabled")
        
        # 按钮框架
        button_frame = tk.Frame(dialog)
        button_frame.pack(fill="x", padx=10, pady=5)
        
        # 路径输入框
        path_label = tk.Label(button_frame, text="路径:")
        path_label.pack(side="left")
        
        path_entry = tk.Entry(button_frame, width=40)
        path_entry.pack(side="left", padx=5)
        path_entry.insert(0, current_path)
        
        # 浏览按钮
        def browse_path():
            current_browse_path = path_entry.get()
            if current_browse_path:
                # 在新线程中获取目录内容
                def get_directory_content():
                    try:
                        stdin, stdout, stderr = self.ssh_manager.client.exec_command(f"ls -la {current_browse_path}")
                        output = stdout.read().decode('utf-8')
                        error = stderr.read().decode('utf-8')
                        
                        if error:
                            self.master.after(0, lambda: messagebox.showerror("错误", f"无法访问路径: {error}"))
                            return
                        
                        # 更新对话框内容
                        self.master.after(0, lambda: self._update_directory_dialog(text_widget, current_browse_path, output))
                        
                    except Exception as e:
                        self.master.after(0, lambda: messagebox.showerror("错误", f"获取目录失败: {str(e)}"))
                
                threading.Thread(target=get_directory_content, daemon=True).start()
        
        browse_button = tk.Button(button_frame, text="浏览", command=browse_path)
        browse_button.pack(side="left", padx=5)
        
        # 确定按钮
        def confirm_path():
            new_path = path_entry.get()
            if new_path:
                self.remote_path_entry.delete(0, tk.END)
                self.remote_path_entry.insert(0, new_path)
                logger.info(f"设置远程路径: {new_path}")
            dialog.destroy()
        
        confirm_button = tk.Button(button_frame, text="确定", command=confirm_path)
        confirm_button.pack(side="right", padx=5)
        
        cancel_button = tk.Button(button_frame, text="取消", command=dialog.destroy)
        cancel_button.pack(side="right")

    def upload_selected_file(self):
        if not self.ssh_manager.sftp:
            messagebox.showerror("Error", "SFTP client not initialized. Connect to SSH first.")
            return
        local_path = self.local_file_entry.get()
        remote_path_dir = self.remote_path_entry.get() # This should be a directory

        if not local_path or not remote_path_dir:
            messagebox.showwarning("Input Error", "Please select a local file and specify a remote directory.")
            return

        if not os.path.exists(local_path):
            messagebox.showerror("File Not Found", f"Local file does not exist: {local_path}")
            return
        
        if os.path.isdir(local_path):
            messagebox.showerror("Type Error", "Directory upload is not supported via this button. Use PKG upload or future feature.")
            return

        remote_file_name = os.path.basename(local_path)
        # Ensure remote_path_dir ends with a slash if it's not empty, then append filename
        if remote_path_dir and not remote_path_dir.endswith('/'):
            remote_full_path = remote_path_dir + '/' + remote_file_name
        else:
            remote_full_path = remote_path_dir + remote_file_name
        remote_full_path = remote_full_path.replace("\\", "/") # Ensure Linux path format


        self.progress_label.config(text="Upload Progress: 0%")
        self.update_status("Uploading...", "blue")
        self.append_command_output(f"Attempting to upload {local_path} to {remote_full_path}\n")
        self.ssh_manager.upload_file(local_path, remote_full_path,
                                     self._upload_progress_callback,
                                     self._upload_completion_callback)

    def upload_pkg_file(self):
        """
        Allows selecting a .tar.gz file and uploads it to /home/<USER>/yf_repack_tool/in_pkg.
        """
        if not self.ssh_manager.sftp:
            messagebox.showerror("Error", "SFTP client not initialized. Connect to SSH first.")
            return
        # Open file dialog, filtering for .tar.gz files
        filepath = filedialog.askopenfilename(
            title="Select PKG (tar.gz) File",
            filetypes=(("TAR.GZ files", "*.tar.gz"), ("All files", "*.*"))
        )

        if not filepath: # User cancelled
            return

        if not os.path.exists(filepath):
            messagebox.showerror("File Not Found", f"Local file does not exist: {filepath}")
            return

        if not filepath.lower().endswith(".tar.gz"):
            messagebox.showwarning("File Type Mismatch", "Please select a file ending with .tar.gz.")
            return

        remote_pkg_dir = "/home/<USER>/yf_repack_tool/in_pkg"
        # Get just the filename from the full path
        remote_file_name = os.path.basename(filepath)
        remote_full_path = os.path.join(remote_pkg_dir, remote_file_name).replace("\\", "/") # Ensure Linux path format

        # 重置PKG上传进度显示
        if hasattr(self, 'pkg_progress_label'):
            self.pkg_progress_label.config(text="PKG上传进度: 0%")
        self.update_status(f"Uploading PKG: {remote_file_name}...", "blue")
        self.append_command_output(f"Attempting to upload {filepath} to {remote_full_path}\n")

        self.ssh_manager.upload_file(filepath, remote_full_path,
                                     self._pkg_upload_progress_callback,  # 使用PKG专用进度回调
                                     self._pkg_upload_completion_callback)  # 使用PKG专用完成回调


    def _upload_progress_callback(self, bytes_xfered, bytes_total):
        """上传进度回调函数"""
        percent = (bytes_xfered / bytes_total) * 100 if bytes_total > 0 else 0
        # 确保progress_label存在并从主线程更新
        if hasattr(self, 'progress_label'):
             self.master.after(0, lambda: self.progress_label.config(text=f"上传进度: {percent:.1f}%"))
        else:  # 如果progress_label未定义或不可访问的后备方案
            print(f"上传进度: {percent:.1f}%")
    
    def _pkg_upload_progress_callback(self, bytes_xfered, bytes_total):
        """PKG上传进度回调函数"""
        percent = (bytes_xfered / bytes_total) * 100 if bytes_total > 0 else 0
        # 更新PKG上传进度标签
        if hasattr(self, 'pkg_progress_label'):
             self.master.after(0, lambda: self.pkg_progress_label.config(text=f"PKG上传进度: {percent:.1f}%"))
        else:
            print(f"PKG上传进度: {percent:.1f}%")


    def _upload_completion_callback(self, success, message):
        """上传完成回调函数"""
        # Ensure progress_label exists and is updated from the main thread
        if hasattr(self, 'progress_label'):
            self.master.after(0, lambda: self.progress_label.config(text="上传进度: 100%" if success else "上传失败"))

        if success:
            self.master.after(0, lambda: self.update_status("上传完成!", "green"))
            self.master.after(0, lambda: self.append_command_output(message))
            logger.info(f"文件上传成功: {message}")
        else:
            self.master.after(0, lambda: self.update_status("上传失败!", "red"))
            self.master.after(0, lambda: self.append_command_output(message))
            logger.error(f"文件上传失败: {message}")
    
    def _pkg_upload_completion_callback(self, success, message):
        """PKG上传完成回调函数"""
        if hasattr(self, 'pkg_progress_label'):
            self.master.after(0, lambda: self.pkg_progress_label.config(text="PKG上传进度: 100%" if success else "PKG上传失败"))
        
        if success:
            self.master.after(0, lambda: self.update_status("PKG上传完成!", "green"))
            self.master.after(0, lambda: self.append_command_output(message))
            logger.info(f"PKG文件上传成功: {message}")
            
            # 为上传的tar.gz文件添加可执行权限
            # 从消息中提取文件路径，或者使用默认路径构建
            if "to" in message:
                # 从消息中提取远程路径
                remote_path = message.split("to ")[-1].strip()
            else:
                # 使用默认路径（这种情况不应该发生，但作为后备）
                remote_path = "/home/<USER>/yf_repack_tool/in_pkg/*.tar.gz"
            
            chmod_command = f"chmod +x {remote_path}"
            self.master.after(0, lambda: self.append_command_output(f"正在为PKG文件添加可执行权限...\n"))
            logger.info(f"为PKG文件添加可执行权限: {chmod_command}")
            
            # 执行chmod命令
            self.ssh_manager.execute_command(chmod_command, self._chmod_callback)
        else:
            self.master.after(0, lambda: self.update_status("PKG上传失败!", "red"))
            self.master.after(0, lambda: self.append_command_output(message))
            logger.error(f"PKG文件上传失败: {message}")
    
    def _chmod_callback(self, text):
        """chmod命令执行回调函数"""
        self.master.after(0, lambda: self.append_command_output(f"权限设置: {text}"))
        if "Command Finished" in text:
            self.master.after(0, lambda: self.append_command_output("PKG文件可执行权限设置完成。\n"))
            logger.info("PKG文件可执行权限设置完成")


    # --- File Deletion Methods ---
    def delete_remote_file(self):
        if not self.ssh_manager.sftp:
            messagebox.showerror("Error", "SFTP client not initialized. Connect to SSH first.")
            return
        remote_path = self.delete_path_entry.get()
        if not remote_path:
            messagebox.showwarning("Input Error", "Please enter the remote path to delete.")
            return

        confirm = messagebox.askyesno("Confirm Deletion",
                                      f"Are you sure you want to delete '{remote_path}' on the server?\n"
                                      "This action cannot be undone.")
        if not confirm:
            return

        self.update_status("Deleting...", "blue")
        self.append_command_output(f"Attempting to delete: {remote_path}\n")
        self.ssh_manager.delete_file(remote_path, self._delete_completion_callback)

    def _delete_completion_callback(self, success, message):
        if success:
            self.master.after(0, lambda: self.update_status("Deletion Complete!", "green"))
            self.master.after(0, lambda: self.append_command_output(message))
        else:
            self.master.after(0, lambda: self.update_status("Deletion Failed!", "red"))
            self.master.after(0, lambda: self.append_command_output(message))

# --- Main Application Entry Point ---
if __name__ == "__main__":
    # This is needed if ssh_manager.py is a separate file in the same directory
    # And you want to run main.py directly.
    # You'll need to copy the SSHManager class into this file or ensure it's imported.
    # For this example, I'll assume SSHManager class is copied above App class or imported.

    # --- ssh_manager.py (Simulated backend SSH logic) ---
    # (If you're running as a single file, paste the SSHManager class here)
    import paramiko
    # import os # already imported
    import threading
    import time

    class SSHManager:
        def __init__(self):
            self.client = None
            self.sftp = None
            self.transport = None
            self.command_channel = None # For interactive command execution

        def connect(self, hostname, username, password, port=22):
            self.client = paramiko.SSHClient()
            self.client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            try:
                self.client.connect(hostname=hostname, username=username, password=password, port=port, timeout=10)
                self.sftp = self.client.open_sftp()
                print("SSH connection established.")
                return True
            except paramiko.AuthenticationException:
                print("Authentication failed. Check username and password.")
                return False
            except paramiko.SSHException as e:
                print(f"SSH connection failed: {e}")
                return False
            except Exception as e:
                print(f"An unexpected error occurred during connection: {e}")
                return False

        def disconnect(self):
            if self.sftp:
                self.sftp.close()
                self.sftp = None
            if self.client:
                self.client.close()
                self.client = None
            if self.command_channel:
                self.command_channel.close()
                self.command_channel = None
            print("SSH disconnected.")

        def execute_command(self, command, callback=None):
            if not self.client:
                print("Not connected to SSH server.")
                if callback:
                    callback("Error: Not connected to SSH server.\n")
                return

            def _run_command():
                try:
                    stdin, stdout, stderr = self.client.exec_command(command, get_pty=True)
                    # Read stdout line by line
                    for line in iter(stdout.readline, ""):
                        if callback:
                            callback(line)
                    # Read stderr line by line
                    for line in iter(stderr.readline, ""):
                        if callback:
                            callback(f"STDERR: {line}")
                    stdin.close()
                    stdout.close()
                    stderr.close()
                    if callback:
                        callback("\n--- Command Finished ---\n")
                except Exception as e:
                    error_msg = f"Error executing command: {e}\n"
                    print(error_msg)
                    if callback:
                        callback(error_msg)
            threading.Thread(target=_run_command, daemon=True).start()

        def start_interactive_command(self, command, output_callback=None, input_request_callback=None):
            if not self.client:
                if output_callback: output_callback("Error: Not connected to SSH server.\n")
                return

            def _run_interactive_command():
                try:
                    self.transport = self.client.get_transport()
                    self.command_channel = self.transport.open_session()
                    self.command_channel.get_pty()
                    self.command_channel.exec_command(command)

                    while not self.command_channel.exit_status_ready():
                        if self.command_channel.recv_ready():
                            output = self.command_channel.recv(1024).decode(errors='ignore')
                            if output and output_callback:
                                output_callback(output)
                                if ("password:" in output.lower() or \
                                    "yes/no" in output.lower() or \
                                    "continue?" in output.lower() or \
                                    output.endswith(": ")) and input_request_callback:
                                    input_request_callback()
                        if self.command_channel.recv_stderr_ready():
                            err = self.command_channel.recv_stderr(1024).decode(errors='ignore')
                            if err and output_callback:
                                output_callback(f"STDERR: {err}")
                        time.sleep(0.1)
                    
                    exit_status = self.command_channel.recv_exit_status()
                    if output_callback: output_callback(f"\n--- Command Finished with status: {exit_status} ---\n")
                    self.command_channel.close()
                    self.command_channel = None
                except Exception as e:
                    error_msg = f"Error during interactive command: {e}\n"
                    if output_callback: output_callback(error_msg)
            threading.Thread(target=_run_interactive_command, daemon=True).start()

        def send_interactive_input(self, data):
            if self.command_channel and self.command_channel.active:
                self.command_channel.sendall(data + '\n')
            else:
                print("Interactive channel not active.")

        def upload_file(self, local_path, remote_path, progress_callback=None, callback=None):
            if not self.sftp:
                print("SFTP client not initialized.")
                if callback:
                    callback(False, "Error: SFTP client not initialized.\n")
                return

            def _upload():
                try:
                    if os.path.isdir(local_path): # Should not happen with current GUI for 'upload_file'
                        raise ValueError("Directory upload not fully implemented for this sftp.put.")

                    total_size = os.path.getsize(local_path)
                    
                    # Paramiko's sftp.put callback is (bytes_xfered, total_size_of_current_chunk_or_file)
                    # For simplicity, we'll use the one Paramiko provides.
                    # If you need more granular control for very large files, custom chunking might be needed.
                    
                    # The progress_callback from paramiko's put is different from what one might expect.
                    # It's called with (current_bytes_transferred_for_this_call, total_bytes_to_be_transferred_for_this_call)
                    # For a single file, this works fine.
                    
                    p_callback = None
                    if progress_callback:
                        # Wrapper to match expected (bytes_xfered, bytes_total) where bytes_total is the file's total size
                        p_callback = lambda  xfered, total: progress_callback(xfered, total_size)


                    self.sftp.put(local_path, remote_path, callback=p_callback)
                    print(f"File uploaded: {local_path} to {remote_path}")
                    if callback:
                        callback(True, f"Successfully uploaded {local_path} to {remote_path}\n")
                except Exception as e:
                    print(f"Error uploading file: {e}")
                    if callback:
                        callback(False, f"Error uploading file: {e}\n")
            threading.Thread(target=_upload, daemon=True).start()

        def delete_file(self, remote_path, callback=None):
            if not self.sftp:
                print("SFTP client not initialized.")
                if callback: callback(False, "Error: SFTP client not initialized.\n")
                return

            def _delete_op(): # Run in thread to avoid GUI freeze if remote is slow
                try:
                    sftp_stat = None
                    try:
                        sftp_stat = self.sftp.stat(remote_path)
                    except IOError as e: # File not found is an IOError here
                        if "No such file" in str(e):
                            print(f"File/directory {remote_path} does not exist on remote.")
                            if callback: callback(False, f"Error: Remote path '{remote_path}' not found.\n")
                            return
                        else: # Other IOError
                            raise e
                    
                    if paramiko.sftp_client.SFTP_S_IFDIR & sftp_stat.st_mode: # It's a directory
                        print(f"Attempting to delete directory: {remote_path}. Using 'rm -rf'.")
                        # For directories, SFTP 'remove' often only works on empty dirs.
                        # 'rm -rf' is more robust but needs exec_command.
                        # This means delete_file's callback will be triggered by execute_command's callback
                        # if it's a directory. For simplicity, we'll handle its completion here.
                        # However, execute_command is async, so this isn't ideal.
                        # A better way would be to make this more complex and chain callbacks or use a blocking exec.
                        
                        # For now, we'll make a simple exec_command call and assume success for the callback.
                        # This is a simplification. Proper handling would require waiting for exec_command.
                        self.execute_command(f"rm -rf \"{remote_path}\"", 
                                             lambda out: print(f"Delete output for {remote_path}: {out.strip()}"))
                        # Assuming instant success for callback purposes here, which is not strictly true.
                        if callback: callback(True, f"Delete command for directory '{remote_path}' issued.\nCheck command output for status.\n")


                    else: # It's a file
                        self.sftp.remove(remote_path)
                        print(f"File deleted: {remote_path}")
                        if callback: callback(True, f"Successfully deleted file {remote_path}\n")

                except Exception as e:
                    print(f"Error deleting remote file/dir: {e}")
                    if callback: callback(False, f"Error deleting remote: {e}\n")
            
            # The delete operation itself (especially rmdir) can be blocking or take time.
            # However, sftp.remove is usually quick. `rm -rf` via exec_command is async.
            # For GUI responsiveness, direct SFTP ops that might block or network ops should be threaded.
            # Here, we'll thread the decision logic too.
            threading.Thread(target=_delete_op, daemon=True).start()
        
        def download_file(self, remote_path, local_path, progress_callback=None, callback=None):
            """从远程服务器下载文件到本地"""
            if not self.sftp:
                print("SFTP client not initialized.")
                if callback:
                    callback(False, "Error: SFTP client not initialized.\n")
                return

            def _download():
                try:
                    # 检查远程文件是否存在
                    try:
                        remote_stat = self.sftp.stat(remote_path)
                    except IOError as e:
                        if "No such file" in str(e):
                            error_msg = f"Remote file '{remote_path}' not found.\n"
                            print(error_msg)
                            if callback:
                                callback(False, error_msg)
                            return
                        else:
                            raise e
                    
                    # 确保本地目录存在
                    local_dir = os.path.dirname(local_path)
                    if local_dir and not os.path.exists(local_dir):
                        os.makedirs(local_dir)
                    
                    # 获取文件大小用于进度计算
                    total_size = remote_stat.st_size
                    
                    # 设置进度回调
                    p_callback = None
                    if progress_callback:
                        # Paramiko的get方法的回调参数是(bytes_transferred, total_bytes)
                        p_callback = lambda xfered, total: progress_callback(xfered, total_size)
                    
                    # 下载文件
                    self.sftp.get(remote_path, local_path, callback=p_callback)
                    print(f"File downloaded: {remote_path} to {local_path}")
                    
                    if callback:
                        callback(True, f"Successfully downloaded {os.path.basename(remote_path)} to {local_path}\n")
                        
                except Exception as e:
                    error_msg = f"Error downloading file: {e}\n"
                    print(error_msg)
                    if callback:
                        callback(False, error_msg)
            
            # 在单独线程中执行下载操作
            threading.Thread(target=_download, daemon=True).start()


    # 启动应用程序
    logger.info("启动宇泛升级包工具")
    root = tk.Tk()
    app = App(root)
    def on_closing():
        """优化的关闭逻辑，安全处理SSH连接断开"""
        print("正在关闭应用程序...")
        logger.info("应用程序关闭")
        # 安全断开SSH连接（如果存在）
        if hasattr(app, 'ssh_manager') and app.ssh_manager:
            try:
                app.ssh_manager.disconnect()
            except Exception as e:
                logger.warning(f"断开SSH连接时出现异常: {e}")
        root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()
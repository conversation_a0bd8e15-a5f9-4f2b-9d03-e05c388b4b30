# 宇泛升级包工具 v2.0

## 📖 项目简介

宇泛升级包工具的现代化重构版本，采用模块化架构设计，提供更好的用户体验和系统性能。

## 🚀 快速开始

### 安装依赖
```bash
pip install -r requirements.txt
```

### 启动应用
```bash
python main.py
```

或者双击 `start.bat` 文件启动。

## 📁 项目结构

```
yf_repack_tool/
├── main.py                   # 主应用程序入口
├── requirements.txt          # 依赖包列表
├── config.json              # 配置文件（自动生成）
├── core/                    # 核心模块
│   ├── ssh_manager.py       # SSH管理器
│   ├── config_manager.py    # 配置管理
│   ├── logger_config.py     # 日志系统
│   ├── performance_monitor.py # 性能监控
│   ├── security_manager.py  # 安全管理
│   ├── error_handler.py     # 错误处理
│   ├── ssh_connection_pool.py # SSH连接池
│   └── async_file_transfer.py # 异步文件传输
└── ui/                      # 用户界面模块
    ├── main_window.py       # 主窗口
    ├── ssh_panel.py         # SSH连接面板
    ├── command_panel.py     # 命令执行面板
    ├── file_manager.py      # 文件管理面板
    ├── status_bar.py        # 状态栏
    └── theme_manager.py     # 主题管理
```

## ✨ 主要特性

- **模块化架构**: 清晰的代码结构，易于维护和扩展
- **现代化UI**: 支持多种主题，美观的用户界面
- **配置管理**: 自动保存设置，支持密码安全存储
- **性能监控**: 实时监控系统性能和连接状态
- **错误处理**: 完善的异常处理和恢复机制
- **SSH连接池**: 优化连接管理，提升性能

## 🎨 主题支持

支持四种内置主题：
- **默认主题**: 经典Windows风格
- **现代主题**: 简洁现代设计
- **浅色主题**: 明亮清爽界面
- **深色主题**: 护眼深色模式

## 🔧 配置说明

首次运行会自动生成 `config.json` 配置文件，包含：
- SSH连接设置
- 界面主题配置
- 日志级别设置
- 窗口位置和大小

## 📝 使用说明

1. **SSH连接**: 在连接面板输入服务器信息
2. **命令执行**: 使用命令面板执行远程命令
3. **文件管理**: 通过文件管理器上传下载文件
4. **状态监控**: 查看状态栏了解系统状态

## 🐛 故障排除

如遇问题请检查：
1. `logs/` 目录中的日志文件
2. 确保所有依赖包已正确安装
3. 检查网络连接和SSH服务器状态

## 📄 版本说明

- **v2.0**: 当前版本，采用模块化架构设计，提供完整的功能和现代化的用户体验
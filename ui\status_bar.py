#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
状态栏组件
"""

import tkinter as tk
from tkinter import ttk
import time
import logging

logger = logging.getLogger(__name__)

class StatusBar(ttk.Frame):
    """状态栏组件"""
    
    def __init__(self, parent):
        super().__init__(parent, relief=tk.SUNKEN, borderwidth=1)
        self.create_widgets()
        self.start_updates()
        logger.info("状态栏初始化完成")
    
    def create_widgets(self):
        """创建状态栏组件"""
        # 主状态标签
        self.status_var = tk.StringVar(value="就绪")
        self.status_label = ttk.Label(self, textvariable=self.status_var)
        self.status_label.pack(side=tk.LEFT, padx=(5, 10))
        
        # 分隔符
        ttk.Separator(self, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=2)
        
        # 连接状态
        self.connection_var = tk.StringVar(value="未连接")
        self.connection_label = ttk.Label(self, textvariable=self.connection_var, font=("Arial", 8))
        self.connection_label.pack(side=tk.LEFT, padx=(5, 10))
        
        # 分隔符
        ttk.Separator(self, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=2)
        
        # 时间显示
        self.time_var = tk.StringVar()
        self.time_label = ttk.Label(self, textvariable=self.time_var, font=("Arial", 8))
        self.time_label.pack(side=tk.RIGHT, padx=(10, 5))
        
        # 进度条（传输时显示）
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            self, 
            variable=self.progress_var, 
            mode='determinate',
            length=100
        )
    
    def start_updates(self):
        """启动定时更新"""
        self.update_time()
    
    def update_time(self):
        """更新时间显示"""
        current_time = time.strftime("%Y-%m-%d %H:%M:%S")
        self.time_var.set(current_time)
        self.after(1000, self.update_time)
    
    def update_status(self, message: str, status_type: str = "info"):
        """更新状态信息"""
        self.status_var.set(message)
        
        # 根据状态类型设置颜色
        color_map = {
            "info": "black",
            "success": "green",
            "error": "red",
            "warning": "orange"
        }
        
        try:
            self.status_label.config(foreground=color_map.get(status_type, "black"))
        except:
            pass
        
        # 记录到日志
        if status_type == "error":
            logger.error(f"状态: {message}")
        elif status_type == "warning":
            logger.warning(f"状态: {message}")
        else:
            logger.info(f"状态: {message}")
    
    def update_connection_status(self, connected: bool, host: str = ""):
        """更新连接状态"""
        if connected:
            self.connection_var.set(f"已连接: {host}")
            try:
                self.connection_label.config(foreground="green")
            except:
                pass
        else:
            self.connection_var.set("未连接")
            try:
                self.connection_label.config(foreground="red")
            except:
                pass
    
    def show_progress(self, message: str = ""):
        """显示进度条"""
        if message:
            self.update_status(message, "info")
        
        self.progress_bar.pack(side=tk.RIGHT, padx=(5, 10), before=self.time_label)
        self.progress_var.set(0)
    
    def update_progress(self, value: float, message: str = ""):
        """更新进度"""
        self.progress_var.set(value)
        
        if message:
            self.update_status(f"{message} ({value:.1f}%)", "info")
    
    def hide_progress(self):
        """隐藏进度条"""
        self.progress_bar.pack_forget()
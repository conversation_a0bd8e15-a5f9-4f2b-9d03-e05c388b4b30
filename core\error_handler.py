#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
错误处理和恢复机制
提供统一的错误处理、日志记录和恢复策略
"""

import sys
import traceback
import threading
import time
import logging
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class ErrorInfo:
    """错误信息"""
    error_id: str
    timestamp: float
    severity: ErrorSeverity
    error_type: str
    message: str
    traceback_str: str
    context: Dict[str, Any]
    recovery_attempted: bool = False
    recovery_successful: bool = False

class ErrorHandler:
    """错误处理器"""
    
    def __init__(self):
        self.error_history: List[ErrorInfo] = []
        self.recovery_strategies: Dict[str, Callable] = {}
        self.error_callbacks: List[Callable] = []
        self.max_history = 1000
        
        # 自动恢复配置
        self.auto_recovery_enabled = True
        self.max_recovery_attempts = 3
        self.recovery_delay = 1.0
        
        # 错误统计
        self.error_counts: Dict[str, int] = {}
        
        logger.info("错误处理器初始化完成")
    
    def handle_error(self, 
                    error: Exception, 
                    context: Dict[str, Any] = None,
                    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                    auto_recover: bool = True) -> ErrorInfo:
        """处理错误
        
        Args:
            error: 异常对象
            context: 错误上下文信息
            severity: 错误严重程度
            auto_recover: 是否尝试自动恢复
            
        Returns:
            ErrorInfo: 错误信息对象
        """
        error_id = self._generate_error_id()
        error_type = type(error).__name__
        message = str(error)
        traceback_str = traceback.format_exc()
        
        error_info = ErrorInfo(
            error_id=error_id,
            timestamp=time.time(),
            severity=severity,
            error_type=error_type,
            message=message,
            traceback_str=traceback_str,
            context=context or {}
        )
        
        # 记录错误
        self._log_error(error_info)
        
        # 添加到历史记录
        self.error_history.append(error_info)
        if len(self.error_history) > self.max_history:
            self.error_history = self.error_history[-self.max_history:]
        
        # 更新错误统计
        self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1
        
        # 尝试自动恢复
        if auto_recover and self.auto_recovery_enabled:
            self._attempt_recovery(error_info)
        
        # 通知错误回调
        self._notify_error_callbacks(error_info)
        
        return error_info
    
    def _log_error(self, error_info: ErrorInfo):
        """记录错误到日志"""
        log_level = {
            ErrorSeverity.LOW: logging.INFO,
            ErrorSeverity.MEDIUM: logging.WARNING,
            ErrorSeverity.HIGH: logging.ERROR,
            ErrorSeverity.CRITICAL: logging.CRITICAL
        }.get(error_info.severity, logging.ERROR)
        
        logger.log(log_level, 
                  f"错误 [{error_info.error_id}]: {error_info.error_type} - {error_info.message}")
        
        if error_info.context:
            logger.log(log_level, f"错误上下文: {error_info.context}")
        
        if error_info.severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
            logger.log(log_level, f"错误堆栈:\\n{error_info.traceback_str}")
    
    def _attempt_recovery(self, error_info: ErrorInfo):
        """尝试错误恢复"""
        error_type = error_info.error_type
        
        if error_type in self.recovery_strategies:
            try:
                error_info.recovery_attempted = True
                recovery_func = self.recovery_strategies[error_type]
                
                logger.info(f"尝试恢复错误 [{error_info.error_id}]: {error_type}")
                
                # 在单独线程中执行恢复
                threading.Thread(
                    target=self._execute_recovery,
                    args=(recovery_func, error_info),
                    daemon=True
                ).start()
                
            except Exception as recovery_error:
                logger.error(f"恢复策略执行失败: {recovery_error}")
    
    def _execute_recovery(self, recovery_func: Callable, error_info: ErrorInfo):
        """执行恢复策略"""
        try:
            time.sleep(self.recovery_delay)
            result = recovery_func(error_info)
            
            if result:
                error_info.recovery_successful = True
                logger.info(f"错误恢复成功 [{error_info.error_id}]")
            else:
                logger.warning(f"错误恢复失败 [{error_info.error_id}]")
                
        except Exception as e:
            logger.error(f"恢复策略异常 [{error_info.error_id}]: {e}")
    
    def register_recovery_strategy(self, error_type: str, recovery_func: Callable):
        """注册恢复策略
        
        Args:
            error_type: 错误类型名称
            recovery_func: 恢复函数，接收ErrorInfo参数，返回bool表示是否成功
        """
        self.recovery_strategies[error_type] = recovery_func
        logger.info(f"注册恢复策略: {error_type}")
    
    def add_error_callback(self, callback: Callable):
        """添加错误回调函数"""
        self.error_callbacks.append(callback)
    
    def _notify_error_callbacks(self, error_info: ErrorInfo):
        """通知错误回调"""
        for callback in self.error_callbacks:
            try:
                callback(error_info)
            except Exception as e:
                logger.error(f"错误回调异常: {e}")
    
    def _generate_error_id(self) -> str:
        """生成错误ID"""
        import uuid
        return str(uuid.uuid4())[:8]
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计信息"""
        total_errors = len(self.error_history)
        recent_errors = [e for e in self.error_history if time.time() - e.timestamp < 3600]  # 最近1小时
        
        severity_counts = {}
        for error in self.error_history:
            severity = error.severity.value
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
        
        recovery_stats = {
            "attempted": sum(1 for e in self.error_history if e.recovery_attempted),
            "successful": sum(1 for e in self.error_history if e.recovery_successful)
        }
        
        return {
            "total_errors": total_errors,
            "recent_errors": len(recent_errors),
            "error_types": dict(self.error_counts),
            "severity_distribution": severity_counts,
            "recovery_statistics": recovery_stats,
            "registered_strategies": list(self.recovery_strategies.keys())
        }
    
    def get_recent_errors(self, count: int = 10) -> List[ErrorInfo]:
        """获取最近的错误"""
        return self.error_history[-count:] if self.error_history else []
    
    def clear_error_history(self):
        """清空错误历史"""
        self.error_history.clear()
        self.error_counts.clear()
        logger.info("错误历史已清空")

class GlobalExceptionHandler:
    """全局异常处理器"""
    
    def __init__(self, error_handler: ErrorHandler):
        self.error_handler = error_handler
        self.original_excepthook = sys.excepthook
    
    def install(self):
        """安装全局异常处理器"""
        sys.excepthook = self.handle_exception
        threading.excepthook = self.handle_thread_exception
        logger.info("全局异常处理器已安装")
    
    def uninstall(self):
        """卸载全局异常处理器"""
        sys.excepthook = self.original_excepthook
        logger.info("全局异常处理器已卸载")
    
    def handle_exception(self, exc_type, exc_value, exc_traceback):
        """处理主线程异常"""
        if issubclass(exc_type, KeyboardInterrupt):
            # 允许 Ctrl+C 正常退出
            self.original_excepthook(exc_type, exc_value, exc_traceback)
            return
        
        # 创建异常对象
        error = exc_value if exc_value else exc_type()
        
        # 处理错误
        self.error_handler.handle_error(
            error,
            context={"thread": "main", "type": "unhandled"},
            severity=ErrorSeverity.CRITICAL
        )
        
        # 调用原始处理器
        self.original_excepthook(exc_type, exc_value, exc_traceback)
    
    def handle_thread_exception(self, args):
        """处理线程异常"""
        exc_type, exc_value, exc_traceback, thread = args
        
        # 创建异常对象
        error = exc_value if exc_value else exc_type()
        
        # 处理错误
        self.error_handler.handle_error(
            error,
            context={"thread": thread.name if thread else "unknown", "type": "thread"},
            severity=ErrorSeverity.HIGH
        )

# 全局错误处理器实例
error_handler = ErrorHandler()
global_exception_handler = GlobalExceptionHandler(error_handler)

# 注册常见的恢复策略
def recover_connection_error(error_info: ErrorInfo) -> bool:
    """连接错误恢复策略"""
    logger.info("尝试重新建立连接...")
    # 这里可以实现具体的连接恢复逻辑
    return False

def recover_file_not_found(error_info: ErrorInfo) -> bool:
    """文件未找到错误恢复策略"""
    logger.info("尝试创建缺失的文件或目录...")
    # 这里可以实现具体的文件恢复逻辑
    return False

# 注册恢复策略
error_handler.register_recovery_strategy("ConnectionError", recover_connection_error)
error_handler.register_recovery_strategy("FileNotFoundError", recover_file_not_found)
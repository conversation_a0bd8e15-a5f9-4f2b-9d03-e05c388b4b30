import os
import paramiko
import logging
import stat
import threading
import time
from typing import Optional, Callable, Dict, Any
from contextlib import contextmanager

logger = logging.getLogger(__name__)

class SSHManager:
    """用于管理SSH连接和操作的类"""
    def __init__(self, host, port, username, password=None, key_filename=None):
        """初始化SSHManager实例。

        Args:
            host (str): SSH服务器主机名或IP地址。
            port (int): SSH服务器端口号。
            username (str): SSH登录用户名。
            password (str, optional): SSH登录密码。默认为None。
            key_filename (str, optional): SSH私钥文件路径。默认为None。
        """
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.key_filename = key_filename
        self.client = None
        self.sftp = None
        self._lock = threading.Lock()
        self._last_activity = time.time()
        self._connection_timeout = 300  # 5分钟超时
        self._interactive_channel = None

    def connect(self):
        """建立SSH连接。"""
        try:
            self.client = paramiko.SSHClient()
            self.client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            if self.key_filename:
                self.client.connect(self.host, port=self.port, username=self.username, key_filename=self.key_filename)
            elif self.password:
                self.client.connect(self.host, port=self.port, username=self.username, password=self.password)
            else:
                raise ValueError("必须提供密码或密钥文件进行SSH连接。")
            self.sftp = self.client.open_sftp()
            logger.info(f"成功连接到 {self.host}:{self.port}")
            return True
        except Exception as e:
            logger.error(f"SSH连接失败: {e}")
            self.client = None
            self.sftp = None
            return False

    def disconnect(self):
        """断开SSH连接。"""
        if self.sftp:
            self.sftp.close()
            self.sftp = None
            logger.info("SFTP会话已关闭。")
        if self.client:
            self.client.close()
            self.client = None
            logger.info("SSH连接已关闭。")

    def execute_command(self, command):
        """在远程服务器上执行命令。

        Args:
            command (str): 要执行的命令。

        Returns:
            tuple: 包含stdin, stdout, stderr的元组，如果未连接则返回None。
        """
        if not self.client:
            logger.error("未连接到SSH服务器，无法执行命令。")
            return None, None, None
        try:
            stdin, stdout, stderr = self.client.exec_command(command)
            return stdin, stdout, stderr
        except Exception as e:
            logger.error(f"执行命令 '{command}' 失败: {e}")
            return None, None, None

    def escape_shell_arg(self, arg):
        """转义shell参数以防止注入。
        一个非常基础的实现，对于复杂情况可能不够。
        对于POSIX shell，通常用单引号包围并转义内部的单引号。
        
        Args:
            arg (str): 需要转义的参数字符串。
            
        Returns:
            str: 转义后的参数字符串。
        """
        if not arg:
            return "''"
        # 将 arg 中的 ' 替换为 '\'' (单引号，反斜杠，单引号，单引号)
        # 然后用单引号将整个结果包围起来
        return "'" + arg.replace("'", "'\\''") + "'"

    def upload_file(self, local_path, remote_path, progress_callback=None, completion_callback=None):
        """上传单个文件到远程服务器。

        Args:
            local_path (str): 本地文件路径。
            remote_path (str): 远程文件路径。
            progress_callback (function, optional): 进度回调函数，接收 (transferred_bytes, total_bytes)。
            completion_callback (function, optional): 完成回调函数，接收 (success, message)。
        """
        if not self.sftp:
            logger.error("SFTP会话未建立，无法上传文件。")
            if completion_callback: completion_callback(False, "SFTP会话未建立")
            return
        try:
            logger.info(f"开始上传文件: {local_path} 到 {remote_path}")
            # 确保远程目录存在
            remote_dir = os.path.dirname(remote_path)
            if remote_dir:
                try:
                    self.sftp.stat(remote_dir)
                except FileNotFoundError:
                    self._mkdir_p_sftp(remote_dir)
            
            if progress_callback:
                self.sftp.put(local_path, remote_path, callback=lambda transferred, total: progress_callback(transferred, total))
            else:
                self.sftp.put(local_path, remote_path)
            logger.info(f"文件上传成功: {local_path} -> {remote_path}")
            if completion_callback: completion_callback(True, "上传成功")
        except Exception as e:
            logger.error(f"上传文件 {local_path} 到 {remote_path} 失败: {e}")
            if completion_callback: completion_callback(False, str(e))

    def _mkdir_p_sftp(self, remote_directory):
        """递归创建远程目录 (类似 mkdir -p)。"""
        if remote_directory == "/":
            # 根目录不需要创建
            return
        if remote_directory == "":
            # 空路径，通常意味着当前目录，不需要创建
            return
        try:
            self.sftp.stat(remote_directory)
        except FileNotFoundError:
            parent = os.path.dirname(remote_directory)
            if parent != "/" and parent != "." and parent != "": # 避免无限递归
                self._mkdir_p_sftp(parent)
            logger.info(f"创建远程目录: {remote_directory}")
            self.sftp.mkdir(remote_directory)

    def upload_directory(self, local_dir_path, remote_dir_path, progress_callback=None, completion_callback=None):
        """递归上传整个目录到远程服务器。

        Args:
            local_dir_path (str): 本地目录路径。
            remote_dir_path (str): 远程目标目录路径。
            progress_callback (function, optional): 针对每个文件的进度回调。
            completion_callback (function, optional): 针对每个文件/目录的完成回调。
        """
        if not self.sftp:
            logger.error("SFTP会话未建立，无法上传目录。")
            if completion_callback: completion_callback(False, "SFTP会话未建立")
            return

        logger.info(f"开始上传目录: {local_dir_path} 到 {remote_dir_path}")
        try:
            # 创建远程根目录 (如果不存在)
            try:
                self.sftp.stat(remote_dir_path)
            except FileNotFoundError:
                logger.info(f"远程目标目录 {remote_dir_path} 不存在，正在创建。")
                self._mkdir_p_sftp(remote_dir_path)
            
            for root, dirs, files in os.walk(local_dir_path):
                # 构造远程当前根目录
                relative_path = os.path.relpath(root, local_dir_path)
                if relative_path == ".":
                    current_remote_root = remote_dir_path
                else:
                    current_remote_root = os.path.join(remote_dir_path, relative_path).replace("\\", "/")

                # 创建远程子目录
                for dir_name in dirs:
                    remote_subdir = os.path.join(current_remote_root, dir_name).replace("\\", "/")
                    try:
                        self.sftp.stat(remote_subdir)
                    except FileNotFoundError:
                        logger.info(f"创建远程子目录: {remote_subdir}")
                        self.sftp.mkdir(remote_subdir)
                        if completion_callback: completion_callback(True, f"目录创建成功: {remote_subdir}") # 目录创建也回调
                    except Exception as e_mkdir:
                        logger.error(f"创建远程子目录 {remote_subdir} 失败: {e_mkdir}")
                        if completion_callback: completion_callback(False, f"创建目录 {remote_subdir} 失败: {e_mkdir}")

                # 上传文件
                for file_name in files:
                    local_file_path = os.path.join(root, file_name)
                    remote_file_path = os.path.join(current_remote_root, file_name).replace("\\", "/")
                    self.upload_file(local_file_path, remote_file_path, progress_callback, completion_callback)
            logger.info(f"目录上传完成: {local_dir_path} -> {remote_dir_path}")
            # 顶层目录的完成回调可以在调用此函数后处理，或通过另一种方式通知
        except Exception as e:
            logger.error(f"上传目录 {local_dir_path} 到 {remote_dir_path} 失败: {e}")
            if completion_callback: completion_callback(False, f"目录上传失败: {str(e)}") # 整体目录上传失败

    def download_file(self, remote_path, local_path, progress_callback=None, completion_callback=None):
        """从远程服务器下载单个文件。

        Args:
            remote_path (str): 远程文件路径。
            local_path (str): 本地保存路径。
            progress_callback (function, optional): 进度回调函数。
            completion_callback (function, optional): 完成回调函数。
        """
        if not self.sftp:
            logger.error("SFTP会话未建立，无法下载文件。")
            if completion_callback: completion_callback(False, "SFTP会话未建立")
            return
        try:
            logger.info(f"开始下载文件: {remote_path} 到 {local_path}")
            # 确保本地目录存在
            local_dir = os.path.dirname(local_path)
            if local_dir and not os.path.exists(local_dir):
                os.makedirs(local_dir, exist_ok=True)
                logger.info(f"创建本地目录: {local_dir}")

            if progress_callback:
                self.sftp.get(remote_path, local_path, callback=lambda transferred, total: progress_callback(transferred, total))
            else:
                self.sftp.get(remote_path, local_path)
            logger.info(f"文件下载成功: {remote_path} -> {local_path}")
            if completion_callback: completion_callback(True, "下载成功")
        except Exception as e:
            logger.error(f"下载文件 {remote_path} 到 {local_path} 失败: {e}")
            if completion_callback: completion_callback(False, str(e))

    def download_directory(self, remote_dir_path, local_dir_path, progress_callback=None, completion_callback=None):
        """递归下载整个远程目录到本地。

        Args:
            remote_dir_path (str): 远程目录路径。
            local_dir_path (str): 本地目标目录路径。
            progress_callback (function, optional): 针对每个文件的进度回调。
            completion_callback (function, optional): 针对每个文件/目录的完成回调。
        """
        if not self.sftp:
            logger.error("SFTP会话未建立，无法下载目录。")
            if completion_callback: completion_callback(False, "SFTP会话未建立")
            return

        logger.info(f"开始下载目录: {remote_dir_path} 到 {local_dir_path}")
        try:
            # 创建本地根目录
            if not os.path.exists(local_dir_path):
                os.makedirs(local_dir_path, exist_ok=True)
                logger.info(f"创建本地目录: {local_dir_path}")

            for item_attr in self.sftp.listdir_attr(remote_dir_path):
                item_name = item_attr.filename
                remote_item_path = os.path.join(remote_dir_path, item_name).replace("\\", "/")
                local_item_path = os.path.join(local_dir_path, item_name)

                if stat.S_ISDIR(item_attr.st_mode):
                    logger.info(f"发现远程子目录: {remote_item_path}，准备递归下载。")
                    self.download_directory(remote_item_path, local_item_path, progress_callback, completion_callback)
                    if completion_callback: completion_callback(True, f"目录下载(结构创建)成功: {local_item_path}")
                elif stat.S_ISREG(item_attr.st_mode):
                    self.download_file(remote_item_path, local_item_path, progress_callback, completion_callback)
                else:
                    logger.warning(f"跳过不支持的文件类型: {remote_item_path}")
            logger.info(f"目录下载完成: {remote_dir_path} -> {local_dir_path}")
        except Exception as e:
            logger.error(f"下载目录 {remote_dir_path} 到 {local_dir_path} 失败: {e}")
            if completion_callback: completion_callback(False, f"目录下载失败: {str(e)}")

    def delete_remote_item(self, remote_path, is_dir, completion_callback=None):
        """删除远程文件或目录。

        Args:
            remote_path (str): 要删除的远程文件或目录的路径。
            is_dir (bool): 如果为True，则作为目录删除 (递归)；否则作为文件删除。
            completion_callback (function, optional): 完成回调函数。
        """
        if not self.client and not self.sftp: # 需要client执行rm -rf，或sftp执行单个删除
            logger.error("未连接到SSH服务器，无法删除远程项目。")
            if completion_callback: completion_callback(False, "未连接")
            return
        
        try:
            if is_dir:
                logger.info(f"准备递归删除远程目录: {remote_path}")
                # 对于目录，使用 'rm -rf' 命令更可靠，SFTP的rmdir不能删除非空目录
                # 需要确保 self.client 存在
                if not self.client:
                    msg = "SSH client (for exec_command) is not available for directory deletion."
                    logger.error(msg)
                    if completion_callback: completion_callback(False, msg)
                    return
                
                command = f"rm -rf {self.escape_shell_arg(remote_path)}"
                logger.info(f"执行远程删除目录命令: {command}")
                stdin, stdout, stderr = self.client.exec_command(command)
                exit_status = stdout.channel.recv_exit_status() # 等待命令完成
                error_output = stderr.read().decode(errors='replace').strip()
                if exit_status == 0:
                    logger.info(f"远程目录删除成功: {remote_path}")
                    if completion_callback: completion_callback(True, "目录删除成功")
                else:
                    err_msg = f"删除远程目录 {remote_path} 失败。Exit status: {exit_status}. Error: {error_output}"
                    logger.error(err_msg)
                    if completion_callback: completion_callback(False, err_msg)
            else:
                # 删除单个文件
                if not self.sftp:
                    msg = "SFTP session is not available for file deletion."
                    logger.error(msg)
                    if completion_callback: completion_callback(False, msg)
                    return
                logger.info(f"准备删除远程文件: {remote_path}")
                self.sftp.remove(remote_path)
                logger.info(f"远程文件删除成功: {remote_path}")
                if completion_callback: completion_callback(True, "文件删除成功")
        except Exception as e:
            err_msg = f"删除远程项目 {remote_path} 失败: {e}"
            logger.error(err_msg)
            if completion_callback: completion_callback(False, str(e))

# 示例用法 (可选)
if __name__ == '__main__':
    logging.basicConfig(level=logging.INFO,
                        format='%(asctime)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s')
    
    # 替换为你的SSH服务器信息
    # TEST_HOST = "your_ssh_host"
    # TEST_PORT = 22
    # TEST_USER = "your_username"
    # TEST_PASS = "your_password"
    # TEST_KEY = "/path/to/your/private_key" # 如果使用密钥

    # ssh = SSHManager(TEST_HOST, TEST_PORT, TEST_USER, password=TEST_PASS)
    # # 或者使用密钥: ssh = SSHManager(TEST_HOST, TEST_PORT, TEST_USER, key_filename=TEST_KEY)
    
    # if ssh.connect():
    #     print("连接成功!")
        
    #     # 测试 escape_shell_arg
    #     print(f"Escaped 'my file name with spaces': {ssh.escape_shell_arg('my file name with spaces')}")
    #     print(f"Escaped 'file with 'single' quote': {ssh.escape_shell_arg("file with 'single' quote")}")

    #     # 测试执行命令
    #     # stdin, stdout, stderr = ssh.execute_command("ls -l /tmp")
    #     # if stdout:
    #     #     print("ls -l /tmp output:")
    #     #     print(stdout.read().decode())
    #     #     err = stderr.read().decode()
    #     #     if err: print(f"Error: {err}")

    #     # 测试上传文件
    #     # with open("local_test_file.txt", "w") as f:
    #     #     f.write("This is a test file for SSHManager upload.")
    #     # ssh.upload_file("local_test_file.txt", "/tmp/remote_test_file.txt", 
    #     #                 lambda t,tt: print(f"Upload progress: {t}/{tt} bytes"),
    #     #                 lambda s,m: print(f"Upload completion: Success={s}, Msg={m}"))
    #     # os.remove("local_test_file.txt")

    #     # 测试上传目录
    #     # if not os.path.exists("local_test_dir/subdir"):
    #     #    os.makedirs("local_test_dir/subdir")
    #     # with open("local_test_dir/file1.txt", "w") as f: f.write("file1")
    #     # with open("local_test_dir/subdir/file2.txt", "w") as f: f.write("file2")
    #     # ssh.upload_directory("local_test_dir", "/tmp/remote_test_dir",
    #     #                      lambda t,tt: print(f"Dir Upload progress: {t}/{tt} bytes (current file)"),
    #     #                      lambda s,m: print(f"Dir Upload completion (item): Success={s}, Msg={m}"))
    #     # import shutil
    #     # shutil.rmtree("local_test_dir")

    #     # 测试下载文件
    #     # ssh.download_file("/tmp/remote_test_file.txt", "downloaded_remote_file.txt",
    #     #                   lambda t,tt: print(f"Download progress: {t}/{tt} bytes"),
    #     #                   lambda s,m: print(f"Download completion: Success={s}, Msg={m}"))
    #     # if os.path.exists("downloaded_remote_file.txt"): os.remove("downloaded_remote_file.txt")

    #     # 测试下载目录
    #     # ssh.download_directory("/tmp/remote_test_dir", "downloaded_remote_dir",
    #     #                        lambda t,tt: print(f"Dir Download progress: {t}/{tt} bytes (current file)"),
    #     #                        lambda s,m: print(f"Dir Download completion (item): Success={s}, Msg={m}"))
    #     # if os.path.exists("downloaded_remote_dir"): shutil.rmtree("downloaded_remote_dir")

    #     # 测试删除文件
    #     # ssh.delete_remote_item("/tmp/remote_test_file.txt", is_dir=False, 
    #     #                        completion_callback=lambda s,m: print(f"Delete file completion: Success={s}, Msg={m}"))

    #     # 测试删除目录
    #     # ssh.delete_remote_item("/tmp/remote_test_dir", is_dir=True, 
    #     #                        completion_callback=lambda s,m: print(f"Delete dir completion: Success={s}, Msg={m}"))

    #     ssh.disconnect()
    # else:
    #     print("连接失败。")
    pass # Placeholder for example usage
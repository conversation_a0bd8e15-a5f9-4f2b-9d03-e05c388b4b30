#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SSH连接池管理器
提供连接复用、自动重连等功能
"""

import threading
import time
import queue
import logging
from typing import Dict, Optional, Callable, Any
from dataclasses import dataclass
from contextlib import contextmanager

from .ssh_manager import SSHManager

logger = logging.getLogger(__name__)

@dataclass
class ConnectionInfo:
    """连接信息"""
    host: str
    port: int
    username: str
    password: str = ""
    key_filename: str = ""
    
    def __hash__(self):
        return hash((self.host, self.port, self.username))
    
    def __eq__(self, other):
        if not isinstance(other, ConnectionInfo):
            return False
        return (self.host, self.port, self.username) == (other.host, other.port, other.username)

class SSHConnectionPool:
    """SSH连接池"""
    
    def __init__(self, max_connections: int = 5, connection_timeout: int = 300):
        self.max_connections = max_connections
        self.connection_timeout = connection_timeout
        
        # 连接池
        self.pools: Dict[ConnectionInfo, queue.Queue] = {}
        self.active_connections: Dict[ConnectionInfo, int] = {}
        self.connection_stats: Dict[ConnectionInfo, Dict[str, Any]] = {}
        
        # 线程锁
        self._lock = threading.RLock()
        
        # 清理线程
        self._cleanup_thread = None
        self._running = False
        
        # 回调函数
        self.connection_lost_callback: Optional[Callable] = None
        
        logger.info("SSH连接池初始化完成")
    
    def start(self):
        """启动连接池"""
        if self._running:
            return
        
        self._running = True
        # 创建停止事件用于快速响应停止信号
        self._stop_event = threading.Event()
        
        self._cleanup_thread = threading.Thread(
            target=self._cleanup_loop,
            name="SSHPool-Cleanup",
            daemon=True
        )
        self._cleanup_thread.start()
        
        logger.info("SSH连接池已启动")
    
    def stop(self, timeout: float = 1.0):
        """快速停止连接池"""
        logger.info(f"开始停止SSH连接池，超时: {timeout}秒")
        self._running = False
        
        # 设置停止事件
        if hasattr(self, '_stop_event'):
            self._stop_event.set()
        
        # 停止清理线程
        if self._cleanup_thread:
            self._cleanup_thread.join(timeout=0.5)
            if self._cleanup_thread.is_alive():
                logger.warning("SSH连接池清理线程未及时停止")
        
        # 并行关闭所有连接
        from concurrent.futures import ThreadPoolExecutor, as_completed, TimeoutError
        
        start_time = time.time()
        connections_to_close = []
        
        with self._lock:
            # 收集所有需要关闭的连接
            for conn_info, pool in self.pools.items():
                while not pool.empty():
                    try:
                        ssh_manager = pool.get_nowait()
                        connections_to_close.append(ssh_manager)
                    except queue.Empty:
                        break
        
        if connections_to_close:
            logger.info(f"并行关闭 {len(connections_to_close)} 个SSH连接")
            
            # 使用线程池并行关闭连接
            max_workers = min(5, len(connections_to_close))
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                futures = [
                    executor.submit(self._safe_disconnect, ssh_manager)
                    for ssh_manager in connections_to_close
                ]
                
                # 等待所有连接关闭，但有超时限制
                try:
                    for future in as_completed(futures, timeout=timeout):
                        try:
                            future.result()
                        except Exception as e:
                            logger.error(f"关闭SSH连接异常: {e}")
                except TimeoutError:
                    logger.warning("SSH连接关闭超时，强制继续")
        
        # 清理数据结构
        with self._lock:
            self.pools.clear()
            self.active_connections.clear()
            self.connection_stats.clear()
        
        elapsed_time = time.time() - start_time
        logger.info(f"SSH连接池已停止，耗时: {elapsed_time:.3f}秒")
    
    def _safe_disconnect(self, ssh_manager: SSHManager):
        """安全断开SSH连接"""
        try:
            ssh_manager.disconnect()
        except Exception as e:
            logger.warning(f"SSH断开连接异常: {e}")
    
    def force_stop(self):
        """强制停止连接池"""
        logger.warning("强制停止SSH连接池")
        self._running = False
        
        # 不等待清理线程
        if self._cleanup_thread:
            logger.warning("SSH连接池清理线程被强制终止")
        
        # 强制清理所有数据结构
        with self._lock:
            # 尝试快速关闭连接，但不等待
            for conn_info, pool in self.pools.items():
                while not pool.empty():
                    try:
                        ssh_manager = pool.get_nowait()
                        # 在单独线程中关闭，不阻塞主流程
                        threading.Thread(
                            target=self._safe_disconnect,
                            args=(ssh_manager,),
                            daemon=True
                        ).start()
                    except queue.Empty:
                        break
                    except Exception:
                        pass  # 忽略所有异常
            
            self.pools.clear()
            self.active_connections.clear()
            self.connection_stats.clear()
        
        logger.warning("SSH连接池强制停止完成")
    
    @contextmanager
    def get_connection(self, conn_info: ConnectionInfo):
        """获取连接（上下文管理器）
        
        Args:
            conn_info: 连接信息
            
        Yields:
            SSHManager: SSH管理器实例
        """
        ssh_manager = None
        try:
            ssh_manager = self._get_connection(conn_info)
            yield ssh_manager
        finally:
            if ssh_manager:
                self._return_connection(conn_info, ssh_manager)
    
    def _get_connection(self, conn_info: ConnectionInfo) -> Optional[SSHManager]:
        """获取连接"""
        with self._lock:
            # 初始化连接池
            if conn_info not in self.pools:
                self.pools[conn_info] = queue.Queue(maxsize=self.max_connections)
                self.active_connections[conn_info] = 0
                self.connection_stats[conn_info] = {
                    "created": 0,
                    "reused": 0,
                    "failed": 0,
                    "last_used": time.time()
                }
            
            pool = self.pools[conn_info]
            stats = self.connection_stats[conn_info]
            
            # 尝试从池中获取可用连接
            ssh_manager = None
            while not pool.empty():
                try:
                    candidate = pool.get_nowait()
                    if self._is_connection_alive(candidate):
                        ssh_manager = candidate
                        stats["reused"] += 1
                        break
                    else:
                        # 连接已失效，关闭它
                        try:
                            candidate.disconnect()
                        except:
                            pass
                except queue.Empty:
                    break
            
            # 如果没有可用连接，创建新连接
            if ssh_manager is None:
                if self.active_connections[conn_info] < self.max_connections:
                    ssh_manager = self._create_connection(conn_info)
                    if ssh_manager:
                        stats["created"] += 1
                        self.active_connections[conn_info] += 1
                    else:
                        stats["failed"] += 1
                else:
                    logger.warning(f"连接池已满: {conn_info.host}:{conn_info.port}")
                    return None
            
            if ssh_manager:
                stats["last_used"] = time.time()
            
            return ssh_manager
    
    def _return_connection(self, conn_info: ConnectionInfo, ssh_manager: SSHManager):
        """归还连接"""
        with self._lock:
            if conn_info not in self.pools:
                # 连接池已被清理
                try:
                    ssh_manager.disconnect()
                except:
                    pass
                return
            
            pool = self.pools[conn_info]
            
            # 检查连接是否仍然有效
            if self._is_connection_alive(ssh_manager):
                try:
                    pool.put_nowait(ssh_manager)
                except queue.Full:
                    # 池已满，关闭连接
                    try:
                        ssh_manager.disconnect()
                    except:
                        pass
                    self.active_connections[conn_info] -= 1
            else:
                # 连接已失效
                try:
                    ssh_manager.disconnect()
                except:
                    pass
                self.active_connections[conn_info] -= 1
                
                # 通知连接丢失
                if self.connection_lost_callback:
                    try:
                        self.connection_lost_callback(conn_info)
                    except Exception as e:
                        logger.error(f"连接丢失回调异常: {e}")
    
    def _create_connection(self, conn_info: ConnectionInfo) -> Optional[SSHManager]:
        """创建新连接"""
        try:
            ssh_manager = SSHManager(
                conn_info.host,
                conn_info.port,
                conn_info.username,
                password=conn_info.password,
                key_filename=conn_info.key_filename
            )
            
            if ssh_manager.connect():
                logger.info(f"创建新SSH连接: {conn_info.username}@{conn_info.host}:{conn_info.port}")
                return ssh_manager
            else:
                logger.error(f"SSH连接失败: {conn_info.username}@{conn_info.host}:{conn_info.port}")
                return None
                
        except Exception as e:
            logger.error(f"创建SSH连接异常: {e}")
            return None
    
    def _is_connection_alive(self, ssh_manager: SSHManager) -> bool:
        """检查连接是否存活"""
        try:
            if not ssh_manager.client:
                return False
            
            # 发送简单命令测试连接
            transport = ssh_manager.client.get_transport()
            if not transport or not transport.is_active():
                return False
            
            # 执行简单命令
            stdin, stdout, stderr = ssh_manager.execute_command("echo test")
            if stdout is None:
                return False
            
            result = stdout.read().decode().strip()
            return result == "test"
            
        except Exception as e:
            logger.debug(f"连接存活检查失败: {e}")
            return False
    
    def _cleanup_loop(self):
        """优化的清理循环，支持快速停止"""
        cleanup_interval = 60  # 清理间隔（秒）
        check_interval = 1     # 停止信号检查间隔（秒）
        
        while self._running:
            try:
                self._cleanup_idle_connections()
                
                # 分段等待，支持快速响应停止信号
                remaining_time = cleanup_interval
                while remaining_time > 0 and self._running:
                    wait_time = min(check_interval, remaining_time)
                    if hasattr(self, '_stop_event') and self._stop_event.wait(timeout=wait_time):
                        # 收到停止信号，立即退出
                        break
                    remaining_time -= wait_time
                    
            except Exception as e:
                logger.error(f"连接池清理异常: {e}")
                # 异常情况下也要检查停止信号
                if hasattr(self, '_stop_event') and self._stop_event.wait(timeout=1.0):
                    break
    
    def _cleanup_idle_connections(self):
        """清理空闲连接"""
        current_time = time.time()
        
        with self._lock:
            for conn_info, pool in list(self.pools.items()):
                stats = self.connection_stats[conn_info]
                
                # 如果连接池长时间未使用，清理它
                if current_time - stats["last_used"] > self.connection_timeout:
                    logger.info(f"清理空闲连接池: {conn_info.host}:{conn_info.port}")
                    
                    # 关闭池中的所有连接
                    while not pool.empty():
                        try:
                            ssh_manager = pool.get_nowait()
                            ssh_manager.disconnect()
                        except queue.Empty:
                            break
                        except Exception as e:
                            logger.error(f"关闭空闲连接失败: {e}")
                    
                    # 移除连接池
                    del self.pools[conn_info]
                    del self.active_connections[conn_info]
                    del self.connection_stats[conn_info]
    
    def get_pool_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        with self._lock:
            stats = {
                "total_pools": len(self.pools),
                "total_active_connections": sum(self.active_connections.values()),
                "pools": {}
            }
            
            for conn_info, pool_stats in self.connection_stats.items():
                key = f"{conn_info.username}@{conn_info.host}:{conn_info.port}"
                stats["pools"][key] = {
                    "pool_size": self.pools[conn_info].qsize(),
                    "active_connections": self.active_connections[conn_info],
                    "created": pool_stats["created"],
                    "reused": pool_stats["reused"],
                    "failed": pool_stats["failed"],
                    "last_used": pool_stats["last_used"]
                }
            
            return stats
    
    def set_connection_lost_callback(self, callback: Callable):
        """设置连接丢失回调"""
        self.connection_lost_callback = callback

# 全局连接池实例
ssh_pool = SSHConnectionPool()

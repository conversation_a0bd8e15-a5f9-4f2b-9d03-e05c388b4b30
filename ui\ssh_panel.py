#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SSH连接面板
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import logging
from typing import Optional, Callable

from core.config_manager import config
from core.security_manager import security_manager
from core.ssh_manager import SSHManager

logger = logging.getLogger(__name__)

class SSHPanel(ttk.LabelFrame):
    """SSH连接面板"""
    
    def __init__(self, parent):
        super().__init__(parent, text="SSH连接配置", padding=10)
        
        self.ssh_manager: Optional[SSHManager] = None
        self.is_connected = False
        
        # 回调函数
        self.connection_callback: Optional[Callable] = None
        self.status_callback: Optional[Callable] = None
        
        self.create_widgets()
        self.load_config()
        self.update_button_states()  # 确保初始按钮状态正确
        
        logger.info("SSH面板初始化完成")
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建输入框架
        input_frame = ttk.Frame(self)
        input_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 主机地址
        ttk.Label(input_frame, text="主机:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.host_var = tk.StringVar()
        self.host_entry = ttk.Entry(input_frame, textvariable=self.host_var, width=20)
        self.host_entry.grid(row=0, column=1, sticky=tk.EW, padx=(0, 10))
        
        # 端口
        ttk.Label(input_frame, text="端口:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        self.port_var = tk.StringVar()
        self.port_entry = ttk.Entry(input_frame, textvariable=self.port_var, width=8)
        self.port_entry.grid(row=0, column=3, sticky=tk.EW, padx=(0, 10))
        
        # 用户名
        ttk.Label(input_frame, text="用户名:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5))
        self.username_var = tk.StringVar()
        self.username_entry = ttk.Entry(input_frame, textvariable=self.username_var, width=20)
        self.username_entry.grid(row=1, column=1, sticky=tk.EW, padx=(0, 10))
        
        # 密码
        ttk.Label(input_frame, text="密码:").grid(row=1, column=2, sticky=tk.W, padx=(0, 5))
        self.password_var = tk.StringVar()
        self.password_entry = ttk.Entry(input_frame, textvariable=self.password_var, 
                                       show="*", width=20)
        self.password_entry.grid(row=1, column=3, sticky=tk.EW, padx=(0, 10))
        
        # 记住密码选项
        self.remember_password_var = tk.BooleanVar()
        self.remember_checkbox = ttk.Checkbutton(
            input_frame, 
            text="记住密码", 
            variable=self.remember_password_var,
            command=self.on_remember_password_changed
        )
        self.remember_checkbox.grid(row=2, column=0, columnspan=2, sticky=tk.W, pady=(5, 0))
        
        # 默认远程目录
        ttk.Label(input_frame, text="默认目录:").grid(row=3, column=0, sticky=tk.W, padx=(0, 5))
        self.default_remote_path_var = tk.StringVar()
        self.default_remote_path_entry = ttk.Entry(input_frame, textvariable=self.default_remote_path_var, width=40)
        self.default_remote_path_entry.grid(row=3, column=1, columnspan=3, sticky=tk.EW, padx=(0, 10), pady=(5, 0))
        
        # 设置列权重
        input_frame.columnconfigure(1, weight=1)
        input_frame.columnconfigure(3, weight=1)
        
        # 按钮框架
        button_frame = ttk.Frame(self)
        button_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 连接按钮
        self.connect_btn = ttk.Button(
            button_frame, 
            text="连接", 
            command=self.connect_ssh,
            state=tk.NORMAL  # 确保初始状态为可用
        )
        self.connect_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 断开连接按钮
        self.disconnect_btn = ttk.Button(
            button_frame, 
            text="断开连接", 
            command=self.disconnect_ssh,
            state=tk.DISABLED  # 确保初始状态为禁用
        )
        self.disconnect_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 测试连接按钮
        self.test_btn = ttk.Button(
            button_frame, 
            text="测试连接", 
            command=self.test_connection
        )
        self.test_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 连接状态
        self.status_var = tk.StringVar(value="状态: 未连接")
        self.status_label = ttk.Label(button_frame, textvariable=self.status_var)
        self.status_label.pack(side=tk.RIGHT)
        
        # 进度条（连接时显示）
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            self, 
            variable=self.progress_var, 
            mode='indeterminate'
        )
        # 初始隐藏
        
    def load_config(self):
        """加载配置"""
        ssh_config = config.get_ssh_config()
        
        self.host_var.set(ssh_config.get("host", ""))
        self.port_var.set(str(ssh_config.get("port", 22)))
        self.username_var.set(ssh_config.get("username", ""))
        self.remember_password_var.set(ssh_config.get("remember_password", False))
        self.default_remote_path_var.set(ssh_config.get("default_remote_path", "/home/<USER>/yf_2113_repack"))
        
        # 如果记住密码，尝试从安全存储中获取
        if self.remember_password_var.get():
            username = self.username_var.get()
            host = self.host_var.get()
            if username and host:
                stored_password = security_manager.get_stored_password(
                    f"{username}@{host}"
                )
                if stored_password:
                    self.password_var.set(stored_password)
    
    def save_config(self):
        """保存配置"""
        config.set_ssh_config(
            host=self.host_var.get(),
            port=int(self.port_var.get()) if self.port_var.get().isdigit() else 22,
            username=self.username_var.get(),
            remember_password=self.remember_password_var.get(),
            default_remote_path=self.default_remote_path_var.get()
        )
        
        config.save_config()
    
    def on_remember_password_changed(self):
        """记住密码选项变化处理"""
        username = self.username_var.get()
        host = self.host_var.get()
        password = self.password_var.get()
        
        if not username or not host:
            return
        
        service_key = f"{username}@{host}"
        
        if self.remember_password_var.get():
            # 保存密码到安全存储
            if password:
                success = security_manager.store_password_securely(service_key, password)
                if success:
                    self.update_status("密码已安全保存", "info")
                else:
                    self.update_status("密码保存失败", "error")
                    self.remember_password_var.set(False)
        else:
            # 删除保存的密码
            security_manager.delete_stored_password(service_key)
            self.update_status("已删除保存的密码", "info")
    
    def connect_ssh(self):
        """连接SSH"""
        # 验证输入
        if not self.validate_input():
            return
        
        # 保存配置
        self.save_config()
        
        # 显示进度条
        self.show_progress("正在连接...")
        
        # 在后台线程中连接
        threading.Thread(target=self._connect_thread, daemon=True).start()
    
    def _connect_thread(self):
        """连接线程"""
        try:
            host = self.host_var.get()
            port = int(self.port_var.get())
            username = self.username_var.get()
            password = self.password_var.get()
            
            # 创建SSH管理器
            self.ssh_manager = SSHManager(host, port, username, password=password)
            
            # 尝试连接
            if self.ssh_manager.connect():
                self.is_connected = True
                
                # 在主线程中更新UI
                self.after(0, lambda: self._on_connect_success())
                
                # 保存密码（如果选择记住）
                if self.remember_password_var.get():
                    service_key = f"{username}@{host}"
                    security_manager.store_password_securely(service_key, password)
                
            else:
                self.after(0, lambda: self._on_connect_failed("连接失败"))
                
        except Exception as e:
            self.after(0, lambda: self._on_connect_failed(str(e)))
    
    def _on_connect_success(self):
        """连接成功处理"""
        self.hide_progress()
        self.update_status(f"已连接到 {self.host_var.get()}", "success")
        
        # 更新按钮状态
        self.update_button_states()
        
        # 通知其他组件
        logger.info(f"准备通知其他组件连接成功，回调函数: {self.connection_callback is not None}")
        if self.connection_callback:
            logger.info("调用连接回调函数")
            self.connection_callback(True, self.ssh_manager)
        else:
            logger.warning("连接回调函数为None，无法通知其他组件")
        
        logger.info(f"SSH连接成功: {self.username_var.get()}@{self.host_var.get()}")
    
    def _on_connect_failed(self, error_message: str):
        """连接失败处理"""
        self.hide_progress()
        self.update_status(f"连接失败: {error_message}", "error")
        
        # 显示错误对话框
        messagebox.showerror("连接失败", f"无法连接到SSH服务器:\n{error_message}")
        
        logger.error(f"SSH连接失败: {error_message}")
    
    def disconnect_ssh(self):
        """断开SSH连接"""
        if self.ssh_manager:
            self.ssh_manager.disconnect()
            self.ssh_manager = None
        
        self.is_connected = False
        self.update_status("已断开连接", "info")
        
        # 更新按钮状态
        self.update_button_states()
        
        # 通知其他组件
        if self.connection_callback:
            self.connection_callback(False, None)
        
        logger.info("SSH连接已断开")
    
    def test_connection(self):
        """测试连接"""
        if not self.validate_input():
            return
        
        self.show_progress("正在测试连接...")
        threading.Thread(target=self._test_connection_thread, daemon=True).start()
    
    def _test_connection_thread(self):
        """测试连接线程"""
        try:
            host = self.host_var.get()
            port = int(self.port_var.get())
            username = self.username_var.get()
            password = self.password_var.get()
            
            # 创建临时SSH管理器进行测试
            test_manager = SSHManager(host, port, username, password=password)
            
            if test_manager.connect():
                test_manager.disconnect()
                self.after(0, lambda: self._on_test_success())
            else:
                self.after(0, lambda: self._on_test_failed("连接测试失败"))
                
        except Exception as e:
            self.after(0, lambda: self._on_test_failed(str(e)))
    
    def _on_test_success(self):
        """测试成功"""
        self.hide_progress()
        self.update_status("连接测试成功", "success")
        messagebox.showinfo("测试结果", "连接测试成功！")
    
    def _on_test_failed(self, error_message: str):
        """测试失败"""
        self.hide_progress()
        self.update_status(f"连接测试失败: {error_message}", "error")
        messagebox.showerror("测试结果", f"连接测试失败:\n{error_message}")
    
    def validate_input(self) -> bool:
        """验证输入"""
        if not self.host_var.get().strip():
            messagebox.showerror("输入错误", "请输入主机地址")
            self.host_entry.focus()
            return False
        
        if not self.port_var.get().strip() or not self.port_var.get().isdigit():
            messagebox.showerror("输入错误", "请输入有效的端口号")
            self.port_entry.focus()
            return False
        
        if not self.username_var.get().strip():
            messagebox.showerror("输入错误", "请输入用户名")
            self.username_entry.focus()
            return False
        
        if not self.password_var.get().strip():
            messagebox.showerror("输入错误", "请输入密码")
            self.password_entry.focus()
            return False
        
        return True
    
    def show_progress(self, message: str):
        """显示进度条"""
        self.update_status(message, "info")
        self.progress_bar.pack(fill=tk.X, pady=(5, 0))
        self.progress_bar.start()
    
    def hide_progress(self):
        """隐藏进度条"""
        self.progress_bar.stop()
        self.progress_bar.pack_forget()
    
    def update_status(self, message: str, status_type: str = "info"):
        """更新状态"""
        self.status_var.set(f"状态: {message}")
        
        # 设置状态颜色
        color_map = {
            "info": "blue",
            "success": "green",
            "error": "red",
            "warning": "orange"
        }
        
        # 更新状态标签颜色（如果支持）
        try:
            self.status_label.config(foreground=color_map.get(status_type, "black"))
        except:
            pass
        
        # 通知状态栏
        if self.status_callback:
            self.status_callback(message, status_type)
    
    def set_connection_callback(self, callback: Callable):
        """设置连接状态回调"""
        self.connection_callback = callback
        logger.info(f"SSH面板连接回调已设置: {callback is not None}")
    
    def set_status_callback(self, callback: Callable):
        """设置状态更新回调"""
        self.status_callback = callback
    
    def update_button_states(self):
        """更新按钮状态"""
        if self.is_connected:
            self.connect_btn.config(state=tk.DISABLED)
            self.disconnect_btn.config(state=tk.NORMAL)
        else:
            self.connect_btn.config(state=tk.NORMAL)
            self.disconnect_btn.config(state=tk.DISABLED)
    
    def cleanup(self):
        """清理资源"""
        if self.is_connected:
            self.disconnect_ssh()
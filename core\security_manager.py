#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全管理模块
提供密码加密、安全存储等功能
"""

import os
import base64
import hashlib
import secrets
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from typing import Optional, Dict, Any
import logging
import keyring
from pathlib import Path

logger = logging.getLogger(__name__)

class SecurityManager:
    """安全管理器"""
    
    def __init__(self, app_name: str = "YufanUpgradeTool"):
        self.app_name = app_name
        self.key_file = Path("security.key")
        self._encryption_key = None
    
    def _get_encryption_key(self, password: Optional[str] = None) -> bytes:
        """获取加密密钥"""
        if self._encryption_key:
            return self._encryption_key
        
        if password:
            # 使用用户提供的密码生成密钥
            salt = self._get_or_create_salt()
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        else:
            # 使用机器特定的密钥
            key = self._get_or_create_machine_key()
        
        self._encryption_key = key
        return key
    
    def _get_or_create_salt(self) -> bytes:
        """获取或创建盐值"""
        salt_file = Path("security.salt")
        
        if salt_file.exists():
            with open(salt_file, 'rb') as f:
                return f.read()
        else:
            salt = os.urandom(16)
            with open(salt_file, 'wb') as f:
                f.write(salt)
            return salt
    
    def _get_or_create_machine_key(self) -> bytes:
        """获取或创建机器特定密钥"""
        if self.key_file.exists():
            with open(self.key_file, 'rb') as f:
                return f.read()
        else:
            key = Fernet.generate_key()
            with open(self.key_file, 'wb') as f:
                f.write(key)
            # 设置文件权限（仅所有者可读写）
            os.chmod(self.key_file, 0o600)
            return key
    
    def encrypt_password(self, password: str, master_password: Optional[str] = None) -> str:
        """加密密码
        
        Args:
            password: 要加密的密码
            master_password: 主密码（可选）
            
        Returns:
            str: 加密后的密码（Base64编码）
        """
        try:
            key = self._get_encryption_key(master_password)
            fernet = Fernet(key)
            encrypted = fernet.encrypt(password.encode())
            return base64.urlsafe_b64encode(encrypted).decode()
        except Exception as e:
            logger.error(f"密码加密失败: {e}")
            raise
    
    def decrypt_password(self, encrypted_password: str, master_password: Optional[str] = None) -> str:
        """解密密码
        
        Args:
            encrypted_password: 加密的密码
            master_password: 主密码（可选）
            
        Returns:
            str: 解密后的密码
        """
        try:
            key = self._get_encryption_key(master_password)
            fernet = Fernet(key)
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_password.encode())
            decrypted = fernet.decrypt(encrypted_bytes)
            return decrypted.decode()
        except Exception as e:
            logger.error(f"密码解密失败: {e}")
            raise
    
    def store_password_securely(self, username: str, password: str, service: str = None) -> bool:
        """安全存储密码到系统密钥环
        
        Args:
            username: 用户名
            password: 密码
            service: 服务名（可选）
            
        Returns:
            bool: 是否成功存储
        """
        try:
            service_name = service or self.app_name
            keyring.set_password(service_name, username, password)
            logger.info(f"密码已安全存储到系统密钥环: {username}@{service_name}")
            return True
        except Exception as e:
            logger.error(f"存储密码到密钥环失败: {e}")
            return False
    
    def get_stored_password(self, username: str, service: str = None) -> Optional[str]:
        """从系统密钥环获取密码
        
        Args:
            username: 用户名
            service: 服务名（可选）
            
        Returns:
            Optional[str]: 密码，如果不存在则返回None
        """
        try:
            service_name = service or self.app_name
            password = keyring.get_password(service_name, username)
            if password:
                logger.info(f"从系统密钥环获取密码: {username}@{service_name}")
            return password
        except Exception as e:
            logger.error(f"从密钥环获取密码失败: {e}")
            return None
    
    def delete_stored_password(self, username: str, service: str = None) -> bool:
        """从系统密钥环删除密码
        
        Args:
            username: 用户名
            service: 服务名（可选）
            
        Returns:
            bool: 是否成功删除
        """
        try:
            service_name = service or self.app_name
            keyring.delete_password(service_name, username)
            logger.info(f"从系统密钥环删除密码: {username}@{service_name}")
            return True
        except Exception as e:
            logger.error(f"从密钥环删除密码失败: {e}")
            return False
    
    def generate_secure_password(self, length: int = 16, 
                                include_symbols: bool = True) -> str:
        """生成安全密码
        
        Args:
            length: 密码长度
            include_symbols: 是否包含特殊字符
            
        Returns:
            str: 生成的密码
        """
        import string
        
        characters = string.ascii_letters + string.digits
        if include_symbols:
            characters += "!@#$%^&*"
        
        password = ''.join(secrets.choice(characters) for _ in range(length))
        return password
    
    def validate_password_strength(self, password: str) -> Dict[str, Any]:
        """验证密码强度
        
        Args:
            password: 要验证的密码
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        result = {
            "score": 0,
            "strength": "弱",
            "suggestions": []
        }
        
        # 长度检查
        if len(password) >= 8:
            result["score"] += 1
        else:
            result["suggestions"].append("密码长度至少8位")
        
        if len(password) >= 12:
            result["score"] += 1
        
        # 字符类型检查
        has_lower = any(c.islower() for c in password)
        has_upper = any(c.isupper() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_symbol = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)
        
        if has_lower:
            result["score"] += 1
        else:
            result["suggestions"].append("包含小写字母")
        
        if has_upper:
            result["score"] += 1
        else:
            result["suggestions"].append("包含大写字母")
        
        if has_digit:
            result["score"] += 1
        else:
            result["suggestions"].append("包含数字")
        
        if has_symbol:
            result["score"] += 1
        else:
            result["suggestions"].append("包含特殊字符")
        
        # 评分转换为强度等级
        if result["score"] >= 5:
            result["strength"] = "强"
        elif result["score"] >= 3:
            result["strength"] = "中等"
        else:
            result["strength"] = "弱"
        
        return result
    
    def hash_password(self, password: str) -> str:
        """对密码进行哈希处理
        
        Args:
            password: 原始密码
            
        Returns:
            str: 哈希后的密码
        """
        salt = os.urandom(32)
        pwdhash = hashlib.pbkdf2_hmac('sha256', 
                                     password.encode('utf-8'), 
                                     salt, 
                                     100000)
        return salt.hex() + pwdhash.hex()
    
    def verify_password(self, stored_password: str, provided_password: str) -> bool:
        """验证密码
        
        Args:
            stored_password: 存储的哈希密码
            provided_password: 用户提供的密码
            
        Returns:
            bool: 密码是否匹配
        """
        try:
            salt = bytes.fromhex(stored_password[:64])
            stored_hash = stored_password[64:]
            pwdhash = hashlib.pbkdf2_hmac('sha256',
                                         provided_password.encode('utf-8'),
                                         salt,
                                         100000)
            return pwdhash.hex() == stored_hash
        except Exception as e:
            logger.error(f"密码验证失败: {e}")
            return False

# 全局安全管理器实例
security_manager = SecurityManager()

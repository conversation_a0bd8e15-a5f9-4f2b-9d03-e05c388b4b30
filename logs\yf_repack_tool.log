2025-07-31 13:09:47 - root - INFO - logger_config.py:90 - 日志系统初始化完成
2025-07-31 13:09:47 - test - INFO - test_app.py:29 - 日志系统测试
2025-07-31 13:09:47 - core.performance_monitor - INFO - performance_monitor.py:67 - 性能监控已启动
2025-07-31 13:09:48 - ui.theme_manager - INFO - theme_manager.py:30 - 主题管理器初始化完成
2025-07-31 13:13:19 - root - INFO - logger_config.py:90 - 日志系统初始化完成
2025-07-31 13:13:19 - test - INFO - test_app.py:29 - 日志系统测试
2025-07-31 13:13:19 - core.performance_monitor - INFO - performance_monitor.py:67 - 性能监控已启动
2025-07-31 13:13:19 - core.error_handler - INFO - error_handler.py:56 - 错误处理器初始化完成
2025-07-31 13:13:19 - core.error_handler - INFO - error_handler.py:171 - 注册恢复策略: ConnectionError
2025-07-31 13:13:19 - core.error_handler - INFO - error_handler.py:171 - 注册恢复策略: FileNotFoundError
2025-07-31 13:13:19 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:59 - SSH连接池初始化完成
2025-07-31 13:13:19 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:74 - SSH连接池已启动
2025-07-31 13:13:19 - ui.theme_manager - INFO - theme_manager.py:30 - 主题管理器初始化完成
2025-07-31 13:38:47 - root - INFO - logger_config.py:90 - 日志系统初始化完成
2025-07-31 13:38:47 - test - INFO - test_app.py:29 - 日志系统测试
2025-07-31 13:38:47 - core.performance_monitor - INFO - performance_monitor.py:67 - 性能监控已启动
2025-07-31 13:38:47 - core.error_handler - INFO - error_handler.py:56 - 错误处理器初始化完成
2025-07-31 13:38:47 - core.error_handler - INFO - error_handler.py:171 - 注册恢复策略: ConnectionError
2025-07-31 13:38:47 - core.error_handler - INFO - error_handler.py:171 - 注册恢复策略: FileNotFoundError
2025-07-31 13:38:47 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:59 - SSH连接池初始化完成
2025-07-31 13:38:47 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:74 - SSH连接池已启动
2025-07-31 13:38:47 - ui.theme_manager - INFO - theme_manager.py:30 - 主题管理器初始化完成
2025-07-31 13:59:36 - root - INFO - logger_config.py:90 - 日志系统初始化完成
2025-07-31 13:59:36 - test - INFO - test_app.py:29 - 日志系统测试
2025-07-31 13:59:36 - core.performance_monitor - INFO - performance_monitor.py:67 - 性能监控已启动
2025-07-31 13:59:36 - core.error_handler - INFO - error_handler.py:56 - 错误处理器初始化完成
2025-07-31 13:59:36 - core.error_handler - INFO - error_handler.py:171 - 注册恢复策略: ConnectionError
2025-07-31 13:59:36 - core.error_handler - INFO - error_handler.py:171 - 注册恢复策略: FileNotFoundError
2025-07-31 13:59:36 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:59 - SSH连接池初始化完成
2025-07-31 13:59:36 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:74 - SSH连接池已启动
2025-07-31 13:59:36 - ui.theme_manager - INFO - theme_manager.py:30 - 主题管理器初始化完成
2025-07-31 13:59:55 - root - INFO - logger_config.py:90 - 日志系统初始化完成
2025-07-31 13:59:55 - core.error_handler - INFO - error_handler.py:235 - 全局异常处理器已安装
2025-07-31 13:59:55 - __main__ - INFO - app.py:39 - ==================================================
2025-07-31 13:59:55 - __main__ - INFO - app.py:40 - 宇泛升级包工具启动
2025-07-31 13:59:55 - __main__ - INFO - app.py:41 - ==================================================
2025-07-31 13:59:55 - core.performance_monitor - INFO - performance_monitor.py:67 - 性能监控已启动
2025-07-31 13:59:55 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:74 - SSH连接池已启动
2025-07-31 13:59:55 - ui.theme_manager - INFO - theme_manager.py:57 - 已应用主题: default
2025-07-31 13:59:55 - ui.theme_manager - INFO - theme_manager.py:40 - 主题系统初始化完成，当前主题: default
2025-07-31 13:59:55 - ui.ssh_panel - INFO - ssh_panel.py:35 - SSH面板初始化完成
2025-07-31 13:59:55 - ui.command_panel - INFO - command_panel.py:31 - 命令面板初始化完成
2025-07-31 13:59:55 - ui.file_manager - INFO - file_manager.py:38 - 文件管理面板初始化完成
2025-07-31 13:59:55 - ui.main_window - INFO - main_window.py:37 - 主窗口初始化完成
2025-07-31 13:59:55 - __main__ - INFO - app.py:70 - 启动GUI主循环
2025-07-31 14:00:11 - core.config_manager - INFO - config_manager.py:84 - 配置文件保存成功: config.json
2025-07-31 14:00:11 - paramiko.transport - INFO - transport.py:1944 - Connected (version 2.0, client OpenSSH_8.9p1)
2025-07-31 14:00:12 - paramiko.transport - INFO - transport.py:1944 - Authentication (password) successful!
2025-07-31 14:00:12 - paramiko.transport.sftp - INFO - sftp.py:169 - [chan 0] Opened sftp connection (server version 3)
2025-07-31 14:00:12 - ssh_manager - INFO - ssh_manager.py:48 - 成功连接到 10.67.73.254:22
2025-07-31 14:00:12 - ui.ssh_panel - INFO - ssh_panel.py:238 - SSH连接成功: maojunyi@10.67.73.254
2025-07-31 14:00:12 - ui.file_manager - WARNING - file_manager.py:379 - 远程文件列表警告: ls: 无法访问 '/home/<USER>/yf_repack_tool': 没有那个文件或目录

2025-07-31 14:00:33 - __main__ - INFO - app.py:60 - 应用程序正在关闭...
2025-07-31 14:00:34 - core.performance_monitor - INFO - performance_monitor.py:74 - 性能监控已停止
2025-07-31 14:00:39 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:99 - SSH连接池已停止
2025-07-31 14:00:39 - core.error_handler - INFO - error_handler.py:240 - 全局异常处理器已卸载
2025-07-31 14:00:39 - ui.main_window - INFO - main_window.py:184 - 资源清理完成
2025-07-31 14:11:02 - root - INFO - logger_config.py:90 - 日志系统初始化完成
2025-07-31 14:11:02 - core.error_handler - INFO - error_handler.py:235 - 全局异常处理器已安装
2025-07-31 14:11:02 - __main__ - INFO - app.py:39 - ==================================================
2025-07-31 14:11:02 - __main__ - INFO - app.py:40 - 宇泛升级包工具启动
2025-07-31 14:11:02 - __main__ - INFO - app.py:41 - ==================================================
2025-07-31 14:11:02 - core.performance_monitor - INFO - performance_monitor.py:67 - 性能监控已启动
2025-07-31 14:11:02 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:74 - SSH连接池已启动
2025-07-31 14:11:02 - ui.theme_manager - INFO - theme_manager.py:57 - 已应用主题: default
2025-07-31 14:11:02 - ui.theme_manager - INFO - theme_manager.py:40 - 主题系统初始化完成，当前主题: default
2025-07-31 14:11:02 - ui.ssh_panel - INFO - ssh_panel.py:35 - SSH面板初始化完成
2025-07-31 14:11:02 - ui.command_panel - INFO - command_panel.py:31 - 命令面板初始化完成
2025-07-31 14:11:02 - ui.file_manager - INFO - file_manager.py:38 - 文件管理面板初始化完成
2025-07-31 14:11:02 - ui.main_window - INFO - main_window.py:37 - 主窗口初始化完成
2025-07-31 14:11:02 - __main__ - INFO - app.py:70 - 启动GUI主循环
2025-07-31 14:11:15 - paramiko.transport - INFO - transport.py:1944 - Connected (version 2.0, client OpenSSH_8.9p1)
2025-07-31 14:11:16 - paramiko.transport - INFO - transport.py:1944 - Authentication (password) successful!
2025-07-31 14:11:16 - paramiko.transport.sftp - INFO - sftp.py:169 - [chan 0] Opened sftp connection (server version 3)
2025-07-31 14:11:16 - ssh_manager - INFO - ssh_manager.py:48 - 成功连接到 10.67.73.254:22
2025-07-31 14:11:16 - paramiko.transport.sftp - INFO - sftp.py:169 - [chan 0] sftp session closed.
2025-07-31 14:11:16 - ssh_manager - INFO - ssh_manager.py:61 - SFTP会话已关闭。
2025-07-31 14:11:16 - ssh_manager - INFO - ssh_manager.py:65 - SSH连接已关闭。
2025-07-31 14:11:26 - __main__ - INFO - app.py:60 - 应用程序正在关闭...
2025-07-31 14:11:26 - core.performance_monitor - INFO - performance_monitor.py:74 - 性能监控已停止
2025-07-31 14:11:31 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:99 - SSH连接池已停止
2025-07-31 14:11:31 - core.error_handler - INFO - error_handler.py:240 - 全局异常处理器已卸载
2025-07-31 14:11:31 - ui.main_window - INFO - main_window.py:183 - 资源清理完成
2025-07-31 14:28:05 - root - INFO - logger_config.py:90 - 日志系统初始化完成
2025-07-31 14:28:05 - core.error_handler - INFO - error_handler.py:235 - 全局异常处理器已安装
2025-07-31 14:28:05 - __main__ - INFO - app.py:39 - ==================================================
2025-07-31 14:28:05 - __main__ - INFO - app.py:40 - 宇泛升级包工具启动
2025-07-31 14:28:05 - __main__ - INFO - app.py:41 - ==================================================
2025-07-31 14:28:05 - core.performance_monitor - INFO - performance_monitor.py:67 - 性能监控已启动
2025-07-31 14:28:05 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:74 - SSH连接池已启动
2025-07-31 14:28:05 - ui.theme_manager - INFO - theme_manager.py:57 - 已应用主题: default
2025-07-31 14:28:05 - ui.theme_manager - INFO - theme_manager.py:40 - 主题系统初始化完成，当前主题: default
2025-07-31 14:28:05 - ui.ssh_panel - INFO - ssh_panel.py:35 - SSH面板初始化完成
2025-07-31 14:28:05 - ui.command_panel - INFO - command_panel.py:31 - 命令面板初始化完成
2025-07-31 14:28:05 - ui.file_manager - INFO - file_manager.py:38 - 文件管理面板初始化完成
2025-07-31 14:28:05 - ui.main_window - INFO - main_window.py:37 - 主窗口初始化完成
2025-07-31 14:28:05 - __main__ - INFO - app.py:70 - 启动GUI主循环
2025-07-31 14:28:09 - __main__ - INFO - app.py:60 - 应用程序正在关闭...
2025-07-31 14:28:09 - core.performance_monitor - INFO - performance_monitor.py:74 - 性能监控已停止
2025-07-31 14:28:14 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:99 - SSH连接池已停止
2025-07-31 14:28:14 - core.error_handler - INFO - error_handler.py:240 - 全局异常处理器已卸载
2025-07-31 14:28:14 - ui.main_window - INFO - main_window.py:183 - 资源清理完成
2025-07-31 14:39:20 - root - INFO - logger_config.py:90 - 日志系统初始化完成
2025-07-31 14:39:20 - core.error_handler - INFO - error_handler.py:235 - 全局异常处理器已安装
2025-07-31 14:39:20 - __main__ - INFO - main.py:39 - ==================================================
2025-07-31 14:39:20 - __main__ - INFO - main.py:40 - 宇泛升级包工具启动
2025-07-31 14:39:20 - __main__ - INFO - main.py:41 - ==================================================
2025-07-31 14:39:20 - core.performance_monitor - INFO - performance_monitor.py:67 - 性能监控已启动
2025-07-31 14:39:20 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:74 - SSH连接池已启动
2025-07-31 14:39:20 - ui.theme_manager - INFO - theme_manager.py:57 - 已应用主题: default
2025-07-31 14:39:20 - ui.theme_manager - INFO - theme_manager.py:40 - 主题系统初始化完成，当前主题: default
2025-07-31 14:39:20 - ui.ssh_panel - INFO - ssh_panel.py:35 - SSH面板初始化完成
2025-07-31 14:39:20 - ui.command_panel - INFO - command_panel.py:31 - 命令面板初始化完成
2025-07-31 14:39:20 - ui.file_manager - INFO - file_manager.py:38 - 文件管理面板初始化完成
2025-07-31 14:39:20 - ui.main_window - INFO - main_window.py:37 - 主窗口初始化完成
2025-07-31 14:39:20 - __main__ - INFO - main.py:70 - 启动GUI主循环
2025-07-31 14:39:33 - core.security_manager - INFO - security_manager.py:131 - 密码已安全存储到系统密钥环: maojunyi@10.67.73.254@YufanUpgradeTool
2025-07-31 14:39:34 - core.config_manager - INFO - config_manager.py:84 - 配置文件保存成功: config.json
2025-07-31 14:39:34 - paramiko.transport - INFO - transport.py:1944 - Connected (version 2.0, client OpenSSH_8.9p1)
2025-07-31 14:39:34 - paramiko.transport - INFO - transport.py:1944 - Authentication (password) successful!
2025-07-31 14:39:34 - paramiko.transport.sftp - INFO - sftp.py:169 - [chan 0] Opened sftp connection (server version 3)
2025-07-31 14:39:34 - core.ssh_manager - INFO - ssh_manager.py:48 - 成功连接到 10.67.73.254:22
2025-07-31 14:39:34 - ui.ssh_panel - INFO - ssh_panel.py:238 - SSH连接成功: maojunyi@10.67.73.254
2025-07-31 14:39:34 - ui.file_manager - WARNING - file_manager.py:379 - 远程文件列表警告: ls: 无法访问 '/home/<USER>/yf_repack_tool': 没有那个文件或目录

2025-07-31 14:39:34 - core.security_manager - INFO - security_manager.py:131 - 密码已安全存储到系统密钥环: maojunyi@10.67.73.254@YufanUpgradeTool
2025-07-31 14:39:37 - __main__ - INFO - main.py:60 - 应用程序正在关闭...
2025-07-31 14:39:37 - core.performance_monitor - INFO - performance_monitor.py:74 - 性能监控已停止
2025-07-31 14:39:42 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:99 - SSH连接池已停止
2025-07-31 14:39:42 - core.error_handler - INFO - error_handler.py:240 - 全局异常处理器已卸载
2025-07-31 14:39:42 - ui.main_window - INFO - main_window.py:183 - 资源清理完成
2025-07-31 14:46:39 - root - INFO - logger_config.py:90 - 日志系统初始化完成
2025-07-31 14:46:39 - core.error_handler - INFO - error_handler.py:235 - 全局异常处理器已安装
2025-07-31 14:46:39 - __main__ - INFO - main.py:39 - ==================================================
2025-07-31 14:46:39 - __main__ - INFO - main.py:40 - 宇泛升级包工具启动
2025-07-31 14:46:39 - __main__ - INFO - main.py:41 - ==================================================
2025-07-31 14:46:39 - core.performance_monitor - INFO - performance_monitor.py:67 - 性能监控已启动
2025-07-31 14:46:39 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:74 - SSH连接池已启动
2025-07-31 14:46:39 - ui.theme_manager - INFO - theme_manager.py:57 - 已应用主题: default
2025-07-31 14:46:39 - ui.theme_manager - INFO - theme_manager.py:40 - 主题系统初始化完成，当前主题: default
2025-07-31 14:46:39 - core.security_manager - INFO - security_manager.py:151 - 从系统密钥环获取密码: maojunyi@10.67.73.254@YufanUpgradeTool
2025-07-31 14:46:39 - ui.ssh_panel - INFO - ssh_panel.py:35 - SSH面板初始化完成
2025-07-31 14:46:39 - ui.command_panel - INFO - command_panel.py:31 - 命令面板初始化完成
2025-07-31 14:46:39 - ui.file_manager - INFO - file_manager.py:38 - 文件管理面板初始化完成
2025-07-31 14:46:39 - ui.main_window - INFO - main_window.py:37 - 主窗口初始化完成
2025-07-31 14:46:39 - __main__ - INFO - main.py:70 - 启动GUI主循环
2025-07-31 14:46:46 - __main__ - INFO - main.py:60 - 应用程序正在关闭...
2025-07-31 14:46:46 - core.performance_monitor - INFO - performance_monitor.py:74 - 性能监控已停止
2025-07-31 14:46:51 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:99 - SSH连接池已停止
2025-07-31 14:46:51 - core.error_handler - INFO - error_handler.py:240 - 全局异常处理器已卸载
2025-07-31 14:46:51 - ui.main_window - INFO - main_window.py:183 - 资源清理完成
2025-07-31 15:44:57 - root - INFO - logger_config.py:90 - 日志系统初始化完成
2025-07-31 15:44:57 - core.error_handler - INFO - error_handler.py:235 - 全局异常处理器已安装
2025-07-31 15:44:57 - __main__ - INFO - main.py:39 - ==================================================
2025-07-31 15:44:57 - __main__ - INFO - main.py:40 - 宇泛升级包工具启动
2025-07-31 15:44:57 - __main__ - INFO - main.py:41 - ==================================================
2025-07-31 15:44:57 - core.performance_monitor - INFO - performance_monitor.py:70 - 性能监控已启动
2025-07-31 15:44:57 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:77 - SSH连接池已启动
2025-07-31 15:44:57 - ui.theme_manager - INFO - theme_manager.py:57 - 已应用主题: default
2025-07-31 15:44:57 - ui.theme_manager - INFO - theme_manager.py:40 - 主题系统初始化完成，当前主题: default
2025-07-31 15:44:57 - core.security_manager - INFO - security_manager.py:151 - 从系统密钥环获取密码: maojunyi@10.67.73.254@YufanUpgradeTool
2025-07-31 15:44:57 - ui.ssh_panel - INFO - ssh_panel.py:35 - SSH面板初始化完成
2025-07-31 15:44:57 - ui.command_panel - INFO - command_panel.py:31 - 命令面板初始化完成
2025-07-31 15:44:57 - ui.file_manager - INFO - file_manager.py:38 - 文件管理面板初始化完成
2025-07-31 15:44:57 - ui.main_window - INFO - main_window.py:37 - 主窗口初始化完成
2025-07-31 15:44:57 - __main__ - INFO - main.py:70 - 启动GUI主循环
2025-07-31 15:45:07 - core.config_manager - INFO - config_manager.py:94 - 配置文件保存成功: config.json
2025-07-31 15:45:07 - paramiko.transport - INFO - transport.py:1944 - Connected (version 2.0, client OpenSSH_8.9p1)
2025-07-31 15:45:07 - paramiko.transport - INFO - transport.py:1944 - Authentication (password) successful!
2025-07-31 15:45:07 - paramiko.transport.sftp - INFO - sftp.py:169 - [chan 0] Opened sftp connection (server version 3)
2025-07-31 15:45:07 - core.ssh_manager - INFO - ssh_manager.py:48 - 成功连接到 10.67.73.254:22
2025-07-31 15:45:07 - ui.ssh_panel - INFO - ssh_panel.py:238 - SSH连接成功: maojunyi@10.67.73.254
2025-07-31 15:45:07 - ui.file_manager - WARNING - file_manager.py:379 - 远程文件列表警告: ls: 无法访问 '/home/<USER>/yf_repack_tool': 没有那个文件或目录

2025-07-31 15:45:07 - core.security_manager - INFO - security_manager.py:131 - 密码已安全存储到系统密钥环: maojunyi@10.67.73.254@YufanUpgradeTool
2025-07-31 15:45:09 - __main__ - INFO - main.py:60 - 应用程序正在关闭...
2025-07-31 15:45:09 - core.performance_monitor - INFO - performance_monitor.py:87 - 性能监控已停止
2025-07-31 15:45:09 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:81 - 开始停止SSH连接池，超时: 1.0秒
2025-07-31 15:45:09 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:138 - SSH连接池已停止，耗时: 0.000秒
2025-07-31 15:45:09 - core.error_handler - INFO - error_handler.py:240 - 全局异常处理器已卸载
2025-07-31 15:45:09 - ui.main_window - INFO - main_window.py:265 - 资源清理完成
2025-07-31 15:45:11 - root - INFO - logger_config.py:90 - 日志系统初始化完成
2025-07-31 15:45:11 - core.error_handler - INFO - error_handler.py:235 - 全局异常处理器已安装
2025-07-31 15:45:11 - __main__ - INFO - main.py:39 - ==================================================
2025-07-31 15:45:11 - __main__ - INFO - main.py:40 - 宇泛升级包工具启动
2025-07-31 15:45:11 - __main__ - INFO - main.py:41 - ==================================================
2025-07-31 15:45:11 - core.performance_monitor - INFO - performance_monitor.py:70 - 性能监控已启动
2025-07-31 15:45:11 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:77 - SSH连接池已启动
2025-07-31 15:45:11 - ui.theme_manager - INFO - theme_manager.py:57 - 已应用主题: default
2025-07-31 15:45:11 - ui.theme_manager - INFO - theme_manager.py:40 - 主题系统初始化完成，当前主题: default
2025-07-31 15:45:12 - core.security_manager - INFO - security_manager.py:151 - 从系统密钥环获取密码: maojunyi@10.67.73.254@YufanUpgradeTool
2025-07-31 15:45:12 - ui.ssh_panel - INFO - ssh_panel.py:35 - SSH面板初始化完成
2025-07-31 15:45:12 - ui.command_panel - INFO - command_panel.py:31 - 命令面板初始化完成
2025-07-31 15:45:12 - ui.file_manager - INFO - file_manager.py:38 - 文件管理面板初始化完成
2025-07-31 15:45:12 - ui.main_window - INFO - main_window.py:37 - 主窗口初始化完成
2025-07-31 15:45:12 - __main__ - INFO - main.py:70 - 启动GUI主循环
2025-07-31 15:45:14 - core.config_manager - INFO - config_manager.py:94 - 配置文件保存成功: config.json
2025-07-31 15:45:14 - paramiko.transport - INFO - transport.py:1944 - Connected (version 2.0, client OpenSSH_8.9p1)
2025-07-31 15:45:14 - paramiko.transport - INFO - transport.py:1944 - Authentication (password) successful!
2025-07-31 15:45:14 - paramiko.transport.sftp - INFO - sftp.py:169 - [chan 0] Opened sftp connection (server version 3)
2025-07-31 15:45:14 - core.ssh_manager - INFO - ssh_manager.py:48 - 成功连接到 10.67.73.254:22
2025-07-31 15:45:14 - ui.ssh_panel - INFO - ssh_panel.py:238 - SSH连接成功: maojunyi@10.67.73.254
2025-07-31 15:45:14 - ui.file_manager - WARNING - file_manager.py:379 - 远程文件列表警告: ls: 无法访问 '/home/<USER>/yf_repack_tool': 没有那个文件或目录

2025-07-31 15:45:14 - core.security_manager - INFO - security_manager.py:131 - 密码已安全存储到系统密钥环: maojunyi@10.67.73.254@YufanUpgradeTool
2025-07-31 15:46:07 - __main__ - INFO - main.py:60 - 应用程序正在关闭...
2025-07-31 15:46:07 - core.performance_monitor - INFO - performance_monitor.py:87 - 性能监控已停止
2025-07-31 15:46:07 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:81 - 开始停止SSH连接池，超时: 1.0秒
2025-07-31 15:46:07 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:138 - SSH连接池已停止，耗时: 0.000秒
2025-07-31 15:46:07 - core.error_handler - INFO - error_handler.py:240 - 全局异常处理器已卸载
2025-07-31 15:46:07 - ui.main_window - INFO - main_window.py:265 - 资源清理完成
2025-07-31 15:51:32 - root - INFO - logger_config.py:90 - 日志系统初始化完成
2025-07-31 15:51:32 - core.error_handler - INFO - error_handler.py:235 - 全局异常处理器已安装
2025-07-31 15:51:32 - __main__ - INFO - main.py:39 - ==================================================
2025-07-31 15:51:32 - __main__ - INFO - main.py:40 - 宇泛升级包工具启动
2025-07-31 15:51:32 - __main__ - INFO - main.py:41 - ==================================================
2025-07-31 15:51:32 - core.performance_monitor - INFO - performance_monitor.py:70 - 性能监控已启动
2025-07-31 15:51:32 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:77 - SSH连接池已启动
2025-07-31 15:51:32 - ui.theme_manager - INFO - theme_manager.py:57 - 已应用主题: default
2025-07-31 15:51:32 - ui.theme_manager - INFO - theme_manager.py:40 - 主题系统初始化完成，当前主题: default
2025-07-31 15:51:33 - core.security_manager - INFO - security_manager.py:151 - 从系统密钥环获取密码: maojunyi@10.67.73.254@YufanUpgradeTool
2025-07-31 15:51:33 - ui.ssh_panel - INFO - ssh_panel.py:35 - SSH面板初始化完成
2025-07-31 15:51:33 - ui.command_panel - INFO - command_panel.py:31 - 命令面板初始化完成
2025-07-31 15:51:33 - ui.file_manager - INFO - file_manager.py:38 - 文件管理面板初始化完成
2025-07-31 15:51:33 - ui.main_window - INFO - main_window.py:37 - 主窗口初始化完成
2025-07-31 15:51:33 - __main__ - INFO - main.py:70 - 启动GUI主循环
2025-07-31 15:51:36 - __main__ - INFO - main.py:60 - 应用程序正在关闭...
2025-07-31 15:51:36 - core.performance_monitor - INFO - performance_monitor.py:87 - 性能监控已停止
2025-07-31 15:51:36 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:81 - 开始停止SSH连接池，超时: 1.0秒
2025-07-31 15:51:36 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:138 - SSH连接池已停止，耗时: 0.000秒
2025-07-31 15:51:36 - core.error_handler - INFO - error_handler.py:240 - 全局异常处理器已卸载
2025-07-31 15:51:36 - ui.main_window - INFO - main_window.py:265 - 资源清理完成
2025-07-31 16:04:22 - root - INFO - logger_config.py:90 - 日志系统初始化完成
2025-07-31 16:04:22 - core.error_handler - INFO - error_handler.py:235 - 全局异常处理器已安装
2025-07-31 16:04:22 - __main__ - INFO - main.py:39 - ==================================================
2025-07-31 16:04:22 - __main__ - INFO - main.py:40 - 宇泛升级包工具启动
2025-07-31 16:04:22 - __main__ - INFO - main.py:41 - ==================================================
2025-07-31 16:04:22 - core.performance_monitor - INFO - performance_monitor.py:70 - 性能监控已启动
2025-07-31 16:04:22 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:77 - SSH连接池已启动
2025-07-31 16:04:22 - ui.theme_manager - INFO - theme_manager.py:57 - 已应用主题: default
2025-07-31 16:04:22 - ui.theme_manager - INFO - theme_manager.py:40 - 主题系统初始化完成，当前主题: default
2025-07-31 16:04:22 - core.security_manager - INFO - security_manager.py:151 - 从系统密钥环获取密码: maojunyi@10.67.73.254@YufanUpgradeTool
2025-07-31 16:04:22 - ui.ssh_panel - INFO - ssh_panel.py:35 - SSH面板初始化完成
2025-07-31 16:04:22 - ui.command_panel - INFO - command_panel.py:31 - 命令面板初始化完成
2025-07-31 16:04:22 - ui.file_manager - INFO - file_manager.py:38 - 文件管理面板初始化完成
2025-07-31 16:04:22 - ui.main_window - INFO - main_window.py:37 - 主窗口初始化完成
2025-07-31 16:04:22 - __main__ - INFO - main.py:70 - 启动GUI主循环
2025-07-31 16:04:29 - __main__ - INFO - main.py:60 - 应用程序正在关闭...
2025-07-31 16:04:29 - core.performance_monitor - INFO - performance_monitor.py:87 - 性能监控已停止
2025-07-31 16:04:29 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:81 - 开始停止SSH连接池，超时: 1.0秒
2025-07-31 16:04:29 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:138 - SSH连接池已停止，耗时: 0.000秒
2025-07-31 16:04:29 - core.error_handler - INFO - error_handler.py:240 - 全局异常处理器已卸载
2025-07-31 16:04:29 - ui.main_window - INFO - main_window.py:265 - 资源清理完成
2025-07-31 16:04:45 - root - INFO - logger_config.py:90 - 日志系统初始化完成
2025-07-31 16:04:45 - core.error_handler - INFO - error_handler.py:235 - 全局异常处理器已安装
2025-07-31 16:04:45 - __main__ - INFO - main.py:39 - ==================================================
2025-07-31 16:04:45 - __main__ - INFO - main.py:40 - 宇泛升级包工具启动
2025-07-31 16:04:45 - __main__ - INFO - main.py:41 - ==================================================
2025-07-31 16:04:45 - core.performance_monitor - INFO - performance_monitor.py:70 - 性能监控已启动
2025-07-31 16:04:45 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:77 - SSH连接池已启动
2025-07-31 16:04:46 - ui.theme_manager - INFO - theme_manager.py:57 - 已应用主题: default
2025-07-31 16:04:46 - ui.theme_manager - INFO - theme_manager.py:40 - 主题系统初始化完成，当前主题: default
2025-07-31 16:04:46 - core.security_manager - INFO - security_manager.py:151 - 从系统密钥环获取密码: maojunyi@10.67.73.254@YufanUpgradeTool
2025-07-31 16:04:46 - ui.ssh_panel - INFO - ssh_panel.py:35 - SSH面板初始化完成
2025-07-31 16:04:46 - ui.command_panel - INFO - command_panel.py:31 - 命令面板初始化完成
2025-07-31 16:04:46 - ui.file_manager - INFO - file_manager.py:38 - 文件管理面板初始化完成
2025-07-31 16:04:46 - ui.main_window - INFO - main_window.py:37 - 主窗口初始化完成
2025-07-31 16:04:46 - __main__ - INFO - main.py:70 - 启动GUI主循环
2025-07-31 16:06:36 - __main__ - INFO - main.py:60 - 应用程序正在关闭...
2025-07-31 16:06:36 - core.performance_monitor - INFO - performance_monitor.py:87 - 性能监控已停止
2025-07-31 16:06:36 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:81 - 开始停止SSH连接池，超时: 1.0秒
2025-07-31 16:06:36 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:138 - SSH连接池已停止，耗时: 0.000秒
2025-07-31 16:06:36 - core.error_handler - INFO - error_handler.py:240 - 全局异常处理器已卸载
2025-07-31 16:06:36 - ui.main_window - INFO - main_window.py:265 - 资源清理完成
2025-07-31 16:10:31 - root - INFO - logger_config.py:90 - 日志系统初始化完成
2025-07-31 16:10:31 - core.error_handler - INFO - error_handler.py:235 - 全局异常处理器已安装
2025-07-31 16:10:31 - __main__ - INFO - main.py:39 - ==================================================
2025-07-31 16:10:31 - __main__ - INFO - main.py:40 - 宇泛升级包工具启动
2025-07-31 16:10:31 - __main__ - INFO - main.py:41 - ==================================================
2025-07-31 16:10:31 - core.performance_monitor - INFO - performance_monitor.py:70 - 性能监控已启动
2025-07-31 16:10:31 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:77 - SSH连接池已启动
2025-07-31 16:10:31 - ui.theme_manager - INFO - theme_manager.py:57 - 已应用主题: default
2025-07-31 16:10:31 - ui.theme_manager - INFO - theme_manager.py:40 - 主题系统初始化完成，当前主题: default
2025-07-31 16:10:32 - core.security_manager - INFO - security_manager.py:151 - 从系统密钥环获取密码: maojunyi@10.67.73.254@YufanUpgradeTool
2025-07-31 16:10:32 - ui.ssh_panel - INFO - ssh_panel.py:35 - SSH面板初始化完成
2025-07-31 16:10:32 - ui.command_panel - INFO - command_panel.py:31 - 命令面板初始化完成
2025-07-31 16:10:32 - ui.file_manager - INFO - file_manager.py:38 - 文件管理面板初始化完成
2025-07-31 16:10:32 - ui.main_window - INFO - main_window.py:37 - 主窗口初始化完成
2025-07-31 16:10:32 - __main__ - INFO - main.py:70 - 启动GUI主循环
2025-07-31 16:11:04 - __main__ - INFO - main.py:60 - 应用程序正在关闭...
2025-07-31 16:11:04 - core.performance_monitor - INFO - performance_monitor.py:87 - 性能监控已停止
2025-07-31 16:11:04 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:81 - 开始停止SSH连接池，超时: 1.0秒
2025-07-31 16:11:04 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:138 - SSH连接池已停止，耗时: 0.000秒
2025-07-31 16:11:04 - core.error_handler - INFO - error_handler.py:240 - 全局异常处理器已卸载
2025-07-31 16:11:04 - ui.main_window - INFO - main_window.py:265 - 资源清理完成
2025-07-31 16:13:19 - root - INFO - logger_config.py:90 - 日志系统初始化完成
2025-07-31 16:13:19 - core.error_handler - INFO - error_handler.py:235 - 全局异常处理器已安装
2025-07-31 16:13:19 - __main__ - INFO - main.py:39 - ==================================================
2025-07-31 16:13:19 - __main__ - INFO - main.py:40 - 宇泛升级包工具启动
2025-07-31 16:13:19 - __main__ - INFO - main.py:41 - ==================================================
2025-07-31 16:13:19 - core.performance_monitor - INFO - performance_monitor.py:70 - 性能监控已启动
2025-07-31 16:13:19 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:77 - SSH连接池已启动
2025-07-31 16:13:19 - ui.theme_manager - INFO - theme_manager.py:57 - 已应用主题: default
2025-07-31 16:13:19 - ui.theme_manager - INFO - theme_manager.py:40 - 主题系统初始化完成，当前主题: default
2025-07-31 16:13:19 - core.security_manager - INFO - security_manager.py:151 - 从系统密钥环获取密码: maojunyi@10.67.73.254@YufanUpgradeTool
2025-07-31 16:13:19 - ui.ssh_panel - INFO - ssh_panel.py:35 - SSH面板初始化完成
2025-07-31 16:13:19 - ui.command_panel - INFO - command_panel.py:31 - 命令面板初始化完成
2025-07-31 16:13:19 - ui.file_manager - INFO - file_manager.py:38 - 文件管理面板初始化完成
2025-07-31 16:13:19 - ui.main_window - INFO - main_window.py:37 - 主窗口初始化完成
2025-07-31 16:13:19 - __main__ - INFO - main.py:70 - 启动GUI主循环
2025-07-31 16:19:35 - __main__ - INFO - main.py:60 - 应用程序正在关闭...
2025-07-31 16:19:35 - core.performance_monitor - INFO - performance_monitor.py:87 - 性能监控已停止
2025-07-31 16:19:35 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:81 - 开始停止SSH连接池，超时: 1.0秒
2025-07-31 16:19:35 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:138 - SSH连接池已停止，耗时: 0.000秒
2025-07-31 16:19:35 - core.error_handler - INFO - error_handler.py:240 - 全局异常处理器已卸载
2025-07-31 16:19:35 - ui.main_window - INFO - main_window.py:265 - 资源清理完成
2025-07-31 16:19:43 - root - INFO - logger_config.py:90 - 日志系统初始化完成
2025-07-31 16:19:43 - core.error_handler - INFO - error_handler.py:235 - 全局异常处理器已安装
2025-07-31 16:19:43 - __main__ - INFO - main.py:39 - ==================================================
2025-07-31 16:19:43 - __main__ - INFO - main.py:40 - 宇泛升级包工具启动
2025-07-31 16:19:43 - __main__ - INFO - main.py:41 - ==================================================
2025-07-31 16:19:43 - core.performance_monitor - INFO - performance_monitor.py:70 - 性能监控已启动
2025-07-31 16:19:43 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:77 - SSH连接池已启动
2025-07-31 16:19:43 - ui.theme_manager - INFO - theme_manager.py:57 - 已应用主题: default
2025-07-31 16:19:43 - ui.theme_manager - INFO - theme_manager.py:40 - 主题系统初始化完成，当前主题: default
2025-07-31 16:19:43 - core.security_manager - INFO - security_manager.py:151 - 从系统密钥环获取密码: maojunyi@10.67.73.254@YufanUpgradeTool
2025-07-31 16:19:43 - ui.ssh_panel - INFO - ssh_panel.py:35 - SSH面板初始化完成
2025-07-31 16:19:43 - ui.command_panel - INFO - command_panel.py:31 - 命令面板初始化完成
2025-07-31 16:19:43 - ui.file_manager - INFO - file_manager.py:38 - 文件管理面板初始化完成
2025-07-31 16:19:43 - ui.main_window - INFO - main_window.py:37 - 主窗口初始化完成
2025-07-31 16:19:43 - __main__ - INFO - main.py:70 - 启动GUI主循环
2025-07-31 16:19:56 - core.config_manager - INFO - config_manager.py:94 - 配置文件保存成功: config.json
2025-07-31 16:19:56 - paramiko.transport - INFO - transport.py:1944 - Connected (version 2.0, client OpenSSH_8.9p1)
2025-07-31 16:19:56 - paramiko.transport - INFO - transport.py:1944 - Authentication (password) successful!
2025-07-31 16:19:56 - paramiko.transport.sftp - INFO - sftp.py:169 - [chan 0] Opened sftp connection (server version 3)
2025-07-31 16:19:56 - core.ssh_manager - INFO - ssh_manager.py:48 - 成功连接到 10.67.73.254:22
2025-07-31 16:19:56 - ui.ssh_panel - INFO - ssh_panel.py:247 - SSH连接成功: maojunyi@10.67.73.254
2025-07-31 16:19:56 - core.security_manager - INFO - security_manager.py:131 - 密码已安全存储到系统密钥环: maojunyi@10.67.73.254@YufanUpgradeTool
2025-07-31 16:20:45 - paramiko.transport.sftp - INFO - sftp.py:169 - [chan 0] sftp session closed.
2025-07-31 16:20:45 - core.ssh_manager - INFO - ssh_manager.py:61 - SFTP会话已关闭。
2025-07-31 16:20:45 - core.ssh_manager - INFO - ssh_manager.py:65 - SSH连接已关闭。
2025-07-31 16:20:45 - ui.ssh_panel - INFO - ssh_panel.py:276 - SSH连接已断开
2025-07-31 16:20:48 - core.config_manager - INFO - config_manager.py:94 - 配置文件保存成功: config.json
2025-07-31 16:20:48 - paramiko.transport - INFO - transport.py:1944 - Connected (version 2.0, client OpenSSH_8.9p1)
2025-07-31 16:20:48 - paramiko.transport - INFO - transport.py:1944 - Authentication (password) successful!
2025-07-31 16:20:48 - paramiko.transport.sftp - INFO - sftp.py:169 - [chan 0] Opened sftp connection (server version 3)
2025-07-31 16:20:48 - core.ssh_manager - INFO - ssh_manager.py:48 - 成功连接到 10.67.73.254:22
2025-07-31 16:20:48 - ui.ssh_panel - INFO - ssh_panel.py:247 - SSH连接成功: maojunyi@10.67.73.254
2025-07-31 16:20:48 - core.security_manager - INFO - security_manager.py:131 - 密码已安全存储到系统密钥环: maojunyi@10.67.73.254@YufanUpgradeTool
2025-07-31 16:24:59 - __main__ - INFO - main.py:60 - 应用程序正在关闭...
2025-07-31 16:24:59 - core.performance_monitor - INFO - performance_monitor.py:87 - 性能监控已停止
2025-07-31 16:24:59 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:81 - 开始停止SSH连接池，超时: 1.0秒
2025-07-31 16:24:59 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:138 - SSH连接池已停止，耗时: 0.000秒
2025-07-31 16:24:59 - core.error_handler - INFO - error_handler.py:240 - 全局异常处理器已卸载
2025-07-31 16:24:59 - ui.main_window - INFO - main_window.py:265 - 资源清理完成
2025-07-31 16:25:31 - root - INFO - logger_config.py:90 - 日志系统初始化完成
2025-07-31 16:25:31 - core.error_handler - INFO - error_handler.py:235 - 全局异常处理器已安装
2025-07-31 16:25:31 - __main__ - INFO - main.py:39 - ==================================================
2025-07-31 16:25:31 - __main__ - INFO - main.py:40 - 宇泛升级包工具启动
2025-07-31 16:25:31 - __main__ - INFO - main.py:41 - ==================================================
2025-07-31 16:25:31 - core.performance_monitor - INFO - performance_monitor.py:70 - 性能监控已启动
2025-07-31 16:25:31 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:77 - SSH连接池已启动
2025-07-31 16:25:31 - ui.theme_manager - INFO - theme_manager.py:57 - 已应用主题: default
2025-07-31 16:25:31 - ui.theme_manager - INFO - theme_manager.py:40 - 主题系统初始化完成，当前主题: default
2025-07-31 16:25:32 - core.security_manager - INFO - security_manager.py:151 - 从系统密钥环获取密码: maojunyi@10.67.73.254@YufanUpgradeTool
2025-07-31 16:25:32 - ui.ssh_panel - INFO - ssh_panel.py:35 - SSH面板初始化完成
2025-07-31 16:25:32 - ui.command_panel - INFO - command_panel.py:31 - 命令面板初始化完成
2025-07-31 16:25:32 - ui.file_manager - INFO - file_manager.py:38 - 文件管理面板初始化完成
2025-07-31 16:25:32 - ui.main_window - INFO - main_window.py:37 - 主窗口初始化完成
2025-07-31 16:25:32 - __main__ - INFO - main.py:70 - 启动GUI主循环
2025-07-31 16:25:36 - core.config_manager - INFO - config_manager.py:94 - 配置文件保存成功: config.json
2025-07-31 16:25:36 - paramiko.transport - INFO - transport.py:1944 - Connected (version 2.0, client OpenSSH_8.9p1)
2025-07-31 16:25:36 - paramiko.transport - INFO - transport.py:1944 - Authentication (password) successful!
2025-07-31 16:25:36 - paramiko.transport.sftp - INFO - sftp.py:169 - [chan 0] Opened sftp connection (server version 3)
2025-07-31 16:25:36 - core.ssh_manager - INFO - ssh_manager.py:48 - 成功连接到 10.67.73.254:22
2025-07-31 16:25:36 - ui.ssh_panel - INFO - ssh_panel.py:247 - SSH连接成功: maojunyi@10.67.73.254
2025-07-31 16:25:36 - core.security_manager - INFO - security_manager.py:131 - 密码已安全存储到系统密钥环: maojunyi@10.67.73.254@YufanUpgradeTool
2025-07-31 16:25:41 - __main__ - INFO - main.py:60 - 应用程序正在关闭...
2025-07-31 16:25:41 - core.performance_monitor - INFO - performance_monitor.py:87 - 性能监控已停止
2025-07-31 16:25:41 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:81 - 开始停止SSH连接池，超时: 1.0秒
2025-07-31 16:25:41 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:138 - SSH连接池已停止，耗时: 0.000秒
2025-07-31 16:25:41 - core.error_handler - INFO - error_handler.py:240 - 全局异常处理器已卸载
2025-07-31 16:25:41 - ui.main_window - INFO - main_window.py:265 - 资源清理完成
2025-07-31 16:33:59 - root - INFO - logger_config.py:90 - 日志系统初始化完成
2025-07-31 16:33:59 - core.error_handler - INFO - error_handler.py:235 - 全局异常处理器已安装
2025-07-31 16:33:59 - __main__ - INFO - main.py:39 - ==================================================
2025-07-31 16:33:59 - __main__ - INFO - main.py:40 - 宇泛升级包工具启动
2025-07-31 16:33:59 - __main__ - INFO - main.py:41 - ==================================================
2025-07-31 16:33:59 - core.performance_monitor - INFO - performance_monitor.py:70 - 性能监控已启动
2025-07-31 16:33:59 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:77 - SSH连接池已启动
2025-07-31 16:33:59 - ui.theme_manager - INFO - theme_manager.py:57 - 已应用主题: default
2025-07-31 16:33:59 - ui.theme_manager - INFO - theme_manager.py:40 - 主题系统初始化完成，当前主题: default
2025-07-31 16:33:59 - core.security_manager - INFO - security_manager.py:151 - 从系统密钥环获取密码: maojunyi@10.67.73.254@YufanUpgradeTool
2025-07-31 16:33:59 - ui.ssh_panel - INFO - ssh_panel.py:35 - SSH面板初始化完成
2025-07-31 16:33:59 - ui.command_panel - INFO - command_panel.py:31 - 命令面板初始化完成
2025-07-31 16:33:59 - ui.file_manager - INFO - file_manager.py:38 - 文件管理面板初始化完成
2025-07-31 16:33:59 - ui.main_window - INFO - main_window.py:37 - 主窗口初始化完成
2025-07-31 16:33:59 - __main__ - INFO - main.py:70 - 启动GUI主循环
2025-07-31 16:34:14 - core.config_manager - INFO - config_manager.py:94 - 配置文件保存成功: config.json
2025-07-31 16:34:14 - paramiko.transport - INFO - transport.py:1944 - Connected (version 2.0, client OpenSSH_8.9p1)
2025-07-31 16:34:15 - paramiko.transport - INFO - transport.py:1944 - Authentication (password) successful!
2025-07-31 16:34:15 - paramiko.transport.sftp - INFO - sftp.py:169 - [chan 0] Opened sftp connection (server version 3)
2025-07-31 16:34:15 - core.ssh_manager - INFO - ssh_manager.py:48 - 成功连接到 10.67.73.254:22
2025-07-31 16:34:15 - ui.file_manager - INFO - file_manager.py:372 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/
2025-07-31 16:34:15 - ui.ssh_panel - INFO - ssh_panel.py:247 - SSH连接成功: maojunyi@10.67.73.254
2025-07-31 16:34:15 - ui.file_manager - INFO - file_manager.py:379 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/
2025-07-31 16:34:15 - ui.file_manager - INFO - file_manager.py:389 - ls命令输出长度: 830 字符
2025-07-31 16:34:15 - ui.file_manager - INFO - file_manager.py:390 - ls命令错误输出: ''
2025-07-31 16:34:15 - ui.file_manager - INFO - file_manager.py:430 - 解析到 12 个文件/目录项
2025-07-31 16:34:15 - core.security_manager - INFO - security_manager.py:131 - 密码已安全存储到系统密钥环: maojunyi@10.67.73.254@YufanUpgradeTool
2025-07-31 16:34:15 - ui.file_manager - INFO - file_manager.py:441 - 开始更新远程文件树，项目数: 12
2025-07-31 16:34:15 - ui.file_manager - INFO - file_manager.py:460 - 远程文件树更新完成
2025-07-31 16:34:25 - __main__ - INFO - main.py:60 - 应用程序正在关闭...
2025-07-31 16:34:25 - core.performance_monitor - INFO - performance_monitor.py:87 - 性能监控已停止
2025-07-31 16:34:25 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:81 - 开始停止SSH连接池，超时: 1.0秒
2025-07-31 16:34:25 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:138 - SSH连接池已停止，耗时: 0.000秒
2025-07-31 16:34:25 - core.error_handler - INFO - error_handler.py:240 - 全局异常处理器已卸载
2025-07-31 16:34:25 - ui.main_window - INFO - main_window.py:265 - 资源清理完成
2025-07-31 16:34:32 - root - INFO - logger_config.py:90 - 日志系统初始化完成
2025-07-31 16:34:32 - core.error_handler - INFO - error_handler.py:235 - 全局异常处理器已安装
2025-07-31 16:34:32 - __main__ - INFO - main.py:39 - ==================================================
2025-07-31 16:34:32 - __main__ - INFO - main.py:40 - 宇泛升级包工具启动
2025-07-31 16:34:32 - __main__ - INFO - main.py:41 - ==================================================
2025-07-31 16:34:32 - core.performance_monitor - INFO - performance_monitor.py:70 - 性能监控已启动
2025-07-31 16:34:32 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:77 - SSH连接池已启动
2025-07-31 16:34:32 - ui.theme_manager - INFO - theme_manager.py:57 - 已应用主题: default
2025-07-31 16:34:32 - ui.theme_manager - INFO - theme_manager.py:40 - 主题系统初始化完成，当前主题: default
2025-07-31 16:34:32 - core.security_manager - INFO - security_manager.py:151 - 从系统密钥环获取密码: maojunyi@10.67.73.254@YufanUpgradeTool
2025-07-31 16:34:32 - ui.ssh_panel - INFO - ssh_panel.py:35 - SSH面板初始化完成
2025-07-31 16:34:32 - ui.command_panel - INFO - command_panel.py:31 - 命令面板初始化完成
2025-07-31 16:34:33 - ui.file_manager - INFO - file_manager.py:38 - 文件管理面板初始化完成
2025-07-31 16:34:33 - ui.main_window - INFO - main_window.py:37 - 主窗口初始化完成
2025-07-31 16:34:33 - __main__ - INFO - main.py:70 - 启动GUI主循环
2025-07-31 16:34:36 - __main__ - INFO - main.py:60 - 应用程序正在关闭...
2025-07-31 16:34:36 - core.performance_monitor - INFO - performance_monitor.py:87 - 性能监控已停止
2025-07-31 16:34:36 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:81 - 开始停止SSH连接池，超时: 1.0秒
2025-07-31 16:34:36 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:138 - SSH连接池已停止，耗时: 0.000秒
2025-07-31 16:34:36 - core.error_handler - INFO - error_handler.py:240 - 全局异常处理器已卸载
2025-07-31 16:34:36 - ui.main_window - INFO - main_window.py:265 - 资源清理完成
2025-07-31 16:35:00 - root - INFO - logger_config.py:90 - 日志系统初始化完成
2025-07-31 16:35:00 - core.error_handler - INFO - error_handler.py:235 - 全局异常处理器已安装
2025-07-31 16:35:00 - __main__ - INFO - main.py:39 - ==================================================
2025-07-31 16:35:00 - __main__ - INFO - main.py:40 - 宇泛升级包工具启动
2025-07-31 16:35:00 - __main__ - INFO - main.py:41 - ==================================================
2025-07-31 16:35:00 - core.performance_monitor - INFO - performance_monitor.py:70 - 性能监控已启动
2025-07-31 16:35:00 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:77 - SSH连接池已启动
2025-07-31 16:35:00 - ui.theme_manager - INFO - theme_manager.py:57 - 已应用主题: default
2025-07-31 16:35:00 - ui.theme_manager - INFO - theme_manager.py:40 - 主题系统初始化完成，当前主题: default
2025-07-31 16:35:00 - core.security_manager - INFO - security_manager.py:151 - 从系统密钥环获取密码: maojunyi@10.67.73.254@YufanUpgradeTool
2025-07-31 16:35:00 - ui.ssh_panel - INFO - ssh_panel.py:35 - SSH面板初始化完成
2025-07-31 16:35:00 - ui.command_panel - INFO - command_panel.py:31 - 命令面板初始化完成
2025-07-31 16:35:00 - ui.file_manager - INFO - file_manager.py:38 - 文件管理面板初始化完成
2025-07-31 16:35:00 - ui.main_window - INFO - main_window.py:37 - 主窗口初始化完成
2025-07-31 16:35:00 - __main__ - INFO - main.py:70 - 启动GUI主循环
2025-07-31 16:35:06 - core.config_manager - INFO - config_manager.py:94 - 配置文件保存成功: config.json
2025-07-31 16:35:06 - paramiko.transport - INFO - transport.py:1944 - Connected (version 2.0, client OpenSSH_8.9p1)
2025-07-31 16:35:06 - paramiko.transport - INFO - transport.py:1944 - Authentication (password) successful!
2025-07-31 16:35:06 - paramiko.transport.sftp - INFO - sftp.py:169 - [chan 0] Opened sftp connection (server version 3)
2025-07-31 16:35:06 - core.ssh_manager - INFO - ssh_manager.py:48 - 成功连接到 10.67.73.254:22
2025-07-31 16:35:06 - ui.file_manager - INFO - file_manager.py:372 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/
2025-07-31 16:35:06 - ui.ssh_panel - INFO - ssh_panel.py:247 - SSH连接成功: maojunyi@10.67.73.254
2025-07-31 16:35:06 - ui.file_manager - INFO - file_manager.py:379 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/
2025-07-31 16:35:06 - ui.file_manager - INFO - file_manager.py:389 - ls命令输出长度: 830 字符
2025-07-31 16:35:06 - ui.file_manager - INFO - file_manager.py:390 - ls命令错误输出: ''
2025-07-31 16:35:06 - ui.file_manager - INFO - file_manager.py:430 - 解析到 12 个文件/目录项
2025-07-31 16:35:06 - core.security_manager - INFO - security_manager.py:131 - 密码已安全存储到系统密钥环: maojunyi@10.67.73.254@YufanUpgradeTool
2025-07-31 16:35:06 - ui.file_manager - INFO - file_manager.py:441 - 开始更新远程文件树，项目数: 12
2025-07-31 16:35:06 - ui.file_manager - INFO - file_manager.py:460 - 远程文件树更新完成
2025-07-31 16:35:08 - paramiko.transport.sftp - INFO - sftp.py:169 - [chan 0] sftp session closed.
2025-07-31 16:35:08 - core.ssh_manager - INFO - ssh_manager.py:61 - SFTP会话已关闭。
2025-07-31 16:35:08 - core.ssh_manager - INFO - ssh_manager.py:65 - SSH连接已关闭。
2025-07-31 16:35:08 - ui.ssh_panel - INFO - ssh_panel.py:276 - SSH连接已断开
2025-07-31 16:39:09 - __main__ - INFO - main.py:60 - 应用程序正在关闭...
2025-07-31 16:39:09 - core.performance_monitor - INFO - performance_monitor.py:87 - 性能监控已停止
2025-07-31 16:39:09 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:81 - 开始停止SSH连接池，超时: 1.0秒
2025-07-31 16:39:09 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:138 - SSH连接池已停止，耗时: 0.000秒
2025-07-31 16:39:09 - core.error_handler - INFO - error_handler.py:240 - 全局异常处理器已卸载
2025-07-31 16:39:09 - ui.main_window - INFO - main_window.py:265 - 资源清理完成
2025-07-31 16:40:41 - root - INFO - logger_config.py:90 - 日志系统初始化完成
2025-07-31 16:40:41 - core.error_handler - INFO - error_handler.py:235 - 全局异常处理器已安装
2025-07-31 16:40:41 - __main__ - INFO - main.py:39 - ==================================================
2025-07-31 16:40:41 - __main__ - INFO - main.py:40 - 宇泛升级包工具启动
2025-07-31 16:40:41 - __main__ - INFO - main.py:41 - ==================================================
2025-07-31 16:40:41 - core.performance_monitor - INFO - performance_monitor.py:70 - 性能监控已启动
2025-07-31 16:40:41 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:77 - SSH连接池已启动
2025-07-31 16:40:41 - ui.theme_manager - INFO - theme_manager.py:57 - 已应用主题: default
2025-07-31 16:40:41 - ui.theme_manager - INFO - theme_manager.py:40 - 主题系统初始化完成，当前主题: default
2025-07-31 16:40:41 - core.security_manager - INFO - security_manager.py:151 - 从系统密钥环获取密码: maojunyi@10.67.73.254@YufanUpgradeTool
2025-07-31 16:40:41 - ui.ssh_panel - INFO - ssh_panel.py:36 - SSH面板初始化完成
2025-07-31 16:40:41 - ui.command_panel - INFO - command_panel.py:32 - 命令面板初始化完成
2025-07-31 16:40:41 - ui.file_manager - INFO - file_manager.py:38 - 文件管理面板初始化完成
2025-07-31 16:40:41 - ui.main_window - INFO - main_window.py:37 - 主窗口初始化完成
2025-07-31 16:40:41 - __main__ - INFO - main.py:70 - 启动GUI主循环
2025-07-31 16:40:45 - core.config_manager - INFO - config_manager.py:94 - 配置文件保存成功: config.json
2025-07-31 16:40:45 - paramiko.transport - INFO - transport.py:1944 - Connected (version 2.0, client OpenSSH_8.9p1)
2025-07-31 16:40:45 - paramiko.transport - INFO - transport.py:1944 - Authentication (password) successful!
2025-07-31 16:40:45 - paramiko.transport.sftp - INFO - sftp.py:169 - [chan 0] Opened sftp connection (server version 3)
2025-07-31 16:40:45 - core.ssh_manager - INFO - ssh_manager.py:48 - 成功连接到 10.67.73.254:22
2025-07-31 16:40:45 - ui.ssh_panel - INFO - ssh_panel.py:248 - SSH连接成功: maojunyi@10.67.73.254
2025-07-31 16:40:45 - core.security_manager - INFO - security_manager.py:131 - 密码已安全存储到系统密钥环: maojunyi@10.67.73.254@YufanUpgradeTool
2025-07-31 16:40:52 - __main__ - INFO - main.py:60 - 应用程序正在关闭...
2025-07-31 16:40:52 - core.performance_monitor - INFO - performance_monitor.py:87 - 性能监控已停止
2025-07-31 16:40:52 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:81 - 开始停止SSH连接池，超时: 1.0秒
2025-07-31 16:40:52 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:138 - SSH连接池已停止，耗时: 0.000秒
2025-07-31 16:40:52 - core.error_handler - INFO - error_handler.py:240 - 全局异常处理器已卸载
2025-07-31 16:40:52 - ui.main_window - INFO - main_window.py:264 - 资源清理完成
2025-07-31 16:41:33 - root - INFO - logger_config.py:90 - 日志系统初始化完成
2025-07-31 16:41:33 - core.error_handler - INFO - error_handler.py:235 - 全局异常处理器已安装
2025-07-31 16:41:33 - __main__ - INFO - main.py:39 - ==================================================
2025-07-31 16:41:33 - __main__ - INFO - main.py:40 - 宇泛升级包工具启动
2025-07-31 16:41:33 - __main__ - INFO - main.py:41 - ==================================================
2025-07-31 16:41:33 - core.performance_monitor - INFO - performance_monitor.py:70 - 性能监控已启动
2025-07-31 16:41:33 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:77 - SSH连接池已启动
2025-07-31 16:41:33 - ui.theme_manager - INFO - theme_manager.py:57 - 已应用主题: default
2025-07-31 16:41:33 - ui.theme_manager - INFO - theme_manager.py:40 - 主题系统初始化完成，当前主题: default
2025-07-31 16:41:34 - core.security_manager - INFO - security_manager.py:151 - 从系统密钥环获取密码: maojunyi@10.67.73.254@YufanUpgradeTool
2025-07-31 16:41:34 - ui.ssh_panel - INFO - ssh_panel.py:36 - SSH面板初始化完成
2025-07-31 16:41:34 - ui.command_panel - INFO - command_panel.py:32 - 命令面板初始化完成
2025-07-31 16:41:34 - ui.file_manager - INFO - file_manager.py:38 - 文件管理面板初始化完成
2025-07-31 16:41:34 - ui.main_window - INFO - main_window.py:37 - 主窗口初始化完成
2025-07-31 16:41:34 - __main__ - INFO - main.py:70 - 启动GUI主循环
2025-07-31 16:41:36 - core.config_manager - INFO - config_manager.py:94 - 配置文件保存成功: config.json
2025-07-31 16:41:36 - paramiko.transport - INFO - transport.py:1944 - Connected (version 2.0, client OpenSSH_8.9p1)
2025-07-31 16:41:36 - paramiko.transport - INFO - transport.py:1944 - Authentication (password) successful!
2025-07-31 16:41:37 - paramiko.transport.sftp - INFO - sftp.py:169 - [chan 0] Opened sftp connection (server version 3)
2025-07-31 16:41:37 - core.ssh_manager - INFO - ssh_manager.py:48 - 成功连接到 10.67.73.254:22
2025-07-31 16:41:37 - ui.ssh_panel - INFO - ssh_panel.py:248 - SSH连接成功: maojunyi@10.67.73.254
2025-07-31 16:41:37 - core.security_manager - INFO - security_manager.py:131 - 密码已安全存储到系统密钥环: maojunyi@10.67.73.254@YufanUpgradeTool
2025-07-31 16:41:47 - __main__ - INFO - main.py:60 - 应用程序正在关闭...
2025-07-31 16:41:47 - core.performance_monitor - INFO - performance_monitor.py:87 - 性能监控已停止
2025-07-31 16:41:47 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:81 - 开始停止SSH连接池，超时: 1.0秒
2025-07-31 16:41:47 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:138 - SSH连接池已停止，耗时: 0.000秒
2025-07-31 16:41:47 - core.error_handler - INFO - error_handler.py:240 - 全局异常处理器已卸载
2025-07-31 16:41:47 - ui.main_window - INFO - main_window.py:264 - 资源清理完成
2025-07-31 16:41:56 - root - INFO - logger_config.py:90 - 日志系统初始化完成
2025-07-31 16:41:56 - core.error_handler - INFO - error_handler.py:235 - 全局异常处理器已安装
2025-07-31 16:41:56 - __main__ - INFO - main.py:39 - ==================================================
2025-07-31 16:41:56 - __main__ - INFO - main.py:40 - 宇泛升级包工具启动
2025-07-31 16:41:56 - __main__ - INFO - main.py:41 - ==================================================
2025-07-31 16:41:56 - core.performance_monitor - INFO - performance_monitor.py:70 - 性能监控已启动
2025-07-31 16:41:56 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:77 - SSH连接池已启动
2025-07-31 16:41:56 - ui.theme_manager - INFO - theme_manager.py:57 - 已应用主题: default
2025-07-31 16:41:56 - ui.theme_manager - INFO - theme_manager.py:40 - 主题系统初始化完成，当前主题: default
2025-07-31 16:41:57 - core.security_manager - INFO - security_manager.py:151 - 从系统密钥环获取密码: maojunyi@10.67.73.254@YufanUpgradeTool
2025-07-31 16:41:57 - ui.ssh_panel - INFO - ssh_panel.py:36 - SSH面板初始化完成
2025-07-31 16:41:57 - ui.command_panel - INFO - command_panel.py:32 - 命令面板初始化完成
2025-07-31 16:41:57 - ui.file_manager - INFO - file_manager.py:38 - 文件管理面板初始化完成
2025-07-31 16:41:57 - ui.main_window - INFO - main_window.py:37 - 主窗口初始化完成
2025-07-31 16:41:57 - __main__ - INFO - main.py:70 - 启动GUI主循环
2025-07-31 16:42:00 - core.config_manager - INFO - config_manager.py:94 - 配置文件保存成功: config.json
2025-07-31 16:42:00 - paramiko.transport - INFO - transport.py:1944 - Connected (version 2.0, client OpenSSH_8.9p1)
2025-07-31 16:42:01 - paramiko.transport - INFO - transport.py:1944 - Authentication (password) successful!
2025-07-31 16:42:01 - paramiko.transport.sftp - INFO - sftp.py:169 - [chan 0] Opened sftp connection (server version 3)
2025-07-31 16:42:01 - core.ssh_manager - INFO - ssh_manager.py:48 - 成功连接到 10.67.73.254:22
2025-07-31 16:42:01 - ui.ssh_panel - INFO - ssh_panel.py:248 - SSH连接成功: maojunyi@10.67.73.254
2025-07-31 16:42:01 - core.security_manager - INFO - security_manager.py:131 - 密码已安全存储到系统密钥环: maojunyi@10.67.73.254@YufanUpgradeTool
2025-07-31 16:42:46 - __main__ - INFO - main.py:60 - 应用程序正在关闭...
2025-07-31 16:42:46 - core.performance_monitor - INFO - performance_monitor.py:87 - 性能监控已停止
2025-07-31 16:42:46 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:81 - 开始停止SSH连接池，超时: 1.0秒
2025-07-31 16:42:46 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:138 - SSH连接池已停止，耗时: 0.000秒
2025-07-31 16:42:46 - core.error_handler - INFO - error_handler.py:240 - 全局异常处理器已卸载
2025-07-31 16:42:46 - ui.main_window - INFO - main_window.py:264 - 资源清理完成
2025-07-31 16:49:22 - root - INFO - logger_config.py:90 - 日志系统初始化完成
2025-07-31 16:49:22 - core.error_handler - INFO - error_handler.py:235 - 全局异常处理器已安装
2025-07-31 16:49:22 - __main__ - INFO - main.py:39 - ==================================================
2025-07-31 16:49:22 - __main__ - INFO - main.py:40 - 宇泛升级包工具启动
2025-07-31 16:49:22 - __main__ - INFO - main.py:41 - ==================================================
2025-07-31 16:49:22 - core.performance_monitor - INFO - performance_monitor.py:70 - 性能监控已启动
2025-07-31 16:49:22 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:77 - SSH连接池已启动
2025-07-31 16:49:22 - ui.theme_manager - INFO - theme_manager.py:57 - 已应用主题: default
2025-07-31 16:49:22 - ui.theme_manager - INFO - theme_manager.py:40 - 主题系统初始化完成，当前主题: default
2025-07-31 16:49:22 - core.security_manager - INFO - security_manager.py:151 - 从系统密钥环获取密码: maojunyi@10.67.73.254@YufanUpgradeTool
2025-07-31 16:49:22 - ui.ssh_panel - INFO - ssh_panel.py:36 - SSH面板初始化完成
2025-07-31 16:49:22 - ui.command_panel - INFO - command_panel.py:32 - 命令面板初始化完成
2025-07-31 16:49:22 - ui.file_manager - INFO - file_manager.py:38 - 文件管理面板初始化完成
2025-07-31 16:49:22 - ui.main_window - INFO - main_window.py:90 - 开始设置组件间通信
2025-07-31 16:49:22 - ui.main_window - INFO - main_window.py:91 - SSH面板: True
2025-07-31 16:49:22 - ui.main_window - INFO - main_window.py:92 - 命令面板: True
2025-07-31 16:49:22 - ui.main_window - INFO - main_window.py:93 - 文件管理器: True
2025-07-31 16:49:22 - ui.ssh_panel - INFO - ssh_panel.py:382 - SSH面板连接回调已设置: True
2025-07-31 16:49:22 - ui.main_window - INFO - main_window.py:114 - SSH面板连接回调已设置
2025-07-31 16:49:22 - ui.main_window - INFO - main_window.py:38 - 主窗口初始化完成
2025-07-31 16:49:22 - __main__ - INFO - main.py:70 - 启动GUI主循环
2025-07-31 16:49:25 - core.config_manager - INFO - config_manager.py:94 - 配置文件保存成功: config.json
2025-07-31 16:49:25 - paramiko.transport - INFO - transport.py:1944 - Connected (version 2.0, client OpenSSH_8.9p1)
2025-07-31 16:49:25 - paramiko.transport - INFO - transport.py:1944 - Authentication (password) successful!
2025-07-31 16:49:26 - paramiko.transport.sftp - INFO - sftp.py:169 - [chan 0] Opened sftp connection (server version 3)
2025-07-31 16:49:26 - core.ssh_manager - INFO - ssh_manager.py:48 - 成功连接到 10.67.73.254:22
2025-07-31 16:49:26 - ui.ssh_panel - INFO - ssh_panel.py:245 - 准备通知其他组件连接成功，回调函数: True
2025-07-31 16:49:26 - ui.ssh_panel - INFO - ssh_panel.py:247 - 调用连接回调函数
2025-07-31 16:49:26 - ui.main_window - INFO - main_window.py:99 - 主窗口收到连接状态变化: connected=True
2025-07-31 16:49:26 - ui.main_window - INFO - main_window.py:102 - 通知命令面板连接状态变化
2025-07-31 16:49:26 - ui.command_panel - INFO - command_panel.py:155 - 命令面板收到连接状态变化: connected=True
2025-07-31 16:49:26 - ui.command_panel - INFO - command_panel.py:164 - 快捷命令按钮状态已更新为: 可用
2025-07-31 16:49:26 - ui.main_window - INFO - main_window.py:108 - 通知文件管理器连接状态变化
2025-07-31 16:49:26 - ui.file_manager - INFO - file_manager.py:372 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/
2025-07-31 16:49:26 - ui.ssh_panel - INFO - ssh_panel.py:252 - SSH连接成功: maojunyi@10.67.73.254
2025-07-31 16:49:26 - ui.file_manager - INFO - file_manager.py:379 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/
2025-07-31 16:49:26 - ui.file_manager - INFO - file_manager.py:389 - ls命令输出长度: 830 字符
2025-07-31 16:49:26 - ui.file_manager - INFO - file_manager.py:390 - ls命令错误输出: ''
2025-07-31 16:49:26 - ui.file_manager - INFO - file_manager.py:430 - 解析到 12 个文件/目录项
2025-07-31 16:49:26 - core.security_manager - INFO - security_manager.py:131 - 密码已安全存储到系统密钥环: maojunyi@10.67.73.254@YufanUpgradeTool
2025-07-31 16:49:26 - ui.file_manager - INFO - file_manager.py:441 - 开始更新远程文件树，项目数: 12
2025-07-31 16:49:26 - ui.file_manager - INFO - file_manager.py:460 - 远程文件树更新完成
2025-07-31 16:49:45 - ui.file_manager - INFO - file_manager.py:372 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/
2025-07-31 16:49:45 - ui.file_manager - INFO - file_manager.py:379 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/
2025-07-31 16:49:45 - ui.file_manager - INFO - file_manager.py:389 - ls命令输出长度: 766 字符
2025-07-31 16:49:45 - ui.file_manager - INFO - file_manager.py:390 - ls命令错误输出: ''
2025-07-31 16:49:45 - ui.file_manager - INFO - file_manager.py:430 - 解析到 11 个文件/目录项
2025-07-31 16:49:45 - ui.file_manager - INFO - file_manager.py:441 - 开始更新远程文件树，项目数: 11
2025-07-31 16:49:45 - ui.file_manager - INFO - file_manager.py:460 - 远程文件树更新完成
2025-07-31 16:50:16 - ui.file_manager - INFO - file_manager.py:372 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/in_pkg
2025-07-31 16:50:16 - ui.file_manager - INFO - file_manager.py:379 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/in_pkg
2025-07-31 16:50:16 - ui.file_manager - INFO - file_manager.py:389 - ls命令输出长度: 189 字符
2025-07-31 16:50:16 - ui.file_manager - INFO - file_manager.py:390 - ls命令错误输出: ''
2025-07-31 16:50:16 - ui.file_manager - INFO - file_manager.py:430 - 解析到 2 个文件/目录项
2025-07-31 16:50:16 - ui.file_manager - INFO - file_manager.py:441 - 开始更新远程文件树，项目数: 2
2025-07-31 16:50:16 - ui.file_manager - INFO - file_manager.py:460 - 远程文件树更新完成
2025-07-31 16:50:19 - ui.file_manager - INFO - file_manager.py:372 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack
2025-07-31 16:50:19 - ui.file_manager - INFO - file_manager.py:379 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack
2025-07-31 16:50:19 - ui.file_manager - INFO - file_manager.py:389 - ls命令输出长度: 766 字符
2025-07-31 16:50:19 - ui.file_manager - INFO - file_manager.py:390 - ls命令错误输出: ''
2025-07-31 16:50:19 - ui.file_manager - INFO - file_manager.py:430 - 解析到 11 个文件/目录项
2025-07-31 16:50:19 - ui.file_manager - INFO - file_manager.py:441 - 开始更新远程文件树，项目数: 11
2025-07-31 16:50:19 - ui.file_manager - INFO - file_manager.py:460 - 远程文件树更新完成
2025-07-31 16:51:33 - ui.file_manager - INFO - file_manager.py:372 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack
2025-07-31 16:51:33 - ui.file_manager - INFO - file_manager.py:379 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack
2025-07-31 16:51:33 - ui.file_manager - INFO - file_manager.py:389 - ls命令输出长度: 830 字符
2025-07-31 16:51:33 - ui.file_manager - INFO - file_manager.py:390 - ls命令错误输出: ''
2025-07-31 16:51:33 - ui.file_manager - INFO - file_manager.py:430 - 解析到 12 个文件/目录项
2025-07-31 16:51:33 - ui.file_manager - INFO - file_manager.py:441 - 开始更新远程文件树，项目数: 12
2025-07-31 16:51:33 - ui.file_manager - INFO - file_manager.py:460 - 远程文件树更新完成
2025-07-31 16:51:37 - ui.file_manager - INFO - file_manager.py:372 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/unpacked
2025-07-31 16:51:37 - ui.file_manager - INFO - file_manager.py:379 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/unpacked
2025-07-31 16:51:37 - ui.file_manager - INFO - file_manager.py:389 - ls命令输出长度: 548 字符
2025-07-31 16:51:37 - ui.file_manager - INFO - file_manager.py:390 - ls命令错误输出: ''
2025-07-31 16:51:37 - ui.file_manager - INFO - file_manager.py:430 - 解析到 7 个文件/目录项
2025-07-31 16:51:37 - ui.file_manager - INFO - file_manager.py:441 - 开始更新远程文件树，项目数: 7
2025-07-31 16:51:37 - ui.file_manager - INFO - file_manager.py:460 - 远程文件树更新完成
2025-07-31 16:51:42 - ui.file_manager - INFO - file_manager.py:372 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted
2025-07-31 16:51:42 - ui.file_manager - INFO - file_manager.py:379 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted
2025-07-31 16:51:42 - ui.file_manager - INFO - file_manager.py:389 - ls命令输出长度: 2101 字符
2025-07-31 16:51:42 - ui.file_manager - INFO - file_manager.py:390 - ls命令错误输出: ''
2025-07-31 16:51:42 - ui.file_manager - INFO - file_manager.py:430 - 解析到 34 个文件/目录项
2025-07-31 16:51:42 - ui.file_manager - INFO - file_manager.py:441 - 开始更新远程文件树，项目数: 34
2025-07-31 16:51:42 - ui.file_manager - INFO - file_manager.py:460 - 远程文件树更新完成
2025-07-31 16:51:43 - ui.file_manager - INFO - file_manager.py:372 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application
2025-07-31 16:51:43 - ui.file_manager - INFO - file_manager.py:379 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application
2025-07-31 16:51:43 - ui.file_manager - INFO - file_manager.py:389 - ls命令输出长度: 169 字符
2025-07-31 16:51:43 - ui.file_manager - INFO - file_manager.py:390 - ls命令错误输出: ''
2025-07-31 16:51:43 - ui.file_manager - INFO - file_manager.py:430 - 解析到 2 个文件/目录项
2025-07-31 16:51:43 - ui.file_manager - INFO - file_manager.py:441 - 开始更新远程文件树，项目数: 2
2025-07-31 16:51:43 - ui.file_manager - INFO - file_manager.py:460 - 远程文件树更新完成
2025-07-31 16:51:44 - ui.file_manager - INFO - file_manager.py:372 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron
2025-07-31 16:51:44 - ui.file_manager - INFO - file_manager.py:379 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron
2025-07-31 16:51:44 - ui.file_manager - INFO - file_manager.py:389 - ls命令输出长度: 329 字符
2025-07-31 16:51:44 - ui.file_manager - INFO - file_manager.py:390 - ls命令错误输出: ''
2025-07-31 16:51:44 - ui.file_manager - INFO - file_manager.py:430 - 解析到 5 个文件/目录项
2025-07-31 16:51:44 - ui.file_manager - INFO - file_manager.py:441 - 开始更新远程文件树，项目数: 5
2025-07-31 16:51:44 - ui.file_manager - INFO - file_manager.py:460 - 远程文件树更新完成
2025-07-31 16:51:45 - ui.file_manager - INFO - file_manager.py:372 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron/res
2025-07-31 16:51:45 - ui.file_manager - INFO - file_manager.py:379 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron/res
2025-07-31 16:51:45 - ui.file_manager - INFO - file_manager.py:389 - ls命令输出长度: 584 字符
2025-07-31 16:51:45 - ui.file_manager - INFO - file_manager.py:390 - ls命令错误输出: ''
2025-07-31 16:51:45 - ui.file_manager - INFO - file_manager.py:430 - 解析到 9 个文件/目录项
2025-07-31 16:51:45 - ui.file_manager - INFO - file_manager.py:441 - 开始更新远程文件树，项目数: 9
2025-07-31 16:51:45 - ui.file_manager - INFO - file_manager.py:460 - 远程文件树更新完成
2025-07-31 16:51:47 - ui.file_manager - INFO - file_manager.py:372 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron/res/data
2025-07-31 16:51:47 - ui.file_manager - INFO - file_manager.py:379 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron/res/data
2025-07-31 16:51:47 - ui.file_manager - INFO - file_manager.py:389 - ls命令输出长度: 227 字符
2025-07-31 16:51:47 - ui.file_manager - INFO - file_manager.py:390 - ls命令错误输出: ''
2025-07-31 16:51:47 - ui.file_manager - INFO - file_manager.py:430 - 解析到 3 个文件/目录项
2025-07-31 16:51:47 - ui.file_manager - INFO - file_manager.py:441 - 开始更新远程文件树，项目数: 3
2025-07-31 16:51:47 - ui.file_manager - INFO - file_manager.py:460 - 远程文件树更新完成
2025-07-31 17:03:34 - __main__ - INFO - main.py:60 - 应用程序正在关闭...
2025-07-31 17:03:34 - core.performance_monitor - INFO - performance_monitor.py:87 - 性能监控已停止
2025-07-31 17:03:34 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:81 - 开始停止SSH连接池，超时: 1.0秒
2025-07-31 17:03:34 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:138 - SSH连接池已停止，耗时: 0.000秒
2025-07-31 17:03:34 - core.error_handler - INFO - error_handler.py:240 - 全局异常处理器已卸载
2025-07-31 17:03:34 - paramiko.transport.sftp - INFO - sftp.py:169 - [chan 0] sftp session closed.
2025-07-31 17:03:34 - core.ssh_manager - INFO - ssh_manager.py:61 - SFTP会话已关闭。
2025-07-31 17:03:34 - core.ssh_manager - INFO - ssh_manager.py:65 - SSH连接已关闭。
2025-07-31 17:03:34 - ui.main_window - INFO - main_window.py:99 - 主窗口收到连接状态变化: connected=False
2025-07-31 17:03:34 - ui.main_window - INFO - main_window.py:102 - 通知命令面板连接状态变化
2025-07-31 17:03:34 - ui.command_panel - INFO - command_panel.py:155 - 命令面板收到连接状态变化: connected=False
2025-07-31 17:03:34 - ui.command_panel - INFO - command_panel.py:164 - 快捷命令按钮状态已更新为: 禁用
2025-07-31 17:03:34 - ui.main_window - INFO - main_window.py:108 - 通知文件管理器连接状态变化
2025-07-31 17:03:34 - ui.ssh_panel - INFO - ssh_panel.py:280 - SSH连接已断开
2025-07-31 17:03:34 - core.config_manager - INFO - config_manager.py:94 - 配置文件保存成功: config.json
2025-07-31 17:03:34 - ui.main_window - INFO - main_window.py:282 - 资源清理完成
2025-07-31 17:10:06 - root - INFO - logger_config.py:90 - 日志系统初始化完成
2025-07-31 17:10:06 - core.error_handler - INFO - error_handler.py:235 - 全局异常处理器已安装
2025-07-31 17:10:06 - __main__ - INFO - main.py:39 - ==================================================
2025-07-31 17:10:06 - __main__ - INFO - main.py:40 - 宇泛升级包工具启动
2025-07-31 17:10:06 - __main__ - INFO - main.py:41 - ==================================================
2025-07-31 17:10:06 - core.performance_monitor - INFO - performance_monitor.py:70 - 性能监控已启动
2025-07-31 17:10:06 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:77 - SSH连接池已启动
2025-07-31 17:10:06 - ui.theme_manager - INFO - theme_manager.py:57 - 已应用主题: default
2025-07-31 17:10:06 - ui.theme_manager - INFO - theme_manager.py:40 - 主题系统初始化完成，当前主题: default
2025-07-31 17:10:06 - core.security_manager - INFO - security_manager.py:151 - 从系统密钥环获取密码: maojunyi@10.67.73.254@YufanUpgradeTool
2025-07-31 17:10:06 - ui.ssh_panel - INFO - ssh_panel.py:36 - SSH面板初始化完成
2025-07-31 17:10:06 - ui.command_panel - INFO - command_panel.py:32 - 命令面板初始化完成
2025-07-31 17:10:07 - ui.file_manager - INFO - file_manager.py:38 - 文件管理面板初始化完成
2025-07-31 17:10:07 - ui.main_window - INFO - main_window.py:90 - 开始设置组件间通信
2025-07-31 17:10:07 - ui.main_window - INFO - main_window.py:91 - SSH面板: True
2025-07-31 17:10:07 - ui.main_window - INFO - main_window.py:92 - 命令面板: True
2025-07-31 17:10:07 - ui.main_window - INFO - main_window.py:93 - 文件管理器: True
2025-07-31 17:10:07 - ui.ssh_panel - INFO - ssh_panel.py:382 - SSH面板连接回调已设置: True
2025-07-31 17:10:07 - ui.main_window - INFO - main_window.py:114 - SSH面板连接回调已设置
2025-07-31 17:10:07 - ui.main_window - INFO - main_window.py:38 - 主窗口初始化完成
2025-07-31 17:10:07 - __main__ - INFO - main.py:70 - 启动GUI主循环
2025-07-31 17:10:09 - core.config_manager - INFO - config_manager.py:94 - 配置文件保存成功: config.json
2025-07-31 17:10:09 - paramiko.transport - INFO - transport.py:1944 - Connected (version 2.0, client OpenSSH_8.9p1)
2025-07-31 17:10:10 - paramiko.transport - INFO - transport.py:1944 - Authentication (password) successful!
2025-07-31 17:10:10 - paramiko.transport.sftp - INFO - sftp.py:169 - [chan 0] Opened sftp connection (server version 3)
2025-07-31 17:10:10 - core.ssh_manager - INFO - ssh_manager.py:48 - 成功连接到 10.67.73.254:22
2025-07-31 17:10:10 - ui.ssh_panel - INFO - ssh_panel.py:245 - 准备通知其他组件连接成功，回调函数: True
2025-07-31 17:10:10 - ui.ssh_panel - INFO - ssh_panel.py:247 - 调用连接回调函数
2025-07-31 17:10:10 - ui.main_window - INFO - main_window.py:99 - 主窗口收到连接状态变化: connected=True
2025-07-31 17:10:10 - ui.main_window - INFO - main_window.py:102 - 通知命令面板连接状态变化
2025-07-31 17:10:10 - ui.command_panel - INFO - command_panel.py:155 - 命令面板收到连接状态变化: connected=True
2025-07-31 17:10:10 - ui.command_panel - INFO - command_panel.py:164 - 快捷命令按钮状态已更新为: 可用
2025-07-31 17:10:10 - ui.main_window - INFO - main_window.py:108 - 通知文件管理器连接状态变化
2025-07-31 17:10:10 - ui.file_manager - INFO - file_manager.py:384 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/
2025-07-31 17:10:10 - ui.ssh_panel - INFO - ssh_panel.py:252 - SSH连接成功: maojunyi@10.67.73.254
2025-07-31 17:10:10 - ui.file_manager - INFO - file_manager.py:391 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/
2025-07-31 17:10:10 - ui.file_manager - INFO - file_manager.py:401 - ls命令输出长度: 830 字符
2025-07-31 17:10:10 - ui.file_manager - INFO - file_manager.py:402 - ls命令错误输出: ''
2025-07-31 17:10:10 - ui.file_manager - INFO - file_manager.py:442 - 解析到 12 个文件/目录项
2025-07-31 17:10:10 - core.security_manager - INFO - security_manager.py:131 - 密码已安全存储到系统密钥环: maojunyi@10.67.73.254@YufanUpgradeTool
2025-07-31 17:10:10 - ui.file_manager - INFO - file_manager.py:453 - 开始更新远程文件树，项目数: 12
2025-07-31 17:10:10 - ui.file_manager - INFO - file_manager.py:472 - 远程文件树更新完成
2025-07-31 17:10:43 - ui.file_manager - INFO - file_manager.py:384 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/unpacked
2025-07-31 17:10:43 - ui.file_manager - INFO - file_manager.py:391 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/unpacked
2025-07-31 17:10:43 - ui.file_manager - INFO - file_manager.py:401 - ls命令输出长度: 548 字符
2025-07-31 17:10:43 - ui.file_manager - INFO - file_manager.py:402 - ls命令错误输出: ''
2025-07-31 17:10:43 - ui.file_manager - INFO - file_manager.py:442 - 解析到 7 个文件/目录项
2025-07-31 17:10:43 - ui.file_manager - INFO - file_manager.py:453 - 开始更新远程文件树，项目数: 7
2025-07-31 17:10:43 - ui.file_manager - INFO - file_manager.py:472 - 远程文件树更新完成
2025-07-31 17:10:44 - ui.file_manager - INFO - file_manager.py:384 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted
2025-07-31 17:10:44 - ui.file_manager - INFO - file_manager.py:391 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted
2025-07-31 17:10:44 - ui.file_manager - INFO - file_manager.py:401 - ls命令输出长度: 2101 字符
2025-07-31 17:10:44 - ui.file_manager - INFO - file_manager.py:402 - ls命令错误输出: ''
2025-07-31 17:10:44 - ui.file_manager - INFO - file_manager.py:442 - 解析到 34 个文件/目录项
2025-07-31 17:10:44 - ui.file_manager - INFO - file_manager.py:453 - 开始更新远程文件树，项目数: 34
2025-07-31 17:10:44 - ui.file_manager - INFO - file_manager.py:472 - 远程文件树更新完成
2025-07-31 17:10:48 - ui.file_manager - INFO - file_manager.py:384 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application
2025-07-31 17:10:48 - ui.file_manager - INFO - file_manager.py:391 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application
2025-07-31 17:10:48 - ui.file_manager - INFO - file_manager.py:401 - ls命令输出长度: 169 字符
2025-07-31 17:10:48 - ui.file_manager - INFO - file_manager.py:402 - ls命令错误输出: ''
2025-07-31 17:10:48 - ui.file_manager - INFO - file_manager.py:442 - 解析到 2 个文件/目录项
2025-07-31 17:10:48 - ui.file_manager - INFO - file_manager.py:453 - 开始更新远程文件树，项目数: 2
2025-07-31 17:10:48 - ui.file_manager - INFO - file_manager.py:472 - 远程文件树更新完成
2025-07-31 17:10:49 - ui.file_manager - INFO - file_manager.py:384 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron
2025-07-31 17:10:49 - ui.file_manager - INFO - file_manager.py:391 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron
2025-07-31 17:10:49 - ui.file_manager - INFO - file_manager.py:401 - ls命令输出长度: 329 字符
2025-07-31 17:10:49 - ui.file_manager - INFO - file_manager.py:402 - ls命令错误输出: ''
2025-07-31 17:10:49 - ui.file_manager - INFO - file_manager.py:442 - 解析到 5 个文件/目录项
2025-07-31 17:10:49 - ui.file_manager - INFO - file_manager.py:453 - 开始更新远程文件树，项目数: 5
2025-07-31 17:10:49 - ui.file_manager - INFO - file_manager.py:472 - 远程文件树更新完成
2025-07-31 17:10:50 - ui.file_manager - INFO - file_manager.py:384 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron/res
2025-07-31 17:10:50 - ui.file_manager - INFO - file_manager.py:391 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron/res
2025-07-31 17:10:50 - ui.file_manager - INFO - file_manager.py:401 - ls命令输出长度: 584 字符
2025-07-31 17:10:50 - ui.file_manager - INFO - file_manager.py:402 - ls命令错误输出: ''
2025-07-31 17:10:50 - ui.file_manager - INFO - file_manager.py:442 - 解析到 9 个文件/目录项
2025-07-31 17:10:50 - ui.file_manager - INFO - file_manager.py:453 - 开始更新远程文件树，项目数: 9
2025-07-31 17:10:50 - ui.file_manager - INFO - file_manager.py:472 - 远程文件树更新完成
2025-07-31 17:10:52 - ui.file_manager - INFO - file_manager.py:384 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron/res/data
2025-07-31 17:10:52 - ui.file_manager - INFO - file_manager.py:391 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron/res/data
2025-07-31 17:10:52 - ui.file_manager - INFO - file_manager.py:401 - ls命令输出长度: 227 字符
2025-07-31 17:10:52 - ui.file_manager - INFO - file_manager.py:402 - ls命令错误输出: ''
2025-07-31 17:10:52 - ui.file_manager - INFO - file_manager.py:442 - 解析到 3 个文件/目录项
2025-07-31 17:10:52 - ui.file_manager - INFO - file_manager.py:453 - 开始更新远程文件树，项目数: 3
2025-07-31 17:10:52 - ui.file_manager - INFO - file_manager.py:472 - 远程文件树更新完成
2025-07-31 17:11:04 - core.ssh_manager - INFO - ssh_manager.py:222 - 开始下载文件: /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron/res/data/default.ini 到 default.ini
2025-07-31 17:11:04 - core.ssh_manager - INFO - ssh_manager.py:233 - 文件下载成功: /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron/res/data/default.ini -> default.ini
2025-07-31 17:11:04 - ui.file_manager - INFO - file_manager.py:699 - 下载文件: 下载成功
2025-07-31 17:11:21 - ui.file_manager - INFO - file_manager.py:384 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron/res/data
2025-07-31 17:11:21 - ui.file_manager - INFO - file_manager.py:391 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron/res/data
2025-07-31 17:11:21 - ui.file_manager - INFO - file_manager.py:401 - ls命令输出长度: 227 字符
2025-07-31 17:11:21 - ui.file_manager - INFO - file_manager.py:402 - ls命令错误输出: ''
2025-07-31 17:11:21 - ui.file_manager - INFO - file_manager.py:442 - 解析到 3 个文件/目录项
2025-07-31 17:11:21 - ui.file_manager - INFO - file_manager.py:453 - 开始更新远程文件树，项目数: 3
2025-07-31 17:11:21 - ui.file_manager - INFO - file_manager.py:472 - 远程文件树更新完成
2025-07-31 17:11:27 - ui.file_manager - INFO - file_manager.py:384 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron/res/data
2025-07-31 17:11:27 - ui.file_manager - INFO - file_manager.py:391 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron/res/data
2025-07-31 17:11:27 - ui.file_manager - INFO - file_manager.py:401 - ls命令输出长度: 227 字符
2025-07-31 17:11:27 - ui.file_manager - INFO - file_manager.py:402 - ls命令错误输出: ''
2025-07-31 17:11:27 - ui.file_manager - INFO - file_manager.py:442 - 解析到 3 个文件/目录项
2025-07-31 17:11:27 - ui.file_manager - INFO - file_manager.py:453 - 开始更新远程文件树，项目数: 3
2025-07-31 17:11:27 - ui.file_manager - INFO - file_manager.py:472 - 远程文件树更新完成
2025-07-31 17:11:30 - ui.file_manager - INFO - file_manager.py:384 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron/res
2025-07-31 17:11:30 - ui.file_manager - INFO - file_manager.py:391 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron/res
2025-07-31 17:11:30 - ui.file_manager - INFO - file_manager.py:401 - ls命令输出长度: 584 字符
2025-07-31 17:11:30 - ui.file_manager - INFO - file_manager.py:402 - ls命令错误输出: ''
2025-07-31 17:11:30 - ui.file_manager - INFO - file_manager.py:442 - 解析到 9 个文件/目录项
2025-07-31 17:11:30 - ui.file_manager - INFO - file_manager.py:453 - 开始更新远程文件树，项目数: 9
2025-07-31 17:11:30 - ui.file_manager - INFO - file_manager.py:472 - 远程文件树更新完成
2025-07-31 17:11:36 - ui.file_manager - INFO - file_manager.py:384 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron/res/data
2025-07-31 17:11:36 - ui.file_manager - INFO - file_manager.py:391 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron/res/data
2025-07-31 17:11:36 - ui.file_manager - INFO - file_manager.py:401 - ls命令输出长度: 227 字符
2025-07-31 17:11:36 - ui.file_manager - INFO - file_manager.py:402 - ls命令错误输出: ''
2025-07-31 17:11:36 - ui.file_manager - INFO - file_manager.py:442 - 解析到 3 个文件/目录项
2025-07-31 17:11:36 - ui.file_manager - INFO - file_manager.py:453 - 开始更新远程文件树，项目数: 3
2025-07-31 17:11:36 - ui.file_manager - INFO - file_manager.py:472 - 远程文件树更新完成
2025-07-31 17:11:50 - core.ssh_manager - INFO - ssh_manager.py:117 - 开始上传文件: config.json 到 /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron/res/data/config.json
2025-07-31 17:11:50 - core.ssh_manager - INFO - ssh_manager.py:130 - 文件上传成功: config.json -> /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron/res/data/config.json
2025-07-31 17:11:50 - ui.file_manager - INFO - file_manager.py:654 - 上传文件: 上传成功
2025-07-31 17:11:50 - ui.file_manager - INFO - file_manager.py:384 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron/res/data
2025-07-31 17:11:50 - ui.file_manager - INFO - file_manager.py:391 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron/res/data
2025-07-31 17:11:50 - ui.file_manager - INFO - file_manager.py:401 - ls命令输出长度: 288 字符
2025-07-31 17:11:50 - ui.file_manager - INFO - file_manager.py:402 - ls命令错误输出: ''
2025-07-31 17:11:50 - ui.file_manager - INFO - file_manager.py:442 - 解析到 4 个文件/目录项
2025-07-31 17:11:50 - ui.file_manager - INFO - file_manager.py:453 - 开始更新远程文件树，项目数: 4
2025-07-31 17:11:50 - ui.file_manager - INFO - file_manager.py:472 - 远程文件树更新完成
2025-07-31 17:13:47 - ui.file_manager - INFO - file_manager.py:384 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron/res
2025-07-31 17:13:47 - ui.file_manager - INFO - file_manager.py:391 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron/res
2025-07-31 17:13:47 - ui.file_manager - INFO - file_manager.py:401 - ls命令输出长度: 584 字符
2025-07-31 17:13:47 - ui.file_manager - INFO - file_manager.py:402 - ls命令错误输出: ''
2025-07-31 17:13:47 - ui.file_manager - INFO - file_manager.py:442 - 解析到 9 个文件/目录项
2025-07-31 17:13:47 - ui.file_manager - INFO - file_manager.py:453 - 开始更新远程文件树，项目数: 9
2025-07-31 17:13:47 - ui.file_manager - INFO - file_manager.py:472 - 远程文件树更新完成
2025-07-31 17:13:51 - ui.file_manager - INFO - file_manager.py:384 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron
2025-07-31 17:13:51 - ui.file_manager - INFO - file_manager.py:391 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron
2025-07-31 17:13:51 - ui.file_manager - INFO - file_manager.py:401 - ls命令输出长度: 329 字符
2025-07-31 17:13:51 - ui.file_manager - INFO - file_manager.py:402 - ls命令错误输出: ''
2025-07-31 17:13:51 - ui.file_manager - INFO - file_manager.py:442 - 解析到 5 个文件/目录项
2025-07-31 17:13:51 - ui.file_manager - INFO - file_manager.py:453 - 开始更新远程文件树，项目数: 5
2025-07-31 17:13:51 - ui.file_manager - INFO - file_manager.py:472 - 远程文件树更新完成
2025-07-31 17:14:06 - ui.file_manager - INFO - file_manager.py:384 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application
2025-07-31 17:14:06 - ui.file_manager - INFO - file_manager.py:391 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application
2025-07-31 17:14:06 - ui.file_manager - INFO - file_manager.py:401 - ls命令输出长度: 169 字符
2025-07-31 17:14:06 - ui.file_manager - INFO - file_manager.py:402 - ls命令错误输出: ''
2025-07-31 17:14:06 - ui.file_manager - INFO - file_manager.py:442 - 解析到 2 个文件/目录项
2025-07-31 17:14:06 - ui.file_manager - INFO - file_manager.py:453 - 开始更新远程文件树，项目数: 2
2025-07-31 17:14:06 - ui.file_manager - INFO - file_manager.py:472 - 远程文件树更新完成
2025-07-31 17:14:07 - ui.file_manager - INFO - file_manager.py:384 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted
2025-07-31 17:14:07 - ui.file_manager - INFO - file_manager.py:391 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted
2025-07-31 17:14:07 - ui.file_manager - INFO - file_manager.py:401 - ls命令输出长度: 2101 字符
2025-07-31 17:14:07 - ui.file_manager - INFO - file_manager.py:402 - ls命令错误输出: ''
2025-07-31 17:14:07 - ui.file_manager - INFO - file_manager.py:442 - 解析到 34 个文件/目录项
2025-07-31 17:14:07 - ui.file_manager - INFO - file_manager.py:453 - 开始更新远程文件树，项目数: 34
2025-07-31 17:14:07 - ui.file_manager - INFO - file_manager.py:472 - 远程文件树更新完成
2025-07-31 17:14:07 - ui.file_manager - INFO - file_manager.py:384 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/unpacked
2025-07-31 17:14:07 - ui.file_manager - INFO - file_manager.py:391 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/unpacked
2025-07-31 17:14:07 - ui.file_manager - INFO - file_manager.py:401 - ls命令输出长度: 548 字符
2025-07-31 17:14:07 - ui.file_manager - INFO - file_manager.py:402 - ls命令错误输出: ''
2025-07-31 17:14:07 - ui.file_manager - INFO - file_manager.py:442 - 解析到 7 个文件/目录项
2025-07-31 17:14:07 - ui.file_manager - INFO - file_manager.py:453 - 开始更新远程文件树，项目数: 7
2025-07-31 17:14:07 - ui.file_manager - INFO - file_manager.py:472 - 远程文件树更新完成
2025-07-31 17:14:08 - ui.file_manager - INFO - file_manager.py:384 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack
2025-07-31 17:14:08 - ui.file_manager - INFO - file_manager.py:391 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack
2025-07-31 17:14:08 - ui.file_manager - INFO - file_manager.py:401 - ls命令输出长度: 830 字符
2025-07-31 17:14:08 - ui.file_manager - INFO - file_manager.py:402 - ls命令错误输出: ''
2025-07-31 17:14:08 - ui.file_manager - INFO - file_manager.py:442 - 解析到 12 个文件/目录项
2025-07-31 17:14:08 - ui.file_manager - INFO - file_manager.py:453 - 开始更新远程文件树，项目数: 12
2025-07-31 17:14:08 - ui.file_manager - INFO - file_manager.py:472 - 远程文件树更新完成
2025-07-31 17:14:09 - ui.file_manager - INFO - file_manager.py:384 - 开始刷新远程文件列表，路径: /home/<USER>
2025-07-31 17:14:09 - ui.file_manager - INFO - file_manager.py:391 - 执行远程命令: ls -la /home/<USER>
2025-07-31 17:14:09 - ui.file_manager - INFO - file_manager.py:401 - ls命令输出长度: 3755 字符
2025-07-31 17:14:09 - ui.file_manager - INFO - file_manager.py:402 - ls命令错误输出: ''
2025-07-31 17:14:09 - ui.file_manager - INFO - file_manager.py:442 - 解析到 55 个文件/目录项
2025-07-31 17:14:09 - ui.file_manager - INFO - file_manager.py:453 - 开始更新远程文件树，项目数: 55
2025-07-31 17:14:09 - ui.file_manager - INFO - file_manager.py:472 - 远程文件树更新完成
2025-07-31 17:14:09 - ui.file_manager - INFO - file_manager.py:384 - 开始刷新远程文件列表，路径: /home
2025-07-31 17:14:09 - ui.file_manager - INFO - file_manager.py:391 - 执行远程命令: ls -la /home
2025-07-31 17:14:09 - ui.file_manager - INFO - file_manager.py:401 - ls命令输出长度: 627 字符
2025-07-31 17:14:09 - ui.file_manager - INFO - file_manager.py:402 - ls命令错误输出: ''
2025-07-31 17:14:09 - ui.file_manager - INFO - file_manager.py:442 - 解析到 9 个文件/目录项
2025-07-31 17:14:09 - ui.file_manager - INFO - file_manager.py:453 - 开始更新远程文件树，项目数: 9
2025-07-31 17:14:09 - ui.file_manager - INFO - file_manager.py:472 - 远程文件树更新完成
2025-07-31 17:14:12 - ui.file_manager - INFO - file_manager.py:384 - 开始刷新远程文件列表，路径: /home/<USER>
2025-07-31 17:14:12 - ui.file_manager - INFO - file_manager.py:391 - 执行远程命令: ls -la /home/<USER>
2025-07-31 17:14:12 - ui.file_manager - INFO - file_manager.py:401 - ls命令输出长度: 3755 字符
2025-07-31 17:14:12 - ui.file_manager - INFO - file_manager.py:402 - ls命令错误输出: ''
2025-07-31 17:14:12 - ui.file_manager - INFO - file_manager.py:442 - 解析到 55 个文件/目录项
2025-07-31 17:14:12 - ui.file_manager - INFO - file_manager.py:453 - 开始更新远程文件树，项目数: 55
2025-07-31 17:14:12 - ui.file_manager - INFO - file_manager.py:472 - 远程文件树更新完成
2025-07-31 17:15:45 - ui.file_manager - INFO - file_manager.py:384 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack
2025-07-31 17:15:45 - ui.file_manager - INFO - file_manager.py:391 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack
2025-07-31 17:15:45 - ui.file_manager - INFO - file_manager.py:401 - ls命令输出长度: 830 字符
2025-07-31 17:15:45 - ui.file_manager - INFO - file_manager.py:402 - ls命令错误输出: ''
2025-07-31 17:15:45 - ui.file_manager - INFO - file_manager.py:442 - 解析到 12 个文件/目录项
2025-07-31 17:15:45 - ui.file_manager - INFO - file_manager.py:453 - 开始更新远程文件树，项目数: 12
2025-07-31 17:15:45 - ui.file_manager - INFO - file_manager.py:472 - 远程文件树更新完成
2025-07-31 17:15:51 - ui.file_manager - INFO - file_manager.py:384 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/out_pkg
2025-07-31 17:15:51 - ui.file_manager - INFO - file_manager.py:391 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/out_pkg
2025-07-31 17:15:51 - ui.file_manager - INFO - file_manager.py:401 - ls命令输出长度: 490 字符
2025-07-31 17:15:51 - ui.file_manager - INFO - file_manager.py:402 - ls命令错误输出: ''
2025-07-31 17:15:51 - ui.file_manager - INFO - file_manager.py:442 - 解析到 6 个文件/目录项
2025-07-31 17:15:51 - ui.file_manager - INFO - file_manager.py:453 - 开始更新远程文件树，项目数: 6
2025-07-31 17:15:51 - ui.file_manager - INFO - file_manager.py:472 - 远程文件树更新完成
2025-07-31 17:16:01 - core.ssh_manager - INFO - ssh_manager.py:222 - 开始下载文件: /home/<USER>/yf_2113_repack/out_pkg/GD-V482.2113R.24.20250731_repack.tar.gz 到 GD-V482.2113R.24.20250731_repack.tar.gz
2025-07-31 17:16:20 - core.ssh_manager - INFO - ssh_manager.py:233 - 文件下载成功: /home/<USER>/yf_2113_repack/out_pkg/GD-V482.2113R.24.20250731_repack.tar.gz -> GD-V482.2113R.24.20250731_repack.tar.gz
2025-07-31 17:16:20 - ui.file_manager - INFO - file_manager.py:699 - 下载文件: 下载成功
2025-07-31 17:16:39 - ui.file_manager - INFO - file_manager.py:384 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/out_pkg
2025-07-31 17:16:39 - ui.file_manager - INFO - file_manager.py:391 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/out_pkg
2025-07-31 17:16:39 - ui.file_manager - INFO - file_manager.py:401 - ls命令输出长度: 483 字符
2025-07-31 17:16:39 - ui.file_manager - INFO - file_manager.py:402 - ls命令错误输出: ''
2025-07-31 17:16:39 - ui.file_manager - INFO - file_manager.py:442 - 解析到 6 个文件/目录项
2025-07-31 17:16:39 - ui.file_manager - INFO - file_manager.py:453 - 开始更新远程文件树，项目数: 6
2025-07-31 17:16:39 - ui.file_manager - INFO - file_manager.py:472 - 远程文件树更新完成
2025-07-31 17:16:42 - ui.file_manager - INFO - file_manager.py:384 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack
2025-07-31 17:16:42 - ui.file_manager - INFO - file_manager.py:391 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack
2025-07-31 17:16:42 - ui.file_manager - INFO - file_manager.py:401 - ls命令输出长度: 766 字符
2025-07-31 17:16:42 - ui.file_manager - INFO - file_manager.py:402 - ls命令错误输出: ''
2025-07-31 17:16:42 - ui.file_manager - INFO - file_manager.py:442 - 解析到 11 个文件/目录项
2025-07-31 17:16:42 - ui.file_manager - INFO - file_manager.py:453 - 开始更新远程文件树，项目数: 11
2025-07-31 17:16:42 - ui.file_manager - INFO - file_manager.py:472 - 远程文件树更新完成
2025-07-31 17:18:55 - ui.file_manager - INFO - file_manager.py:384 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack
2025-07-31 17:18:55 - ui.file_manager - INFO - file_manager.py:391 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack
2025-07-31 17:18:55 - ui.file_manager - INFO - file_manager.py:401 - ls命令输出长度: 830 字符
2025-07-31 17:18:55 - ui.file_manager - INFO - file_manager.py:402 - ls命令错误输出: ''
2025-07-31 17:18:55 - ui.file_manager - INFO - file_manager.py:442 - 解析到 12 个文件/目录项
2025-07-31 17:18:55 - ui.file_manager - INFO - file_manager.py:453 - 开始更新远程文件树，项目数: 12
2025-07-31 17:18:55 - ui.file_manager - INFO - file_manager.py:472 - 远程文件树更新完成
2025-07-31 17:18:57 - ui.file_manager - INFO - file_manager.py:384 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/unpacked
2025-07-31 17:18:57 - ui.file_manager - INFO - file_manager.py:391 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/unpacked
2025-07-31 17:18:57 - ui.file_manager - INFO - file_manager.py:401 - ls命令输出长度: 548 字符
2025-07-31 17:18:57 - ui.file_manager - INFO - file_manager.py:402 - ls命令错误输出: ''
2025-07-31 17:18:57 - ui.file_manager - INFO - file_manager.py:442 - 解析到 7 个文件/目录项
2025-07-31 17:18:57 - ui.file_manager - INFO - file_manager.py:453 - 开始更新远程文件树，项目数: 7
2025-07-31 17:18:57 - ui.file_manager - INFO - file_manager.py:472 - 远程文件树更新完成
2025-07-31 17:18:58 - ui.file_manager - INFO - file_manager.py:384 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted
2025-07-31 17:18:58 - ui.file_manager - INFO - file_manager.py:391 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted
2025-07-31 17:18:58 - ui.file_manager - INFO - file_manager.py:401 - ls命令输出长度: 2101 字符
2025-07-31 17:18:58 - ui.file_manager - INFO - file_manager.py:402 - ls命令错误输出: ''
2025-07-31 17:18:58 - ui.file_manager - INFO - file_manager.py:442 - 解析到 34 个文件/目录项
2025-07-31 17:18:58 - ui.file_manager - INFO - file_manager.py:453 - 开始更新远程文件树，项目数: 34
2025-07-31 17:18:58 - ui.file_manager - INFO - file_manager.py:472 - 远程文件树更新完成
2025-07-31 17:18:59 - ui.file_manager - INFO - file_manager.py:384 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application
2025-07-31 17:18:59 - ui.file_manager - INFO - file_manager.py:391 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application
2025-07-31 17:18:59 - ui.file_manager - INFO - file_manager.py:401 - ls命令输出长度: 169 字符
2025-07-31 17:18:59 - ui.file_manager - INFO - file_manager.py:402 - ls命令错误输出: ''
2025-07-31 17:18:59 - ui.file_manager - INFO - file_manager.py:442 - 解析到 2 个文件/目录项
2025-07-31 17:18:59 - ui.file_manager - INFO - file_manager.py:453 - 开始更新远程文件树，项目数: 2
2025-07-31 17:18:59 - ui.file_manager - INFO - file_manager.py:472 - 远程文件树更新完成
2025-07-31 17:19:00 - ui.file_manager - INFO - file_manager.py:384 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron
2025-07-31 17:19:00 - ui.file_manager - INFO - file_manager.py:391 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron
2025-07-31 17:19:00 - ui.file_manager - INFO - file_manager.py:401 - ls命令输出长度: 329 字符
2025-07-31 17:19:00 - ui.file_manager - INFO - file_manager.py:402 - ls命令错误输出: ''
2025-07-31 17:19:00 - ui.file_manager - INFO - file_manager.py:442 - 解析到 5 个文件/目录项
2025-07-31 17:19:00 - ui.file_manager - INFO - file_manager.py:453 - 开始更新远程文件树，项目数: 5
2025-07-31 17:19:00 - ui.file_manager - INFO - file_manager.py:472 - 远程文件树更新完成
2025-07-31 17:19:01 - ui.file_manager - INFO - file_manager.py:384 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron/res
2025-07-31 17:19:01 - ui.file_manager - INFO - file_manager.py:391 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron/res
2025-07-31 17:19:01 - ui.file_manager - INFO - file_manager.py:401 - ls命令输出长度: 584 字符
2025-07-31 17:19:01 - ui.file_manager - INFO - file_manager.py:402 - ls命令错误输出: ''
2025-07-31 17:19:01 - ui.file_manager - INFO - file_manager.py:442 - 解析到 9 个文件/目录项
2025-07-31 17:19:01 - ui.file_manager - INFO - file_manager.py:453 - 开始更新远程文件树，项目数: 9
2025-07-31 17:19:01 - ui.file_manager - INFO - file_manager.py:472 - 远程文件树更新完成
2025-07-31 17:19:02 - ui.file_manager - INFO - file_manager.py:384 - 开始刷新远程文件列表，路径: /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron/res/data
2025-07-31 17:19:02 - ui.file_manager - INFO - file_manager.py:391 - 执行远程命令: ls -la /home/<USER>/yf_2113_repack/unpacked/rootfs_extracted/application/neutron/res/data
2025-07-31 17:19:02 - ui.file_manager - INFO - file_manager.py:401 - ls命令输出长度: 288 字符
2025-07-31 17:19:02 - ui.file_manager - INFO - file_manager.py:402 - ls命令错误输出: ''
2025-07-31 17:19:02 - ui.file_manager - INFO - file_manager.py:442 - 解析到 4 个文件/目录项
2025-07-31 17:19:02 - ui.file_manager - INFO - file_manager.py:453 - 开始更新远程文件树，项目数: 4
2025-07-31 17:19:02 - ui.file_manager - INFO - file_manager.py:472 - 远程文件树更新完成
2025-07-31 18:05:02 - __main__ - INFO - main.py:60 - 应用程序正在关闭...
2025-07-31 18:05:02 - core.performance_monitor - INFO - performance_monitor.py:87 - 性能监控已停止
2025-07-31 18:05:02 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:81 - 开始停止SSH连接池，超时: 1.0秒
2025-07-31 18:05:02 - core.ssh_connection_pool - INFO - ssh_connection_pool.py:138 - SSH连接池已停止，耗时: 0.000秒
2025-07-31 18:05:02 - core.error_handler - INFO - error_handler.py:240 - 全局异常处理器已卸载
2025-07-31 18:05:02 - paramiko.transport.sftp - INFO - sftp.py:169 - [chan 0] sftp session closed.
2025-07-31 18:05:02 - core.ssh_manager - INFO - ssh_manager.py:61 - SFTP会话已关闭。
2025-07-31 18:05:02 - core.ssh_manager - INFO - ssh_manager.py:65 - SSH连接已关闭。
2025-07-31 18:05:02 - ui.main_window - INFO - main_window.py:99 - 主窗口收到连接状态变化: connected=False
2025-07-31 18:05:02 - ui.main_window - INFO - main_window.py:102 - 通知命令面板连接状态变化
2025-07-31 18:05:02 - ui.command_panel - INFO - command_panel.py:155 - 命令面板收到连接状态变化: connected=False
2025-07-31 18:05:02 - ui.command_panel - INFO - command_panel.py:164 - 快捷命令按钮状态已更新为: 禁用
2025-07-31 18:05:02 - ui.main_window - INFO - main_window.py:108 - 通知文件管理器连接状态变化
2025-07-31 18:05:02 - ui.ssh_panel - INFO - ssh_panel.py:280 - SSH连接已断开
2025-07-31 18:05:02 - core.config_manager - INFO - config_manager.py:94 - 配置文件保存成功: config.json
2025-07-31 18:05:02 - ui.main_window - INFO - main_window.py:282 - 资源清理完成

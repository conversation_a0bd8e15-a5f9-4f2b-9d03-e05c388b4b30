#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试命令优化 - 验证快捷命令的修改
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_command_panel_commands():
    """测试命令面板的快捷命令"""
    print("=== 命令面板快捷命令测试 ===")
    
    # 模拟命令面板的快捷命令
    commands = {
        "拆包": "./repack.sh unpack",
        "打包": "./repack.sh pack", 
        "清理": "rm -rf unpacked"
    }
    
    for name, command in commands.items():
        print(f"{name}命令: {command}")
    
    print()

def test_remote_path_config():
    """测试远程路径配置"""
    print("=== 远程路径配置测试 ===")
    
    from core.config_manager import config
    
    ssh_config = config.get_ssh_config()
    default_remote = ssh_config.get("default_remote_path", "/home/<USER>/yf_2113_repack")
    
    print(f"默认远程目录: {default_remote}")
    print()

def test_ls_command_parsing():
    """测试ls命令输出解析"""
    print("=== ls命令输出解析测试 ===")
    
    # 模拟ls -la输出
    sample_output = """total 12
drwxr-xr-x 3 <USER> <GROUP> 4096 Jul 31 15:30 .
drwxr-xr-x 5 <USER> <GROUP> 4096 Jul 31 15:30 ..
-rwxr-xr-x 1 <USER> <GROUP> 1234 Jul 31 15:30 repack.sh
drwxr-xr-x 2 <USER> <GROUP> 4096 Jul 31 15:30 unpacked"""
    
    lines = sample_output.strip().split('\n')
    items = []
    
    for line in lines[1:]:  # 跳过第一行（总计）
        if not line.strip():
            continue
        
        parts = line.split(None, 8)
        if len(parts) < 9:
            continue
        
        permissions = parts[0]
        size = parts[4]
        date_parts = parts[5:8]
        name = parts[8]
        
        # 跳过当前目录
        if name in ['.']:
            continue
        
        is_dir = permissions.startswith('d')
        
        items.append({
            "name": name,
            "is_dir": is_dir,
            "size": size if not is_dir else "",
            "modified": " ".join(date_parts),
            "permissions": permissions
        })
    
    print("解析结果:")
    for item in items:
        icon = "📁" if item["is_dir"] else "📄"
        print(f"{icon} {item['name']} - {item['permissions']} - {item['modified']}")
    
    print()

if __name__ == "__main__":
    print("开始命令优化测试...")
    print()
    
    test_command_panel_commands()
    test_remote_path_config()
    test_ls_command_parsing()
    
    print("命令优化测试完成！")
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异步资源清理器
支持并行资源清理，避免串行阻塞
"""

import time
import threading
import logging
from typing import Dict, List, Callable, Any, Optional
from dataclasses import dataclass
from enum import Enum
from concurrent.futures import ThreadPoolExecutor, as_completed, TimeoutError

logger = logging.getLogger(__name__)

class CleanupStatus(Enum):
    """清理状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"

@dataclass
class CleanupTaskInfo:
    """清理任务信息"""
    name: str
    task: Callable
    timeout: float
    priority: int
    status: CleanupStatus = CleanupStatus.PENDING
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    error: Optional[Exception] = None
    result: Any = None

@dataclass
class CleanupResult:
    """清理结果"""
    total_tasks: int
    successful_tasks: int
    failed_tasks: int
    timeout_tasks: int
    total_time: float
    task_results: Dict[str, CleanupTaskInfo]

class AsyncResourceCleaner:
    """异步资源清理器
    
    支持并行执行多个资源清理任务，提供超时控制和错误处理
    """
    
    def __init__(self, max_workers: int = 4):
        """初始化异步资源清理器
        
        Args:
            max_workers: 最大工作线程数
        """
        self.max_workers = max_workers
        self.cleanup_tasks: List[CleanupTaskInfo] = []
        self._lock = threading.Lock()
        self._executor: Optional[ThreadPoolExecutor] = None
        
        logger.info(f"异步资源清理器初始化完成，最大工作线程数: {max_workers}")
    
    def add_cleanup_task(self, 
                        name: str, 
                        task: Callable, 
                        timeout: float = 1.0, 
                        priority: int = 0):
        """添加清理任务
        
        Args:
            name: 任务名称
            task: 清理任务函数
            timeout: 任务超时时间（秒）
            priority: 优先级，数字越小优先级越高
        """
        with self._lock:
            task_info = CleanupTaskInfo(
                name=name,
                task=task,
                timeout=timeout,
                priority=priority
            )
            self.cleanup_tasks.append(task_info)
            
            # 按优先级排序
            self.cleanup_tasks.sort(key=lambda x: x.priority)
            
        logger.info(f"添加清理任务: {name} (超时: {timeout}s, 优先级: {priority})")
    
    def execute_all_cleanups(self, max_wait_time: float = 2.0) -> CleanupResult:
        """并行执行所有清理任务
        
        Args:
            max_wait_time: 最大等待时间（秒）
            
        Returns:
            CleanupResult: 清理结果
        """
        start_time = time.time()
        
        with self._lock:
            tasks_to_execute = self.cleanup_tasks.copy()
        
        if not tasks_to_execute:
            logger.info("没有清理任务需要执行")
            return CleanupResult(
                total_tasks=0,
                successful_tasks=0,
                failed_tasks=0,
                timeout_tasks=0,
                total_time=0,
                task_results={}
            )
        
        logger.info(f"开始执行 {len(tasks_to_execute)} 个清理任务，最大等待时间: {max_wait_time}秒")
        
        # 创建线程池
        max_workers = min(self.max_workers, len(tasks_to_execute))
        self._executor = ThreadPoolExecutor(max_workers=max_workers)
        
        try:
            # 提交所有任务
            future_to_task = {}
            for task_info in tasks_to_execute:
                future = self._executor.submit(self._execute_cleanup_task, task_info)
                future_to_task[future] = task_info
            
            # 等待所有任务完成
            completed_tasks = 0
            for future in as_completed(future_to_task, timeout=max_wait_time):
                task_info = future_to_task[future]
                try:
                    future.result()  # 获取结果，如果有异常会抛出
                    completed_tasks += 1
                except Exception as e:
                    logger.error(f"清理任务 {task_info.name} 执行异常: {e}")
                    task_info.status = CleanupStatus.FAILED
                    task_info.error = e
                    task_info.end_time = time.time()
            
        except TimeoutError:
            logger.warning(f"清理任务执行超时，已完成 {completed_tasks}/{len(tasks_to_execute)} 个任务")
            
            # 标记未完成的任务为超时
            for task_info in tasks_to_execute:
                if task_info.status == CleanupStatus.RUNNING:
                    task_info.status = CleanupStatus.TIMEOUT
                    task_info.end_time = time.time()
                    
        except Exception as e:
            logger.error(f"清理任务执行异常: {e}")
            
        finally:
            # 关闭线程池
            if self._executor:
                self._executor.shutdown(wait=False)
                self._executor = None
        
        # 统计结果
        total_time = time.time() - start_time
        result = self._generate_cleanup_result(tasks_to_execute, total_time)
        
        logger.info(f"清理任务执行完成，成功: {result.successful_tasks}, "
                   f"失败: {result.failed_tasks}, 超时: {result.timeout_tasks}, "
                   f"总耗时: {total_time:.3f}秒")
        
        return result
    
    def _execute_cleanup_task(self, task_info: CleanupTaskInfo):
        """执行单个清理任务
        
        Args:
            task_info: 清理任务信息
        """
        task_info.status = CleanupStatus.RUNNING
        task_info.start_time = time.time()
        
        try:
            logger.debug(f"开始执行清理任务: {task_info.name}")
            
            # 使用线程执行任务以支持超时控制
            result_container = [None]
            exception_container = [None]
            
            def task_wrapper():
                try:
                    result_container[0] = task_info.task()
                except Exception as e:
                    exception_container[0] = e
            
            task_thread = threading.Thread(target=task_wrapper, daemon=True)
            task_thread.start()
            task_thread.join(timeout=task_info.timeout)
            
            if task_thread.is_alive():
                # 任务超时
                logger.warning(f"清理任务 {task_info.name} 超时")
                task_info.status = CleanupStatus.TIMEOUT
            elif exception_container[0]:
                # 任务异常
                raise exception_container[0]
            else:
                # 任务成功
                task_info.status = CleanupStatus.COMPLETED
                task_info.result = result_container[0]
                logger.debug(f"清理任务 {task_info.name} 完成")
                
        except Exception as e:
            logger.error(f"清理任务 {task_info.name} 失败: {e}")
            task_info.status = CleanupStatus.FAILED
            task_info.error = e
            
        finally:
            task_info.end_time = time.time()
    
    def _generate_cleanup_result(self, 
                               tasks: List[CleanupTaskInfo], 
                               total_time: float) -> CleanupResult:
        """生成清理结果
        
        Args:
            tasks: 任务列表
            total_time: 总耗时
            
        Returns:
            CleanupResult: 清理结果
        """
        successful_tasks = sum(1 for t in tasks if t.status == CleanupStatus.COMPLETED)
        failed_tasks = sum(1 for t in tasks if t.status == CleanupStatus.FAILED)
        timeout_tasks = sum(1 for t in tasks if t.status == CleanupStatus.TIMEOUT)
        
        task_results = {task.name: task for task in tasks}
        
        return CleanupResult(
            total_tasks=len(tasks),
            successful_tasks=successful_tasks,
            failed_tasks=failed_tasks,
            timeout_tasks=timeout_tasks,
            total_time=total_time,
            task_results=task_results
        )
    
    def execute_single_cleanup(self, 
                             name: str, 
                             task: Callable, 
                             timeout: float = 1.0) -> CleanupTaskInfo:
        """执行单个清理任务
        
        Args:
            name: 任务名称
            task: 清理任务函数
            timeout: 超时时间
            
        Returns:
            CleanupTaskInfo: 任务执行结果
        """
        task_info = CleanupTaskInfo(
            name=name,
            task=task,
            timeout=timeout,
            priority=0
        )
        
        self._execute_cleanup_task(task_info)
        return task_info
    
    def clear_tasks(self):
        """清空所有任务"""
        with self._lock:
            self.cleanup_tasks.clear()
        logger.info("清理任务列表已清空")
    
    def get_task_count(self) -> int:
        """获取任务数量
        
        Returns:
            int: 任务数量
        """
        with self._lock:
            return len(self.cleanup_tasks)
    
    def get_tasks_by_status(self, status: CleanupStatus) -> List[CleanupTaskInfo]:
        """根据状态获取任务
        
        Args:
            status: 任务状态
            
        Returns:
            List[CleanupTaskInfo]: 指定状态的任务列表
        """
        with self._lock:
            return [task for task in self.cleanup_tasks if task.status == status]
    
    def cancel_pending_tasks(self):
        """取消所有待执行的任务"""
        with self._lock:
            cancelled_count = 0
            for task in self.cleanup_tasks:
                if task.status == CleanupStatus.PENDING:
                    task.status = CleanupStatus.FAILED
                    task.error = Exception("Task cancelled")
                    cancelled_count += 1
                    
        if cancelled_count > 0:
            logger.info(f"取消了 {cancelled_count} 个待执行的任务")
    
    def force_shutdown(self):
        """强制关闭清理器"""
        logger.warning("强制关闭异步资源清理器")
        
        # 取消待执行的任务
        self.cancel_pending_tasks()
        
        # 强制关闭线程池
        if self._executor:
            self._executor.shutdown(wait=False)
            self._executor = None

# 全局异步资源清理器实例
async_resource_cleaner = AsyncResourceCleaner()
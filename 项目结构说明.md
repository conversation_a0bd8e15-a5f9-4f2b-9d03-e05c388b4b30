# 项目结构说明

## 📁 目录结构

```
yf_repack_tool/
├── main.py                   # 🚀 主程序入口文件
├── requirements.txt          # 📦 Python依赖包列表
├── config.json              # ⚙️ 应用配置文件（自动生成）
├── start.bat                # 🖱️ Windows启动脚本
├── README.md                # 📖 项目说明文档
├── VERSION.md               # 📋 版本历史记录
├── CHANGELOG.md             # 📝 更新日志
├── core/                    # 🏗️ 核心功能模块
│   ├── __init__.py          # 模块初始化文件
│   ├── ssh_manager.py       # SSH连接管理器
│   ├── config_manager.py    # 配置管理器
│   ├── logger_config.py     # 日志系统配置
│   ├── performance_monitor.py # 性能监控器
│   ├── security_manager.py  # 安全管理器
│   ├── error_handler.py     # 错误处理器
│   ├── ssh_connection_pool.py # SSH连接池
│   └── async_file_transfer.py # 异步文件传输
├── ui/                      # 🎨 用户界面模块
│   ├── __init__.py          # 模块初始化文件
│   ├── main_window.py       # 主窗口界面
│   ├── ssh_panel.py         # SSH连接面板
│   ├── command_panel.py     # 命令执行面板
│   ├── file_manager.py      # 文件管理面板
│   ├── status_bar.py        # 状态栏组件
│   └── theme_manager.py     # 主题管理器
└── logs/                    # 📄 日志文件目录（自动生成）
    ├── yf_repack_tool.log   # 主日志文件
    └── errors.log           # 错误日志文件
```

## 🎯 设计原则

### 1. 单一入口
- **main.py**: 作为唯一的主程序入口文件
- 简化项目结构，用户只需运行一个文件

### 2. 模块化分层
- **core/**: 核心业务逻辑，不依赖UI
- **ui/**: 用户界面组件，依赖core模块
- 清晰的依赖关系，便于维护和测试

### 3. 功能分离
- 每个模块负责特定功能
- 高内聚，低耦合
- 便于独立开发和测试

## 🚀 启动方式

### 方式一：直接运行
```bash
python main.py
```

### 方式二：使用启动脚本
```bash
start.bat
```

## 📋 模块说明

### Core模块
- **ssh_manager.py**: SSH连接的底层实现
- **config_manager.py**: 统一的配置文件管理
- **logger_config.py**: 日志系统的配置和初始化
- **performance_monitor.py**: 系统性能监控
- **security_manager.py**: 密码加密和安全存储
- **error_handler.py**: 全局异常处理和恢复
- **ssh_connection_pool.py**: SSH连接池管理
- **async_file_transfer.py**: 异步文件传输框架

### UI模块
- **main_window.py**: 主窗口布局和组件协调
- **ssh_panel.py**: SSH连接配置界面
- **command_panel.py**: 远程命令执行界面
- **file_manager.py**: 文件上传下载管理界面
- **status_bar.py**: 底部状态栏显示
- **theme_manager.py**: 界面主题管理

## 🔧 配置文件

### config.json
应用程序的主配置文件，包含：
- SSH连接默认设置
- 界面主题和布局配置
- 日志级别和文件设置
- 性能监控阈值配置

### requirements.txt
Python依赖包列表：
- paramiko: SSH连接库
- psutil: 系统性能监控
- cryptography: 加密功能
- keyring: 系统密钥环访问

## 📊 优势特点

1. **结构清晰**: 功能模块分离，易于理解和维护
2. **单一入口**: 只需运行main.py，简化使用流程
3. **模块化**: 核心功能与界面分离，便于测试和扩展
4. **配置化**: 通过配置文件管理所有设置
5. **日志完善**: 分级日志记录，便于问题排查
6. **性能优化**: 连接池、监控等提升系统性能

---

**注意**: 此结构设计遵循Python项目最佳实践，便于后续维护和功能扩展。
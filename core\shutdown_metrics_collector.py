#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关闭状态监控和指标收集器
记录和分析程序关闭性能
"""

import time
import json
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class ShutdownRecord:
    """关闭记录"""
    timestamp: str
    total_time: float
    config_save_time: float
    resource_cleanup_time: float
    thread_stop_time: float
    forced_shutdown: bool
    failed_operations: List[str]
    completed_tasks: List[str]
    shutdown_trigger: str  # 'user_close', 'force_close', 'exception'
    system_info: Dict[str, Any]

class ShutdownMetricsCollector:
    """关闭指标收集器
    
    收集、存储和分析程序关闭性能指标
    """
    
    def __init__(self, max_records: int = 100):
        """初始化指标收集器
        
        Args:
            max_records: 最大记录数量
        """
        self.max_records = max_records
        self.records: List[ShutdownRecord] = []
        self.metrics_file = Path("logs/shutdown_metrics.json")
        
        # 确保日志目录存在
        self.metrics_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 加载历史记录
        self._load_records()
        
        logger.info(f"关闭指标收集器初始化完成，最大记录数: {max_records}")
    
    def record_shutdown(self, 
                       metrics,  # ShutdownMetrics from fast_shutdown_manager
                       trigger: str = "user_close",
                       completed_tasks: List[str] = None,
                       system_info: Dict[str, Any] = None):
        """记录关闭事件
        
        Args:
            metrics: 关闭指标
            trigger: 关闭触发方式
            completed_tasks: 完成的任务列表
            system_info: 系统信息
        """
        try:
            record = ShutdownRecord(
                timestamp=datetime.now().isoformat(),
                total_time=metrics.total_shutdown_time,
                config_save_time=metrics.config_save_time,
                resource_cleanup_time=metrics.resource_cleanup_time,
                thread_stop_time=metrics.thread_stop_time,
                forced_shutdown=metrics.forced_shutdown,
                failed_operations=metrics.failed_operations.copy(),
                completed_tasks=completed_tasks or [],
                shutdown_trigger=trigger,
                system_info=system_info or self._get_system_info()
            )
            
            self.records.append(record)
            
            # 限制记录数量
            if len(self.records) > self.max_records:
                self.records = self.records[-self.max_records:]
            
            # 保存到文件
            self._save_records()
            
            # 分析性能
            self._analyze_performance(record)
            
            logger.info(f"记录关闭事件: 耗时 {record.total_time:.3f}秒, "
                       f"触发方式: {trigger}, 强制关闭: {record.forced_shutdown}")
            
        except Exception as e:
            logger.error(f"记录关闭事件失败: {e}")
    
    def _get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        try:
            import psutil
            return {
                "cpu_count": psutil.cpu_count(),
                "memory_total_mb": psutil.virtual_memory().total / 1024 / 1024,
                "platform": "windows"  # 根据实际平台调整
            }
        except Exception as e:
            logger.warning(f"获取系统信息失败: {e}")
            return {}
    
    def _analyze_performance(self, record: ShutdownRecord):
        """分析关闭性能"""
        # 检查是否超过目标时间
        if record.total_time > 0.5:
            logger.warning(f"关闭时间超过500ms目标: {record.total_time:.3f}秒")
        
        # 检查是否有失败的操作
        if record.failed_operations:
            logger.warning(f"关闭过程中有失败的操作: {record.failed_operations}")
        
        # 检查是否强制关闭
        if record.forced_shutdown:
            logger.warning("程序被强制关闭")
        
        # 分析最慢的阶段
        phases = {
            "配置保存": record.config_save_time,
            "资源清理": record.resource_cleanup_time,
            "线程停止": record.thread_stop_time
        }
        
        slowest_phase = max(phases.items(), key=lambda x: x[1])
        if slowest_phase[1] > 0.2:  # 超过200ms
            logger.info(f"最慢的关闭阶段: {slowest_phase[0]} ({slowest_phase[1]:.3f}秒)")
    
    def _load_records(self):
        """加载历史记录"""
        try:
            if self.metrics_file.exists():
                with open(self.metrics_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.records = [
                        ShutdownRecord(**record) for record in data.get('records', [])
                    ]
                logger.info(f"加载了 {len(self.records)} 条历史关闭记录")
        except Exception as e:
            logger.warning(f"加载历史记录失败: {e}")
            self.records = []
    
    def _save_records(self):
        """保存记录到文件"""
        try:
            data = {
                "version": "1.0",
                "last_updated": datetime.now().isoformat(),
                "records": [asdict(record) for record in self.records]
            }
            
            with open(self.metrics_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"保存记录失败: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        if not self.records:
            return {"total_records": 0}
        
        # 基本统计
        total_records = len(self.records)
        forced_shutdowns = sum(1 for r in self.records if r.forced_shutdown)
        
        # 时间统计
        times = [r.total_time for r in self.records]
        avg_time = sum(times) / len(times)
        max_time = max(times)
        min_time = min(times)
        
        # 成功率统计
        successful_shutdowns = sum(1 for r in self.records 
                                 if not r.forced_shutdown and not r.failed_operations)
        success_rate = successful_shutdowns / total_records * 100
        
        # 触发方式统计
        trigger_counts = {}
        for record in self.records:
            trigger = record.shutdown_trigger
            trigger_counts[trigger] = trigger_counts.get(trigger, 0) + 1
        
        # 最近记录
        recent_records = self.records[-10:] if len(self.records) >= 10 else self.records
        recent_avg_time = sum(r.total_time for r in recent_records) / len(recent_records)
        
        return {
            "total_records": total_records,
            "forced_shutdowns": forced_shutdowns,
            "success_rate": round(success_rate, 2),
            "average_time": round(avg_time, 3),
            "max_time": round(max_time, 3),
            "min_time": round(min_time, 3),
            "recent_average_time": round(recent_avg_time, 3),
            "trigger_distribution": trigger_counts,
            "target_compliance": sum(1 for t in times if t <= 0.5) / len(times) * 100
        }
    
    def get_recent_records(self, count: int = 10) -> List[ShutdownRecord]:
        """获取最近的记录
        
        Args:
            count: 记录数量
            
        Returns:
            List[ShutdownRecord]: 最近的记录
        """
        return self.records[-count:] if len(self.records) >= count else self.records
    
    def get_performance_trends(self) -> Dict[str, List[float]]:
        """获取性能趋势
        
        Returns:
            Dict[str, List[float]]: 性能趋势数据
        """
        if len(self.records) < 2:
            return {}
        
        # 取最近50条记录分析趋势
        recent_records = self.records[-50:] if len(self.records) >= 50 else self.records
        
        return {
            "total_times": [r.total_time for r in recent_records],
            "config_save_times": [r.config_save_time for r in recent_records],
            "resource_cleanup_times": [r.resource_cleanup_time for r in recent_records],
            "thread_stop_times": [r.thread_stop_time for r in recent_records],
            "timestamps": [r.timestamp for r in recent_records]
        }
    
    def export_report(self, output_file: str = None) -> str:
        """导出性能报告
        
        Args:
            output_file: 输出文件路径
            
        Returns:
            str: 报告内容
        """
        stats = self.get_statistics()
        trends = self.get_performance_trends()
        
        report_lines = [
            "# 程序关闭性能报告",
            f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "## 总体统计",
            f"- 总记录数: {stats['total_records']}",
            f"- 成功率: {stats['success_rate']}%",
            f"- 强制关闭次数: {stats['forced_shutdowns']}",
            f"- 平均关闭时间: {stats['average_time']}秒",
            f"- 最快关闭时间: {stats['min_time']}秒",
            f"- 最慢关闭时间: {stats['max_time']}秒",
            f"- 500ms目标达成率: {stats['target_compliance']:.1f}%",
            "",
            "## 触发方式分布",
        ]
        
        for trigger, count in stats['trigger_distribution'].items():
            percentage = count / stats['total_records'] * 100
            report_lines.append(f"- {trigger}: {count}次 ({percentage:.1f}%)")
        
        if trends and len(trends['total_times']) > 1:
            report_lines.extend([
                "",
                "## 性能趋势",
                f"- 最近平均时间: {stats['recent_average_time']}秒",
                f"- 趋势记录数: {len(trends['total_times'])}",
            ])
        
        report_content = "\n".join(report_lines)
        
        if output_file:
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(report_content)
                logger.info(f"性能报告已导出到: {output_file}")
            except Exception as e:
                logger.error(f"导出报告失败: {e}")
        
        return report_content
    
    def clear_records(self):
        """清空所有记录"""
        self.records.clear()
        self._save_records()
        logger.info("关闭记录已清空")

# 全局关闭指标收集器实例
shutdown_metrics_collector = ShutdownMetricsCollector()
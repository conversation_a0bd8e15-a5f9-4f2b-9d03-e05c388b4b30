#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内存使用优化器
实现历史数据自动清理和内存使用监控
"""

import gc
import time
import threading
import logging
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from collections import deque
import psutil

logger = logging.getLogger(__name__)

@dataclass
class MemoryThresholds:
    """内存阈值配置"""
    idle_limit_mb: float = 50.0       # 空闲时内存限制
    active_limit_mb: float = 150.0    # 活跃时内存限制
    warning_mb: float = 120.0         # 警告阈值
    critical_mb: float = 180.0        # 严重阈值
    cleanup_trigger_mb: float = 100.0 # 触发清理阈值
    max_history_size: int = 500       # 最大历史记录数
    log_cleanup_size_mb: float = 10.0 # 日志清理阈值

class MemoryOptimizer:
    """内存使用优化器
    
    监控内存使用，自动清理历史数据，优化内存占用
    """
    
    def __init__(self, thresholds: MemoryThresholds = None):
        """初始化内存优化器
        
        Args:
            thresholds: 内存阈值配置
        """
        self.thresholds = thresholds or MemoryThresholds()
        self.running = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.cleanup_callbacks: List[Callable] = []
        self._stop_event = threading.Event()
        self._lock = threading.Lock()
        
        # 内存使用历史 - 优化存储结构
        self.memory_history = deque(maxlen=self.thresholds.max_history_size)
        
        # 应用状态跟踪
        self.app_state = "idle"  # idle, active, transferring
        self.last_activity_time = time.time()
        
        # 清理统计
        self.cleanup_stats = {
            "total_cleanups": 0,
            "memory_freed_mb": 0.0,
            "last_cleanup_time": 0,
            "auto_cleanups": 0,
            "manual_cleanups": 0
        }
        
        # 数据管理器
        self.data_managers = {}
        
        logger.info("内存优化器初始化完成")
    
    def start_monitoring(self, interval: float = 10.0):
        """开始内存监控
        
        Args:
            interval: 监控间隔（秒）
        """
        if self.running:
            return
        
        self.running = True
        self._stop_event.clear()
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            name="MemoryOptimizer",
            daemon=True
        )
        self.monitor_thread.start()
        
        logger.info(f"内存监控已启动，间隔: {interval}秒")
    
    def stop_monitoring(self, timeout: float = 2.0):
        """停止内存监控
        
        Args:
            timeout: 停止超时时间
        """
        self.running = False
        self._stop_event.set()
        
        if self.monitor_thread:
            self.monitor_thread.join(timeout=timeout)
            if self.monitor_thread.is_alive():
                logger.warning(f"内存监控线程未在 {timeout}秒内停止")
            else:
                logger.info("内存监控已停止")
    
    def _monitor_loop(self, interval: float):
        """内存监控循环 - 支持快速停止和实时控制"""
        quick_check_interval = 0.5  # 500ms快速检查间隔
        
        while self.running:
            try:
                current_memory = self._get_current_memory_usage()
                
                # 更新应用状态
                self._update_app_state()
                
                # 优化的历史数据存储
                with self._lock:
                    self.memory_history.append({
                        "timestamp": time.time(),
                        "memory_mb": current_memory,
                        "app_state": self.app_state
                    })
                
                # 实时内存控制 - 根据应用状态检查不同阈值
                self._check_memory_limits(current_memory)
                
                # 自动清理检查
                if self._should_trigger_cleanup(current_memory):
                    logger.info(f"触发自动内存清理: {current_memory:.1f}MB")
                    self._trigger_cleanup(auto=True)
                
                # 使用事件等待支持快速停止
                remaining_time = interval
                while remaining_time > 0 and self.running:
                    wait_time = min(quick_check_interval, remaining_time)
                    if self._stop_event.wait(timeout=wait_time):
                        break
                    remaining_time -= wait_time
                
            except Exception as e:
                logger.error(f"内存监控异常: {e}")
                if self._stop_event.wait(timeout=1.0):
                    break
    
    def _get_current_memory_usage(self) -> float:
        """获取当前内存使用量（MB）
        
        Returns:
            float: 内存使用量（MB）
        """
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            return memory_info.rss / 1024 / 1024
        except Exception as e:
            logger.error(f"获取内存使用量失败: {e}")
            return 0.0
    
    def _update_app_state(self):
        """更新应用状态"""
        try:
            # 检查是否有文件传输活动
            if self._is_file_transfer_active():
                self.app_state = "transferring"
                self.last_activity_time = time.time()
            # 检查是否有其他活动
            elif self._is_app_active():
                self.app_state = "active"
                self.last_activity_time = time.time()
            # 检查是否空闲超过30秒
            elif time.time() - self.last_activity_time > 30:
                self.app_state = "idle"
        except Exception as e:
            logger.error(f"更新应用状态失败: {e}")
    
    def _is_file_transfer_active(self) -> bool:
        """检查是否有文件传输活动"""
        try:
            # 检查是否有活跃的文件传输线程
            for thread in threading.enumerate():
                if "FileTransfer" in thread.name or "Upload" in thread.name or "Download" in thread.name:
                    return True
            return False
        except Exception:
            return False
    
    def _is_app_active(self) -> bool:
        """检查应用是否活跃"""
        try:
            # 检查CPU使用率来判断活跃状态
            process = psutil.Process()
            cpu_percent = process.cpu_percent(interval=0.1)
            return cpu_percent > 5.0  # CPU使用率超过5%认为是活跃状态
        except Exception:
            return False
    
    def _check_memory_limits(self, current_memory: float):
        """检查内存限制 - 根据应用状态使用不同阈值"""
        if self.app_state == "idle":
            limit = self.thresholds.idle_limit_mb
            if current_memory > limit:
                logger.warning(f"空闲状态内存超限: {current_memory:.1f}MB > {limit}MB")
                self._trigger_cleanup(auto=True, aggressive=True)
        
        elif self.app_state == "transferring":
            limit = self.thresholds.active_limit_mb
            if current_memory > limit:
                logger.warning(f"传输状态内存超限: {current_memory:.1f}MB > {limit}MB")
                self._trigger_cleanup(auto=True)
        
        elif self.app_state == "active":
            limit = self.thresholds.active_limit_mb * 0.8  # 活跃状态使用80%的传输限制
            if current_memory > limit:
                logger.warning(f"活跃状态内存超限: {current_memory:.1f}MB > {limit}MB")
                self._trigger_cleanup(auto=True)
        
        # 检查严重阈值
        if current_memory > self.thresholds.critical_mb:
            logger.critical(f"内存使用严重超限: {current_memory:.1f}MB")
            self._emergency_cleanup()
    
    def _should_trigger_cleanup(self, current_memory: float) -> bool:
        """判断是否应该触发清理"""
        # 基于当前内存使用量和应用状态决定
        if current_memory > self.thresholds.cleanup_trigger_mb:
            return True
        
        # 检查内存增长趋势
        if len(self.memory_history) >= 5:
            recent_memories = [item["memory_mb"] for item in list(self.memory_history)[-5:]]
            if len(recent_memories) >= 2:
                trend = recent_memories[-1] - recent_memories[0]
                if trend > 10:  # 内存增长超过10MB
                    logger.info(f"检测到内存增长趋势: +{trend:.1f}MB")
                    return True
        
        return False
    
    def _trigger_cleanup(self, auto: bool = False, aggressive: bool = False):
        """触发内存清理
        
        Args:
            auto: 是否为自动清理
            aggressive: 是否使用激进清理策略
        """
        cleanup_type = "自动" if auto else "手动"
        logger.info(f"开始{cleanup_type}内存清理 (激进模式: {aggressive})")
        start_memory = self._get_current_memory_usage()
        start_time = time.time()
        
        try:
            # 执行注册的清理回调
            for callback in self.cleanup_callbacks:
                try:
                    callback()
                except Exception as e:
                    logger.error(f"清理回调异常: {e}")
            
            # 执行内置清理
            self._cleanup_internal_data(aggressive=aggressive)
            
            # 清理性能监控数据
            self._cleanup_performance_data(aggressive=aggressive)
            
            # 清理日志数据
            self._cleanup_log_data()
            
            # 清理应用特定数据
            self._cleanup_application_data(aggressive=aggressive)
            
            # 强制垃圾回收
            self._force_garbage_collection()
            
            # 统计清理效果
            end_memory = self._get_current_memory_usage()
            cleanup_time = time.time() - start_time
            freed_memory = start_memory - end_memory
            
            # 更新统计信息
            with self._lock:
                self.cleanup_stats["total_cleanups"] += 1
                self.cleanup_stats["memory_freed_mb"] += max(0, freed_memory)
                self.cleanup_stats["last_cleanup_time"] = time.time()
                if auto:
                    self.cleanup_stats["auto_cleanups"] += 1
                else:
                    self.cleanup_stats["manual_cleanups"] += 1
            
            logger.info(f"{cleanup_type}内存清理完成，释放: {freed_memory:.1f}MB，耗时: {cleanup_time:.2f}秒")
            
       
    
    def _emergency_cleanup(self):
        """紧急内存清理"""
        logger.warning("执行紧急内存清理")
        
        # 更激进的清理策略
        self._cleanup_internal_data(aggressive=True)
        
        # 多次垃圾回收
        for _ in range(3):
            self._force_garbage_collection()
            time.sleep(0.1)
        
        # 清理性能监控历史数据
        self._cleanup_performance_data()
    
    def _cleanup_internal_data(self, aggressive: bool = False):
        """清理内部数据
        
        Args:
            aggressive: 是否使用激进清理策略
        """
        # 清理内存使用历史
        if aggressive:
            self.memory_history.clear()
        else:
            # 保留最近的数据
            while len(self.memory_history) > 50:
                self.memory_history.popleft()
        
        logger.debug("内部数据清理完成")
    
    def _cleanup_performance_data(self):
        """清理性能监控数据"""
        try:
            from core.performance_monitor import performance_monitor
            
            # 清理性能监控历史数据
            with performance_monitor._lock:
                if len(performance_monitor.metrics_history) > 100:
                    # 保留最近100条记录
                    recent_metrics = list(performance_monitor.metrics_history)[-100:]
                    performance_monitor.metrics_history.clear()
                    performance_monitor.metrics_history.extend(recent_metrics)
            
            logger.debug("性能监控数据清理完成")
            
        except Exception as e:
            logger.error(f"清理性能监控数据失败: {e}")
    
    def _force_garbage_collection(self):
        """强制垃圾回收"""
        try:
            # 执行所有代的垃圾回收
            collected = gc.collect()
            logger.debug(f"垃圾回收完成，回收对象数: {collected}")
        except Exception as e:
            logger.error(f"垃圾回收异常: {e}")
    
    def register_cleanup_callback(self, callback: Callable):
        """注册清理回调函数
        
        Args:
            callback: 清理回调函数
        """
        self.cleanup_callbacks.append(callback)
        logger.info(f"注册内存清理回调: {callback.__name__}")
    
    def get_memory_statistics(self) -> Dict[str, Any]:
        """获取内存统计信息
        
        Returns:
            Dict[str, Any]: 内存统计信息
        """
        current_memory = self._get_current_memory_usage()
        
        # 计算内存趋势
        if len(self.memory_history) >= 2:
            recent_avg = sum(item["memory_mb"] for item in list(self.memory_history)[-10:]) / min(10, len(self.memory_history))
            older_avg = sum(item["memory_mb"] for item in list(self.memory_history)[-20:-10]) / min(10, len(self.memory_history) - 10) if len(self.memory_history) > 10 else recent_avg
            trend = recent_avg - older_avg
        else:
            trend = 0.0
        
        return {
            "current_memory_mb": current_memory,
            "memory_trend_mb": trend,
            "warning_threshold_mb": self.thresholds.warning_mb,
            "critical_threshold_mb": self.thresholds.critical_mb,
            "cleanup_trigger_mb": self.thresholds.cleanup_trigger_mb,
            "cleanup_stats": self.cleanup_stats.copy(),
            "history_size": len(self.memory_history),
            "monitoring_active": self.running
        }
    
    def get_memory_usage_trend(self) -> List[Dict[str, Any]]:
        """获取内存使用趋势
        
        Returns:
            List[Dict[str, Any]]: 内存使用历史数据
        """
        return list(self.memory_history)
    
    def manual_cleanup(self) -> Dict[str, Any]:
        """手动触发内存清理
        
        Returns:
            Dict[str, Any]: 清理结果
        """
        start_memory = self._get_current_memory_usage()
        start_time = time.time()
        
        self._trigger_cleanup()
        
        end_memory = self._get_current_memory_usage()
        cleanup_time = time.time() - start_time
        freed_memory = start_memory - end_memory
        
        return {
            "start_memory_mb": start_memory,
            "end_memory_mb": end_memory,
            "freed_memory_mb": max(0, freed_memory),
            "cleanup_time_seconds": cleanup_time,
            "success": True
        }
    
    def set_thresholds(self, **kwargs):
        """设置内存阈值
        
        Args:
            **kwargs: 阈值参数
        """
        for key, value in kwargs.items():
            if hasattr(self.thresholds, key):
                setattr(self.thresholds, key, value)
                logger.info(f"更新内存阈值: {key} = {value}")

# 全局内存优化器实例
memory_optimizer = MemoryOptimizer()

# 注册默认的清理回调
def cleanup_log_data():
    """清理日志数据"""
    try:
        # 这里可以添加日志清理逻辑
        logger.debug("日志数据清理完成")
    except Exception as e:
        logger.error(f"清理日志数据失败: {e}")

def cleanup_cache_data():
    """清理缓存数据"""
    try:
        # 这里可以添加缓存清理逻辑
        logger.debug("缓存数据清理完成")
    except Exception as e:
        logger.error(f"清理缓存数据失败: {e}")

# 注册默认清理回调
memory_optimizer.register_cleanup_callback(cleanup_log_data)
memory_optimizer.register_cleanup_callback(cleanup_cache_data)
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
远程目录显示问题调试脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def debug_file_manager_logic():
    """调试文件管理器逻辑"""
    print("=== 文件管理器远程目录显示问题调试 ===")
    
    from core.config_manager import config
    
    # 1. 检查配置
    ssh_config = config.get_ssh_config()
    default_remote = ssh_config.get("default_remote_path", "/home/<USER>/yf_2113_repack")
    print(f"1. 配置中的默认远程目录: {default_remote}")
    
    # 2. 模拟连接状态变化
    print("2. 模拟连接状态变化:")
    print("   - 连接前: remote_path = '/'")
    print("   - 连接后: 应该设置为配置中的默认目录")
    print(f"   - 期望结果: remote_path = '{default_remote}'")
    
    # 3. 检查ls命令构造
    remote_path = default_remote
    command = f"ls -la {remote_path}"
    print(f"3. 构造的ls命令: {command}")
    
    # 4. 模拟可能的错误情况
    print("4. 可能的错误情况:")
    print("   - 目录不存在: 'ls: 无法访问 '/path': 没有那个文件或目录'")
    print("   - 权限不足: 'ls: 无法访问 '/path': 权限不够'")
    print("   - 网络问题: SSH连接中断")
    
    # 5. 检查字符串处理
    print("5. 字符串处理检查:")
    sample_error = "ls: 无法访问 '/home/<USER>/yf_2113_repack': 没有那个文件或目录\n"
    print(f"   - 原始错误: {repr(sample_error)}")
    print(f"   - 去除空白: {repr(sample_error.strip())}")
    print(f"   - 包含目录不存在: {'没有那个文件或目录' in sample_error}")
    
    print()

def debug_connection_flow():
    """调试连接流程"""
    print("=== SSH连接流程调试 ===")
    
    print("1. SSH面板连接成功后的流程:")
    print("   a. _on_connect_success() 被调用")
    print("   b. connection_callback(True, ssh_manager) 被调用")
    print("   c. 文件管理器的 on_connection_changed() 被调用")
    print("   d. 重新加载配置中的默认远程目录")
    print("   e. 调用 refresh_remote_files()")
    print("   f. 在后台线程中执行 _refresh_remote_files_thread()")
    
    print("\n2. 可能的问题点:")
    print("   - 配置加载时机不对")
    print("   - 远程路径变量没有正确更新")
    print("   - ls命令执行失败")
    print("   - 输出解析错误")
    print("   - UI更新时机问题")
    
    print()

def debug_ui_update():
    """调试UI更新"""
    print("=== UI更新调试 ===")
    
    print("1. 远程文件树更新流程:")
    print("   a. 清空现有项目: remote_tree.delete(*remote_tree.get_children())")
    print("   b. 执行ls命令")
    print("   c. 解析输出")
    print("   d. 调用 _update_remote_tree(items)")
    print("   e. 在主线程中更新UI")
    
    print("\n2. 可能的UI问题:")
    print("   - after(0, lambda: ...) 调用时机")
    print("   - 线程间通信问题")
    print("   - UI组件状态不一致")
    
    print()

if __name__ == "__main__":
    print("开始远程目录显示问题调试...")
    print()
    
    debug_file_manager_logic()
    debug_connection_flow()
    debug_ui_update()
    
    print("调试完成！")
    print("\n建议的排查步骤:")
    print("1. 检查SSH连接是否真正成功")
    print("2. 检查默认远程目录是否存在")
    print("3. 手动执行ls命令验证")
    print("4. 检查日志输出")
    print("5. 添加更多调试信息")
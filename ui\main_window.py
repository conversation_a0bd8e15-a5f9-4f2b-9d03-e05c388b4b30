#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口类 - 重构后的模块化版本
"""

import tkinter as tk
from tkinter import ttk, messagebox
import logging
from typing import Optional

from core.config_manager import config
from core.performance_monitor import performance_monitor
from ui.ssh_panel import SSHPanel
from ui.file_manager import FileManagerPanel
from ui.command_panel import CommandPanel
# from ui.status_bar import StatusBar  # 暂时注释，使用简单状态栏
from ui.theme_manager import theme_manager

logger = logging.getLogger(__name__)

class MainWindow:
    """主窗口类"""
    
    def __init__(self, root: tk.Tk):
        self.root = root
        
        # 初始化组件引用
        self.ssh_panel: Optional[SSHPanel] = None
        self.file_manager: Optional[FileManagerPanel] = None
        self.command_panel: Optional[CommandPanel] = None
        self.status_bar = None
        
        self.setup_window()
        self.create_widgets()
        self.setup_bindings()
        
        logger.info("主窗口初始化完成")
    
    def setup_window(self):
        """设置窗口属性"""
        # 从配置获取窗口设置
        ui_config = config.get_ui_config()
        
        self.root.title("宇泛升级包拆包打包工具 v2.0")
        
        # 设置窗口大小和位置
        width = ui_config.get("window_width", 1200)
        height = ui_config.get("window_height", 800)
        x = ui_config.get("window_x", 100)
        y = ui_config.get("window_y", 100)
        
        self.root.geometry(f"{width}x{height}+{x}+{y}")
        self.root.minsize(800, 600)
        
        # 初始化并应用主题
        theme_manager.initialize(self.root)
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建顶部面板（SSH连接）
        self.ssh_panel = SSHPanel(main_frame)
        self.ssh_panel.pack(fill=tk.X, pady=(0, 5))
        
        # 创建中间面板（使用PanedWindow分割）
        paned_window = ttk.PanedWindow(main_frame, orient=tk.VERTICAL)
        paned_window.pack(fill=tk.BOTH, expand=True)
        
        # 命令执行面板
        self.command_panel = CommandPanel(paned_window)
        paned_window.add(self.command_panel, weight=1)
        
        # 文件管理面板
        self.file_manager = FileManagerPanel(paned_window)
        paned_window.add(self.file_manager, weight=2)
        
        # 创建简单状态栏
        self.status_bar = ttk.Label(main_frame, text="就绪", relief=tk.SUNKEN, borderwidth=1)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)
        
        # 设置组件间的通信
        self.setup_component_communication()
    
    def setup_component_communication(self):
        """设置组件间的通信"""
        logger.info("开始设置组件间通信")
        logger.info(f"SSH面板: {self.ssh_panel is not None}")
        logger.info(f"命令面板: {self.command_panel is not None}")
        logger.info(f"文件管理器: {self.file_manager is not None}")
        
        if self.ssh_panel:
            # SSH连接状态变化时通知所有相关组件
            def on_connection_changed(connected, ssh_manager):
                """统一的连接状态变化处理"""
                logger.info(f"主窗口收到连接状态变化: connected={connected}")
                
                if self.command_panel:
                    logger.info("通知命令面板连接状态变化")
                    self.command_panel.on_connection_changed(connected, ssh_manager)
                else:
                    logger.warning("命令面板为None，无法通知")
                
                if self.file_manager:
                    logger.info("通知文件管理器连接状态变化")
                    self.file_manager.on_connection_changed(connected, ssh_manager)
                else:
                    logger.warning("文件管理器为None，无法通知")
            
            self.ssh_panel.set_connection_callback(on_connection_changed)
            logger.info("SSH面板连接回调已设置")
        else:
            logger.warning("SSH面板为None，无法设置回调")
        
        # 暂时注释状态栏回调
        # if self.status_bar:
        #     if self.ssh_panel:
        #         self.ssh_panel.set_status_callback(self.status_bar.update_status)
        #     if self.command_panel:
        #         self.command_panel.set_status_callback(self.status_bar.update_status)
        #     if self.file_manager:
        #         self.file_manager.set_status_callback(self.status_bar.update_status)
    
    def setup_bindings(self):
        """设置事件绑定"""
        # 窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 窗口大小变化事件
        self.root.bind("<Configure>", self.on_window_configure)
        
        # 性能监控警报回调
        performance_monitor.add_alert_callback(self.on_performance_alert)
    
    def on_closing(self):
        """优化的窗口关闭事件处理"""
        from core.fast_shutdown_manager import fast_shutdown_manager
        import time
        
        start_time = time.time()
        logger.info("开始程序关闭流程")
        
        try:
            # 注册清理任务到快速关闭管理器
            self._register_shutdown_tasks()
            
            # 启动快速关闭流程
            metrics = fast_shutdown_manager.initiate_shutdown()
            
            # 记录关闭性能
            total_time = time.time() - start_time
            logger.info(f"程序关闭完成，总耗时: {total_time:.3f}秒")
            
            if total_time > 0.5:
                logger.warning(f"关闭时间超过500ms: {total_time:.3f}秒")
            
            # 关闭窗口
            self.root.destroy()
            
        except Exception as e:
            logger.error(f"关闭窗口时发生错误: {e}")
            # 强制关闭
            fast_shutdown_manager.force_shutdown()
    
    def _register_shutdown_tasks(self):
        """注册关闭任务到快速关闭管理器"""
        from core.fast_shutdown_manager import fast_shutdown_manager
        
        # 清空之前的任务
        fast_shutdown_manager.clear_cleanup_tasks()
        
        # 注册配置保存任务（最高优先级）
        fast_shutdown_manager.register_cleanup_task(
            name="save_window_config",
            task=self._safe_save_config,
            timeout=0.2,
            priority=0
        )
        
        # 注册SSH面板清理任务
        if self.ssh_panel:
            fast_shutdown_manager.register_cleanup_task(
                name="ssh_panel_cleanup",
                task=self.ssh_panel.cleanup,
                timeout=0.5,
                priority=1
            )
        
        # 注册文件管理器清理任务
        if self.file_manager:
            fast_shutdown_manager.register_cleanup_task(
                name="file_manager_cleanup",
                task=self.file_manager.cleanup,
                timeout=0.5,
                priority=2
            )
        
        # 注册命令面板清理任务
        if self.command_panel:
            fast_shutdown_manager.register_cleanup_task(
                name="command_panel_cleanup",
                task=self.command_panel.cleanup,
                timeout=0.3,
                priority=3
            )
        
        # 注册性能监控停止任务
        fast_shutdown_manager.register_cleanup_task(
            name="performance_monitor_stop",
            task=self._stop_performance_monitor,
            timeout=0.3,
            priority=4
        )
    
    def _safe_save_config(self):
        """安全保存配置"""
        try:
            self.save_window_config()
            return True
        except Exception as e:
            logger.warning(f"保存配置失败，跳过: {e}")
            return False
    
    def _stop_performance_monitor(self):
        """停止性能监控"""
        try:
            performance_monitor.stop_monitoring(timeout=0.2)
            return True
        except Exception as e:
            logger.warning(f"停止性能监控失败: {e}")
            return False
    
    def on_window_configure(self, event):
        """窗口配置变化事件处理"""
        if event.widget == self.root:
            # 更新窗口配置
            config.set_ui_config(
                window_width=self.root.winfo_width(),
                window_height=self.root.winfo_height(),
                window_x=self.root.winfo_x(),
                window_y=self.root.winfo_y()
            )
    
    def on_performance_alert(self, alert_message: str, metrics):
        """性能警报处理"""
        logger.warning(f"性能警报: {alert_message}")
        if self.status_bar:
            self.status_bar.config(text=f"性能警报: {alert_message}")
    
    def save_window_config(self):
        """保存窗口配置"""
        try:
            config.set_ui_config(
                window_width=self.root.winfo_width(),
                window_height=self.root.winfo_height(),
                window_x=self.root.winfo_x(),
                window_y=self.root.winfo_y()
            )
            config.save_config()
            logger.info("窗口配置已保存")
        except Exception as e:
            logger.error(f"保存窗口配置失败: {e}")
    
    def cleanup(self):
        """传统的资源清理方法（保留兼容性）"""
        try:
            # 清理SSH连接
            if self.ssh_panel:
                self.ssh_panel.cleanup()
            
            # 清理文件传输
            if self.file_manager:
                self.file_manager.cleanup()
            
            # 清理命令执行
            if self.command_panel:
                self.command_panel.cleanup()
            
            logger.info("资源清理完成")
            
        except Exception as e:
            logger.error(f"资源清理失败: {e}")
    
    def force_shutdown(self):
        """强制关闭程序"""
        logger.warning("触发强制关闭")
        
        try:
            # 尝试快速清理关键资源
            if self.ssh_panel:
                try:
                    self.ssh_panel.force_cleanup()
                except:
                    pass
            
            # 强制关闭窗口
            self.root.quit()
            self.root.destroy()
            
        except Exception as e:
            logger.error(f"强制关闭异常: {e}")
        finally:
            # 最后的手段
            import os
            os._exit(1)
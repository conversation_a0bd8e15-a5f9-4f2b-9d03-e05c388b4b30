#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CPU使用率优化器
优化界面更新频率和监控采样间隔，降低CPU占用
"""

import time
import threading
import logging
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass
import psutil

logger = logging.getLogger(__name__)

@dataclass
class CPUOptimizationConfig:
    """CPU优化配置"""
    idle_cpu_threshold: float = 5.0        # 空闲CPU阈值
    high_cpu_threshold: float = 30.0       # 高CPU使用率阈值
    base_update_interval: float = 0.1      # 基础更新间隔
    adaptive_max_interval: float = 1.0     # 自适应最大间隔
    monitoring_interval: float = 5.0       # 监控间隔

class CPUOptimizer:
    """CPU使用率优化器
    
    监控CPU使用率，自适应调整更新频率和采样间隔
    """
    
    def __init__(self, config: CPUOptimizationConfig = None):
        """初始化CPU优化器
        
        Args:
            config: CPU优化配置
        """
        self.config = config or CPUOptimizationConfig()
        self.running = False
        self.monitor_thread: Optional[threading.Thread] = None
        
        # 当前优化状态
        self.current_cpu_usage = 0.0
        self.optimization_level = 0  # 0: 正常, 1: 轻度优化, 2: 重度优化
        
        # 自适应间隔
        self.current_update_interval = self.config.base_update_interval
        self.current_monitoring_interval = 1.0
        
        # 优化回调
        self.optimization_callbacks: Dict[str, Callable] = {}
        
        logger.info("CPU优化器初始化完成")
    
    def start_optimization(self):
        """开始CPU优化"""
        if self.running:
            return
        
        self.running = True
        self.monitor_thread = threading.Thread(
            target=self._optimization_loop,
            name="CPUOptimizer",
            daemon=True
        )
        self.monitor_thread.start()
        
        logger.info("CPU优化已启动")
    
    def stop_optimization(self):
        """停止CPU优化"""
        self.running = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2)
        logger.info("CPU优化已停止")
    
    def _optimization_loop(self):
        """CPU优化循环"""
        while self.running:
            try:
                # 获取当前CPU使用率
                self.current_cpu_usage = self._get_cpu_usage()
                
                # 根据CPU使用率调整优化级别
                old_level = self.optimization_level
                self._adjust_optimization_level()
                
                # 如果优化级别改变，应用新的优化策略
                if old_level != self.optimization_level:
                    self._apply_optimization()
                
                # 动态调整监控间隔
                self._adjust_monitoring_interval()
                
                time.sleep(self.current_monitoring_interval)
                
            except Exception as e:
                logger.error(
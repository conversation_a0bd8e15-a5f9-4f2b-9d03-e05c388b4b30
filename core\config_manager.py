#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
用于管理应用程序的配置信息，包括SSH连接信息、界面设置等
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.json"):
        """初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = Path(config_file)
        self.config_data = {}
        self.default_config = {
            "ssh": {
                "host": "************",
                "port": 22,
                "username": "maojunyi",
                "password": "",  # 不保存密码
                "remember_password": False,
                "default_remote_path": "/home/<USER>/yf_2113_repack"
            },
            "ui": {
                "window_width": 1000,
                "window_height": 700,
                "window_x": 100,
                "window_y": 100,
                "theme": "default",
                "font_size": 9
            },
            "paths": {
                "default_local_path": "",
                "last_download_path": "",
                "last_upload_path": ""
            },
            "logging": {
                "level": "INFO",
                "max_log_files": 5,
                "max_log_size_mb": 10
            }
        }
        self.load_config()
    
    def load_config(self) -> None:
        """加载配置文件"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config_data = json.load(f)
                logger.info(f"配置文件加载成功: {self.config_file}")
            else:
                logger.info("配置文件不存在，使用默认配置")
                self.config_data = self.default_config.copy()
                self.save_config()
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            self.config_data = self.default_config.copy()
    
    def save_config(self, timeout: float = None) -> bool:
        """保存配置文件
        
        Args:
            timeout: 保存超时时间（秒），None表示无超时
        
        Returns:
            bool: 保存是否成功
        """
        if timeout is None:
            return self._save_config_sync()
        else:
            return self._save_config_with_timeout(timeout)
    
    def _save_config_sync(self) -> bool:
        """同步保存配置文件"""
        try:
            # 确保目录存在
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, indent=2, ensure_ascii=False)
            logger.info(f"配置文件保存成功: {self.config_file}")
            return True
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
            return False
    
    def _save_config_with_timeout(self, timeout: float) -> bool:
        """带超时的配置保存
        
        Args:
            timeout: 超时时间（秒）
            
        Returns:
            bool: 保存是否成功
        """
        import threading
        import time
        
        result_container = [False]
        exception_container = [None]
        
        def save_worker():
            try:
                result_container[0] = self._save_config_sync()
            except Exception as e:
                exception_container[0] = e
        
        # 在单独线程中执行保存操作
        save_thread = threading.Thread(target=save_worker, daemon=True)
        save_thread.start()
        save_thread.join(timeout=timeout)
        
        if save_thread.is_alive():
            logger.warning(f"配置保存超时 ({timeout}秒)，跳过保存")
            return False
        
        if exception_container[0]:
            logger.error(f"配置保存异常: {exception_container[0]}")
            return False
        
        return result_container[0]
    
    def safe_save_config(self, timeout: float = 0.5) -> bool:
        """安全保存配置（用于程序关闭时）
        
        Args:
            timeout: 超时时间（秒）
            
        Returns:
            bool: 保存是否成功
        """
        try:
            logger.info(f"开始安全保存配置，超时: {timeout}秒")
            success = self.save_config(timeout=timeout)
            
            if success:
                logger.info("配置安全保存成功")
            else:
                logger.warning("配置安全保存失败或超时")
            
            return success
            
        except Exception as e:
            logger.error(f"安全保存配置异常: {e}")
            return False
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """获取配置值
        
        Args:
            key_path: 配置键路径，如 "ssh.host"
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key_path.split('.')
        value = self.config_data
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value: Any) -> None:
        """设置配置值
        
        Args:
            key_path: 配置键路径，如 "ssh.host"
            value: 配置值
        """
        keys = key_path.split('.')
        config = self.config_data
        
        # 确保路径存在
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        config[keys[-1]] = value
    
    def get_ssh_config(self) -> Dict[str, Any]:
        """获取SSH配置"""
        return self.get("ssh", {})
    
    def set_ssh_config(self, **kwargs) -> None:
        """设置SSH配置"""
        for key, value in kwargs.items():
            self.set(f"ssh.{key}", value)
    
    def get_ui_config(self) -> Dict[str, Any]:
        """获取UI配置"""
        return self.get("ui", {})
    
    def set_ui_config(self, **kwargs) -> None:
        """设置UI配置"""
        for key, value in kwargs.items():
            self.set(f"ui.{key}", value)
    
    def reset_to_default(self) -> None:
        """重置为默认配置"""
        self.config_data = self.default_config.copy()
        logger.info("配置已重置为默认值")
    
    def create_backup(self) -> bool:
        """创建配置备份
        
        Returns:
            bool: 备份是否成功
        """
        try:
            backup_file = self.config_file.with_suffix('.backup.json')
            
            if self.config_file.exists():
                import shutil
                shutil.copy2(self.config_file, backup_file)
                logger.info(f"配置备份创建成功: {backup_file}")
                return True
            else:
                logger.warning("原配置文件不存在，无法创建备份")
                return False
                
        except Exception as e:
            logger.error(f"创建配置备份失败: {e}")
            return False
    
    def restore_from_backup(self) -> bool:
        """从备份恢复配置
        
        Returns:
            bool: 恢复是否成功
        """
        try:
            backup_file = self.config_file.with_suffix('.backup.json')
            
            if backup_file.exists():
                import shutil
                shutil.copy2(backup_file, self.config_file)
                self.load_config()  # 重新加载配置
                logger.info(f"配置从备份恢复成功: {backup_file}")
                return True
            else:
                logger.warning("备份文件不存在，无法恢复")
                return False
                
        except Exception as e:
            logger.error(f"从备份恢复配置失败: {e}")
            return False
    
    def get_critical_config(self) -> Dict[str, Any]:
        """获取关键配置（用于紧急保存）
        
        Returns:
            Dict[str, Any]: 关键配置数据
        """
        critical_keys = [
            "ssh.host", "ssh.port", "ssh.username",
            "ui.window_width", "ui.window_height", 
            "ui.window_x", "ui.window_y"
        ]
        
        critical_config = {}
        for key in critical_keys:
            value = self.get(key)
            if value is not None:
                critical_config[key] = value
        
        return critical_config
    
    def emergency_save(self) -> bool:
        """紧急保存关键配置
        
        Returns:
            bool: 保存是否成功
        """
        try:
            critical_config = self.get_critical_config()
            emergency_file = self.config_file.with_suffix('.emergency.json')
            
            with open(emergency_file, 'w', encoding='utf-8') as f:
                json.dump(critical_config, f, indent=2, ensure_ascii=False)
            
            logger.info(f"紧急配置保存成功: {emergency_file}")
            return True
            
        except Exception as e:
            logger.error(f"紧急配置保存失败: {e}")
            return False

# 全局配置实例
config = ConfigManager()

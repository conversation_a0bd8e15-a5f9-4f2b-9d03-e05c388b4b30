#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速关闭管理器测试脚本
"""

import time
import logging
from core.fast_shutdown_manager import FastShutdownManager, ShutdownPhase

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_cleanup_task_1():
    """测试清理任务1"""
    print("执行清理任务1...")
    time.sleep(0.1)  # 模拟清理工作
    return True

def test_cleanup_task_2():
    """测试清理任务2"""
    print("执行清理任务2...")
    time.sleep(0.05)  # 模拟清理工作
    return True

def test_slow_cleanup_task():
    """测试慢速清理任务（会超时）"""
    print("执行慢速清理任务...")
    time.sleep(2.0)  # 故意超时
    return True

def main():
    """测试主函数"""
    print("=== 快速关闭管理器测试 ===")
    
    # 创建关闭管理器
    shutdown_manager = FastShutdownManager(shutdown_timeout=3.0)
    
    # 注册清理任务
    shutdown_manager.register_cleanup_task("task1", test_cleanup_task_1, timeout=0.5, priority=1)
    shutdown_manager.register_cleanup_task("task2", test_cleanup_task_2, timeout=0.5, priority=2)
    shutdown_manager.register_cleanup_task("slow_task", test_slow_cleanup_task, timeout=0.3, priority=3)
    
    print(f"注册了 {len(shutdown_manager.cleanup_tasks)} 个清理任务")
    
    # 测试关闭流程
    print("\n开始关闭流程...")
    start_time = time.time()
    
    try:
        metrics = shutdown_manager.initiate_shutdown()
        
        print(f"\n=== 关闭完成 ===")
        print(f"总耗时: {metrics.total_shutdown_time:.3f}秒")
        print(f"配置保存耗时: {metrics.config_save_time:.3f}秒")
        print(f"资源清理耗时: {metrics.resource_cleanup_time:.3f}秒")
        print(f"线程停止耗时: {metrics.thread_stop_time:.3f}秒")
        print(f"是否强制关闭: {metrics.forced_shutdown}")
        print(f"失败的操作: {metrics.failed_operations}")
        
        # 检查状态
        status = shutdown_manager.get_status()
        print(f"\n=== 关闭状态 ===")
        print(f"当前阶段: {status.current_phase}")
        print(f"完成的任务: {status.completed_tasks}")
        print(f"失败的任务: {status.failed_tasks}")
        
        # 验证关闭时间是否在预期范围内
        if metrics.total_shutdown_time <= 2.0:
            print("\n✅ 关闭时间测试通过")
        else:
            print(f"\n❌ 关闭时间测试失败: {metrics.total_shutdown_time:.3f}秒 > 2.0秒")
            
    except Exception as e:
        print(f"测试异常: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 快速关闭管理器测试完成")
    else:
        print("\n💥 快速关闭管理器测试失败")
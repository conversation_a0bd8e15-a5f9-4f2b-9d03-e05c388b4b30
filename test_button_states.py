#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试按钮状态问题修复
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_ssh_panel_button_states():
    """测试SSH面板按钮状态"""
    print("=== SSH面板按钮状态测试 ===")
    
    print("1. 初始状态（未连接）:")
    print("   - 连接按钮: 应该可用 (NORMAL)")
    print("   - 断开连接按钮: 应该禁用 (DISABLED)")
    
    print("\n2. 连接成功后:")
    print("   - 连接按钮: 应该禁用 (DISABLED)")
    print("   - 断开连接按钮: 应该可用 (NORMAL)")
    
    print("\n3. 断开连接后:")
    print("   - 连接按钮: 应该可用 (NORMAL)")
    print("   - 断开连接按钮: 应该禁用 (DISABLED)")
    
    print()

def test_command_panel_button_states():
    """测试命令面板按钮状态"""
    print("=== 命令面板按钮状态测试 ===")
    
    print("1. 初始状态（未连接）:")
    print("   - 执行按钮: 应该禁用 (DISABLED)")
    print("   - 拆包按钮: 应该禁用 (DISABLED)")
    print("   - 打包按钮: 应该禁用 (DISABLED)")
    print("   - 清理按钮: 应该禁用 (DISABLED)")
    
    print("\n2. 连接成功后:")
    print("   - 执行按钮: 应该可用 (NORMAL)")
    print("   - 拆包按钮: 应该可用 (NORMAL)")
    print("   - 打包按钮: 应该可用 (NORMAL)")
    print("   - 清理按钮: 应该可用 (NORMAL)")
    
    print("\n3. 断开连接后:")
    print("   - 执行按钮: 应该禁用 (DISABLED)")
    print("   - 拆包按钮: 应该禁用 (DISABLED)")
    print("   - 打包按钮: 应该禁用 (DISABLED)")
    print("   - 清理按钮: 应该禁用 (DISABLED)")
    
    print()

def test_connection_flow():
    """测试连接流程"""
    print("=== 连接流程测试 ===")
    
    print("1. SSH面板连接流程:")
    print("   a. 用户点击连接按钮")
    print("   b. 验证输入信息")
    print("   c. 显示进度条")
    print("   d. 在后台线程中连接")
    print("   e. 连接成功后调用 _on_connect_success()")
    print("   f. 更新按钮状态")
    print("   g. 调用 connection_callback(True, ssh_manager)")
    
    print("\n2. 命令面板响应连接:")
    print("   a. on_connection_changed(True, ssh_manager) 被调用")
    print("   b. 更新 is_connected = True")
    print("   c. 更新 ssh_manager 引用")
    print("   d. 更新所有快捷命令按钮状态为可用")
    
    print("\n3. 可能的问题点:")
    print("   - connection_callback 没有正确设置")
    print("   - 回调函数没有被调用")
    print("   - 按钮状态更新时机不对")
    print("   - 线程间通信问题")
    
    print()

def test_button_state_logic():
    """测试按钮状态逻辑"""
    print("=== 按钮状态逻辑测试 ===")
    
    import tkinter as tk
    
    # 模拟按钮状态
    print("1. tkinter 按钮状态常量:")
    print(f"   - tk.NORMAL = {tk.NORMAL}")
    print(f"   - tk.DISABLED = {tk.DISABLED}")
    
    print("\n2. 状态判断逻辑:")
    connected_states = [True, False]
    for connected in connected_states:
        state = tk.NORMAL if connected else tk.DISABLED
        print(f"   - connected={connected} -> state={state}")
    
    print()

if __name__ == "__main__":
    print("开始按钮状态测试...")
    print()
    
    test_ssh_panel_button_states()
    test_command_panel_button_states()
    test_connection_flow()
    test_button_state_logic()
    
    print("按钮状态测试完成！")
    print("\n修复要点:")
    print("1. 确保初始按钮状态正确")
    print("2. 添加 update_button_states() 方法")
    print("3. 在连接状态变化时调用状态更新")
    print("4. 添加调试日志以便排查问题")
    print("5. 确保回调函数正确设置和调用")
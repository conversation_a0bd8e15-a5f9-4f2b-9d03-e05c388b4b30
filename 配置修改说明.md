# SSH连接配置优化说明

## 修改概述

本次修改优化了SSH连接配置：
1. 去除了默认脚本配置，简化配置结构
2. 将默认远程目录改为 `yf_2113_repack`
3. 保持代码中使用配置的方式不变

## 配置文件变更

### config.json
```json
{
  "ssh": {
    "host": "************",
    "port": 22,
    "username": "maojunyi",
    "password": "",
    "remember_password": true,
    "default_remote_path": "/home/<USER>/yf_2113_repack"
  }
}
```

### 配置项说明

1. **default_remote_path**: 默认远程工作目录
   - 用于文件管理器的初始远程目录
   - 用于命令执行时的工作目录
   - 新值：`/home/<USER>/yf_2113_repack`

## 代码修改详情

### 1. 核心配置管理器 (core/config_manager.py)
- 在默认配置中添加了新的配置项
- 保持向后兼容性，提供合理的默认值

### 2. SSH面板 (ui/ssh_panel.py)
- 添加了默认远程目录输入框
- 移除了脚本配置区域，简化界面
- 在加载和保存配置时处理默认远程目录配置

### 3. 文件管理器 (ui/file_manager.py)
- 修改了远程目录初始化逻辑，从SSH配置中读取默认路径
- 移除了硬编码的远程目录路径

### 4. 命令面板 (ui/command_panel.py)
- 修改了命令执行时的工作目录获取逻辑，使用配置中的默认远程目录
- 快捷命令按钮使用固定的脚本名称（简化配置）
- 移除了硬编码的远程目录路径

## 使用方式

### 通过UI配置
1. 在SSH连接面板中可以直接修改默认远程目录
2. 配置会自动保存到config.json文件

### 通过代码配置
```python
from core.config_manager import config

# 修改默认远程目录
config.set("ssh.default_remote_path", "/new/remote/path")

# 保存配置
config.save_config()
```

## 向后兼容性

- 所有新配置项都有合理的默认值
- 现有的配置文件会自动升级，添加缺失的配置项
- 不会影响现有功能的正常使用

## 测试验证

运行 `python test_config_integration.py` 可以验证配置功能是否正常工作。

## 优势

1. **简洁性**: 移除了复杂的脚本配置，简化了界面和配置结构
2. **可维护性**: 消除了硬编码的远程目录路径，便于维护和修改
3. **用户友好**: 提供了直观的UI界面配置默认远程目录
4. **一致性**: 所有相关功能都使用统一的配置源获取远程目录
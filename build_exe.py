#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyInstaller打包脚本
用于将宇泛升级包工具打包成Windows可执行文件
"""

import os
import sys
import subprocess
from pathlib import Path

def install_pyinstaller():
    """安装PyInstaller"""
    try:
        import PyInstaller
        print("PyInstaller已安装")
        return True
    except ImportError:
        print("正在安装PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("PyInstaller安装成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"PyInstaller安装失败: {e}")
            return False

def build_executable():
    """构建可执行文件"""
    # 获取当前脚本所在目录
    current_dir = Path(__file__).parent.absolute()
    main_py = current_dir / "main.py"
    
    if not main_py.exists():
        print(f"错误: 找不到main.py文件: {main_py}")
        return False
    
    # PyInstaller命令参数
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",  # 打包成单个exe文件
        "--windowed",  # 不显示控制台窗口（GUI应用）
        "--name=YufanUpgradeTool",  # 可执行文件名称
        "--distpath=dist",  # 输出目录
        "--workpath=build",  # 临时文件目录
        "--specpath=.",  # spec文件位置
        "--clean",  # 清理临时文件
        "--noconfirm",  # 不询问覆盖
        str(main_py)  # 主程序文件
    ]
    
    print("开始打包...")
    print(f"命令: {' '.join(cmd)}")
    
    try:
        # 切换到项目目录
        os.chdir(current_dir)
        
        # 执行打包命令
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("\n打包成功！")
            exe_path = current_dir / "dist" / "YufanUpgradeTool.exe"
            if exe_path.exists():
                print(f"可执行文件位置: {exe_path}")
                print(f"文件大小: {exe_path.stat().st_size / (1024*1024):.1f} MB")
            else:
                print("警告: 未找到生成的exe文件")
            return True
        else:
            print("\n打包失败！")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"打包过程中发生错误: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("宇泛升级包工具 - Windows可执行文件打包器")
    print("=" * 50)
    
    # 检查并安装PyInstaller
    if not install_pyinstaller():
        print("无法安装PyInstaller，打包终止")
        return
    
    # 安装项目依赖
    print("\n安装项目依赖...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("依赖安装完成")
    except subprocess.CalledProcessError as e:
        print(f"依赖安装失败: {e}")
        print("继续打包过程...")
    
    # 构建可执行文件
    print("\n开始构建可执行文件...")
    if build_executable():
        print("\n✅ 打包完成！")
        print("\n使用说明:")
        print("1. 可执行文件位于 dist/YufanUpgradeTool.exe")
        print("2. 该文件可以在任何Windows系统上运行，无需安装Python")
        print("3. 首次运行可能需要几秒钟启动时间")
        print("4. 如果Windows Defender报警，请添加信任")
    else:
        print("\n❌ 打包失败！")
        print("\n故障排除建议:")
        print("1. 确保所有依赖都已正确安装")
        print("2. 检查main.py文件是否存在语法错误")
        print("3. 尝试手动运行: python -m PyInstaller --onefile --windowed main.py")

if __name__ == "__main__":
    main()
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速关闭管理器
提供统一的程序关闭流程控制，确保程序能在500ms内快速关闭
"""

import os
import time
import threading
import logging
from typing import Dict, Any, List, Callable, Optional
from dataclasses import dataclass
from enum import Enum
from concurrent.futures import ThreadPoolExecutor, as_completed, TimeoutError

logger = logging.getLogger(__name__)

class ShutdownPhase(Enum):
    """关闭阶段枚举"""
    INITIATED = "initiated"
    CONFIG_SAVING = "config_saving"
    RESOURCE_CLEANUP = "resource_cleanup"
    THREAD_STOPPING = "thread_stopping"
    COMPLETED = "completed"
    FORCE_SHUTDOWN = "force_shutdown"

@dataclass
class ShutdownStatus:
    """关闭状态信息"""
    start_time: float
    current_phase: ShutdownPhase
    completed_tasks: List[str]
    failed_tasks: List[str]
    force_shutdown: bool = False

@dataclass
class ShutdownMetrics:
    """关闭性能指标"""
    total_shutdown_time: float
    config_save_time: float
    resource_cleanup_time: float
    thread_stop_time: float
    forced_shutdown: bool
    failed_operations: List[str]

@dataclass
class CleanupTask:
    """清理任务"""
    name: str
    func: Callable
    timeout: float
    priority: int = 0  # 优先级，数字越小优先级越高

class FastShutdownManager:
    """快速关闭管理器
    
    负责协调整个程序的快速关闭流程，确保在500ms内完成关闭
    """
    
    def __init__(self, shutdown_timeout: float = 2.0):
        """初始化快速关闭管理器
        
        Args:
            shutdown_timeout: 最大关闭时间（秒）
        """
        self.shutdown_timeout = shutdown_timeout
        self.shutdown_event = threading.Event()
        self.force_shutdown_event = threading.Event()
        
        # 清理任务列表
        self.cleanup_tasks: List[CleanupTask] = []
        self._cleanup_lock = threading.Lock()
        
        # 关闭状态
        self.status = ShutdownStatus(
            start_time=0,
            current_phase=ShutdownPhase.INITIATED,
            completed_tasks=[],
            failed_tasks=[]
        )
        
        # 强制关闭定时器
        self._force_shutdown_timer: Optional[threading.Timer] = None
        
        # 线程池用于并行执行清理任务
        self._cleanup_executor: Optional[ThreadPoolExecutor] = None
        
        logger.info(f"快速关闭管理器初始化完成，超时时间: {shutdown_timeout}秒")
    
    def register_cleanup_task(self, 
                            name: str, 
                            task: Callable, 
                            timeout: float = 1.0, 
                            priority: int = 0):
        """注册清理任务
        
        Args:
            name: 任务名称
            task: 清理任务函数
            timeout: 任务超时时间（秒）
            priority: 优先级，数字越小优先级越高
        """
        with self._cleanup_lock:
            cleanup_task = CleanupTask(
                name=name,
                func=task,
                timeout=timeout,
                priority=priority
            )
            self.cleanup_tasks.append(cleanup_task)
            # 按优先级排序
            self.cleanup_tasks.sort(key=lambda x: x.priority)
            
        logger.info(f"注册清理任务: {name} (超时: {timeout}s, 优先级: {priority})")
    
    def initiate_shutdown(self) -> ShutdownMetrics:
        """启动关闭流程
        
        Returns:
            ShutdownMetrics: 关闭性能指标
        """
        start_time = time.time()
        self.status.start_time = start_time
        self.status.current_phase = ShutdownPhase.INITIATED
        
        logger.info("开始程序关闭流程")
        
        # 设置关闭事件
        self.shutdown_event.set()
        
        # 启动强制关闭定时器
        self._start_force_shutdown_timer()
        
        try:
            # 执行关闭流程
            config_save_time = self._save_configuration()
            resource_cleanup_time = self._cleanup_resources()
            thread_stop_time = self._stop_threads()
            
            # 标记完成
            self.status.current_phase = ShutdownPhase.COMPLETED
            
            # 取消强制关闭定时器
            self._cancel_force_shutdown_timer()
            
            total_time = time.time() - start_time
            
            metrics = ShutdownMetrics(
                total_shutdown_time=total_time,
                config_save_time=config_save_time,
                resource_cleanup_time=resource_cleanup_time,
                thread_stop_time=thread_stop_time,
                forced_shutdown=self.status.force_shutdown,
                failed_operations=self.status.failed_tasks.copy()
            )
            
            logger.info(f"程序关闭完成，总耗时: {total_time:.3f}秒")
            
            # 记录关闭指标
            self._record_shutdown_metrics(metrics, "normal")
            
            return metrics
            
        except Exception as e:
            logger.error(f"关闭流程异常: {e}")
            self.force_shutdown()
            
            metrics = ShutdownMetrics(
                total_shutdown_time=time.time() - start_time,
                config_save_time=0,
                resource_cleanup_time=0,
                thread_stop_time=0,
                forced_shutdown=True,
                failed_operations=["shutdown_exception"]
            )
            
            # 记录异常关闭指标
            self._record_shutdown_metrics(metrics, "exception")
            
            return metrics
    
    def _save_configuration(self) -> float:
        """保存配置信息
        
        Returns:
            float: 配置保存耗时
        """
        start_time = time.time()
        self.status.current_phase = ShutdownPhase.CONFIG_SAVING
        
        try:
            # 这里可以添加具体的配置保存逻辑
            # 目前只是一个占位符，实际实现时需要调用具体的配置管理器
            logger.info("保存配置信息...")
            
            # 模拟配置保存，实际实现时替换为真实逻辑
            time.sleep(0.01)  # 模拟少量I/O时间
            
            self.status.completed_tasks.append("config_save")
            save_time = time.time() - start_time
            logger.info(f"配置保存完成，耗时: {save_time:.3f}秒")
            return save_time
            
        except Exception as e:
            logger.warning(f"配置保存失败: {e}")
            self.status.failed_tasks.append("config_save")
            return time.time() - start_time
    
    def _cleanup_resources(self) -> float:
        """清理资源
        
        Returns:
            float: 资源清理耗时
        """
        start_time = time.time()
        self.status.current_phase = ShutdownPhase.RESOURCE_CLEANUP
        
        logger.info("开始资源清理...")
        
        # 创建线程池用于并行执行清理任务
        max_workers = min(4, len(self.cleanup_tasks)) if self.cleanup_tasks else 1
        self._cleanup_executor = ThreadPoolExecutor(max_workers=max_workers)
        
        try:
            # 提交所有清理任务
            future_to_task = {}
            with self._cleanup_lock:
                for task in self.cleanup_tasks:
                    if self.force_shutdown_event.is_set():
                        break
                    future = self._cleanup_executor.submit(self._execute_cleanup_task, task)
                    future_to_task[future] = task
            
            # 等待所有任务完成，但有总体超时限制
            cleanup_timeout = min(1.5, self.shutdown_timeout - (time.time() - self.status.start_time))
            
            for future in as_completed(future_to_task, timeout=cleanup_timeout):
                task = future_to_task[future]
                try:
                    success = future.result()
                    if success:
                        self.status.completed_tasks.append(f"cleanup_{task.name}")
                    else:
                        self.status.failed_tasks.append(f"cleanup_{task.name}")
                except Exception as e:
                    logger.error(f"清理任务 {task.name} 异常: {e}")
                    self.status.failed_tasks.append(f"cleanup_{task.name}")
            
        except TimeoutError:
            logger.warning("资源清理超时，强制继续")
        except Exception as e:
            logger.error(f"资源清理异常: {e}")
        finally:
            # 关闭线程池
            if self._cleanup_executor:
                self._cleanup_executor.shutdown(wait=False)
        
        cleanup_time = time.time() - start_time
        logger.info(f"资源清理完成，耗时: {cleanup_time:.3f}秒")
        return cleanup_time
    
    def _execute_cleanup_task(self, task: CleanupTask) -> bool:
        """执行单个清理任务
        
        Args:
            task: 清理任务
            
        Returns:
            bool: 是否成功
        """
        try:
            logger.debug(f"执行清理任务: {task.name}")
            
            # 使用线程执行任务以支持超时控制
            result_container = [None]
            exception_container = [None]
            
            def task_wrapper():
                try:
                    result_container[0] = task.func()
                except Exception as e:
                    exception_container[0] = e
            
            task_thread = threading.Thread(target=task_wrapper, daemon=True)
            task_thread.start()
            task_thread.join(timeout=task.timeout)
            
            if task_thread.is_alive():
                logger.warning(f"清理任务 {task.name} 超时")
                return False
            
            if exception_container[0]:
                raise exception_container[0]
            
            logger.debug(f"清理任务 {task.name} 完成")
            return True
            
        except Exception as e:
            logger.error(f"清理任务 {task.name} 失败: {e}")
            return False
    
    def _stop_threads(self) -> float:
        """停止线程
        
        Returns:
            float: 线程停止耗时
        """
        start_time = time.time()
        self.status.current_phase = ShutdownPhase.THREAD_STOPPING
        
        logger.info("停止后台线程...")
        
        # 这里可以添加具体的线程停止逻辑
        # 实际实现时需要与线程管理器集成
        
        self.status.completed_tasks.append("thread_stop")
        stop_time = time.time() - start_time
        logger.info(f"线程停止完成，耗时: {stop_time:.3f}秒")
        return stop_time
    
    def _start_force_shutdown_timer(self):
        """启动强制关闭定时器"""
        self._force_shutdown_timer = threading.Timer(
            self.shutdown_timeout, 
            self.force_shutdown
        )
        self._force_shutdown_timer.start()
        logger.debug(f"强制关闭定时器已启动，{self.shutdown_timeout}秒后触发")
    
    def _cancel_force_shutdown_timer(self):
        """取消强制关闭定时器"""
        if self._force_shutdown_timer:
            self._force_shutdown_timer.cancel()
            self._force_shutdown_timer = None
            logger.debug("强制关闭定时器已取消")
    
    def force_shutdown(self):
        """强制关闭程序"""
        logger.warning("触发强制关闭")
        
        self.status.force_shutdown = True
        self.status.current_phase = ShutdownPhase.FORCE_SHUTDOWN
        self.force_shutdown_event.set()
        
        try:
            # 强制关闭线程池
            if self._cleanup_executor:
                self._cleanup_executor.shutdown(wait=False)
            
            # 记录强制关闭
            logger.critical("程序强制关闭")
            
            # 使用 os._exit() 强制退出程序
            # 注意：这会立即终止程序，不会执行任何清理代码
            os._exit(1)
            
        except Exception as e:
            logger.error(f"强制关闭异常: {e}")
            # 记录强制关闭
            try:
                from .shutdown_metrics_collector import shutdown_metrics_collector
                fake_metrics = ShutdownMetrics(
                    total_shutdown_time=time.time() - self.status.start_time if self.status.start_time else 0,
                    config_save_time=0,
                    resource_cleanup_time=0,
                    thread_stop_time=0,
                    forced_shutdown=True,
                    failed_operations=["force_shutdown"]
                )
                shutdown_metrics_collector.record_shutdown(fake_metrics, "force_shutdown")
            except:
                pass  # 忽略记录异常
            
            # 最后的手段
            os._exit(1)
    
    def is_shutdown_requested(self) -> bool:
        """检查是否请求关闭
        
        Returns:
            bool: 是否请求关闭
        """
        return self.shutdown_event.is_set()
    
    def is_force_shutdown_requested(self) -> bool:
        """检查是否请求强制关闭
        
        Returns:
            bool: 是否请求强制关闭
        """
        return self.force_shutdown_event.is_set()
    
    def get_status(self) -> ShutdownStatus:
        """获取关闭状态
        
        Returns:
            ShutdownStatus: 当前关闭状态
        """
        return self.status
    
    def clear_cleanup_tasks(self):
        """清空清理任务列表"""
        with self._cleanup_lock:
            self.cleanup_tasks.clear()
        logger.info("清理任务列表已清空")
    
    def _record_shutdown_metrics(self, metrics: ShutdownMetrics, trigger: str):
        """记录关闭指标
        
        Args:
            metrics: 关闭指标
            trigger: 触发方式
        """
        try:
            from .shutdown_metrics_collector import shutdown_metrics_collector
            
            shutdown_metrics_collector.record_shutdown(
                metrics=metrics,
                trigger=trigger,
                completed_tasks=self.status.completed_tasks.copy(),
                system_info=None  # 由收集器自动获取
            )
        except Exception as e:
            logger.warning(f"记录关闭指标失败: {e}")

# 全局快速关闭管理器实例
fast_shutdown_manager = FastShutdownManager()